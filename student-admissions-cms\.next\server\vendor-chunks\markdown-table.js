"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/markdown-table";
exports.ids = ["vendor-chunks/markdown-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/markdown-table/index.js":
/*!**********************************************!*\
  !*** ./node_modules/markdown-table/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar repeat = __webpack_require__(/*! repeat-string */ \"(ssr)/./node_modules/repeat-string/index.js\")\n\nmodule.exports = markdownTable\n\nvar trailingWhitespace = / +$/\n\n// Characters.\nvar space = ' '\nvar lineFeed = '\\n'\nvar dash = '-'\nvar colon = ':'\nvar verticalBar = '|'\n\nvar x = 0\nvar C = 67\nvar L = 76\nvar R = 82\nvar c = 99\nvar l = 108\nvar r = 114\n\n// Create a table from a matrix of strings.\nfunction markdownTable(table, options) {\n  var settings = options || {}\n  var padding = settings.padding !== false\n  var start = settings.delimiterStart !== false\n  var end = settings.delimiterEnd !== false\n  var align = (settings.align || []).concat()\n  var alignDelimiters = settings.alignDelimiters !== false\n  var alignments = []\n  var stringLength = settings.stringLength || defaultStringLength\n  var rowIndex = -1\n  var rowLength = table.length\n  var cellMatrix = []\n  var sizeMatrix = []\n  var row = []\n  var sizes = []\n  var longestCellByColumn = []\n  var mostCellsPerRow = 0\n  var cells\n  var columnIndex\n  var columnLength\n  var largest\n  var size\n  var cell\n  var lines\n  var line\n  var before\n  var after\n  var code\n\n  // This is a superfluous loop if we don’t align delimiters, but otherwise we’d\n  // do superfluous work when aligning, so optimize for aligning.\n  while (++rowIndex < rowLength) {\n    cells = table[rowIndex]\n    columnIndex = -1\n    columnLength = cells.length\n    row = []\n    sizes = []\n\n    if (columnLength > mostCellsPerRow) {\n      mostCellsPerRow = columnLength\n    }\n\n    while (++columnIndex < columnLength) {\n      cell = serialize(cells[columnIndex])\n\n      if (alignDelimiters === true) {\n        size = stringLength(cell)\n        sizes[columnIndex] = size\n\n        largest = longestCellByColumn[columnIndex]\n\n        if (largest === undefined || size > largest) {\n          longestCellByColumn[columnIndex] = size\n        }\n      }\n\n      row.push(cell)\n    }\n\n    cellMatrix[rowIndex] = row\n    sizeMatrix[rowIndex] = sizes\n  }\n\n  // Figure out which alignments to use.\n  columnIndex = -1\n  columnLength = mostCellsPerRow\n\n  if (typeof align === 'object' && 'length' in align) {\n    while (++columnIndex < columnLength) {\n      alignments[columnIndex] = toAlignment(align[columnIndex])\n    }\n  } else {\n    code = toAlignment(align)\n\n    while (++columnIndex < columnLength) {\n      alignments[columnIndex] = code\n    }\n  }\n\n  // Inject the alignment row.\n  columnIndex = -1\n  columnLength = mostCellsPerRow\n  row = []\n  sizes = []\n\n  while (++columnIndex < columnLength) {\n    code = alignments[columnIndex]\n    before = ''\n    after = ''\n\n    if (code === l) {\n      before = colon\n    } else if (code === r) {\n      after = colon\n    } else if (code === c) {\n      before = colon\n      after = colon\n    }\n\n    // There *must* be at least one hyphen-minus in each alignment cell.\n    size = alignDelimiters\n      ? Math.max(\n          1,\n          longestCellByColumn[columnIndex] - before.length - after.length\n        )\n      : 1\n\n    cell = before + repeat(dash, size) + after\n\n    if (alignDelimiters === true) {\n      size = before.length + size + after.length\n\n      if (size > longestCellByColumn[columnIndex]) {\n        longestCellByColumn[columnIndex] = size\n      }\n\n      sizes[columnIndex] = size\n    }\n\n    row[columnIndex] = cell\n  }\n\n  // Inject the alignment row.\n  cellMatrix.splice(1, 0, row)\n  sizeMatrix.splice(1, 0, sizes)\n\n  rowIndex = -1\n  rowLength = cellMatrix.length\n  lines = []\n\n  while (++rowIndex < rowLength) {\n    row = cellMatrix[rowIndex]\n    sizes = sizeMatrix[rowIndex]\n    columnIndex = -1\n    columnLength = mostCellsPerRow\n    line = []\n\n    while (++columnIndex < columnLength) {\n      cell = row[columnIndex] || ''\n      before = ''\n      after = ''\n\n      if (alignDelimiters === true) {\n        size = longestCellByColumn[columnIndex] - (sizes[columnIndex] || 0)\n        code = alignments[columnIndex]\n\n        if (code === r) {\n          before = repeat(space, size)\n        } else if (code === c) {\n          if (size % 2 === 0) {\n            before = repeat(space, size / 2)\n            after = before\n          } else {\n            before = repeat(space, size / 2 + 0.5)\n            after = repeat(space, size / 2 - 0.5)\n          }\n        } else {\n          after = repeat(space, size)\n        }\n      }\n\n      if (start === true && columnIndex === 0) {\n        line.push(verticalBar)\n      }\n\n      if (\n        padding === true &&\n        // Don’t add the opening space if we’re not aligning and the cell is\n        // empty: there will be a closing space.\n        !(alignDelimiters === false && cell === '') &&\n        (start === true || columnIndex !== 0)\n      ) {\n        line.push(space)\n      }\n\n      if (alignDelimiters === true) {\n        line.push(before)\n      }\n\n      line.push(cell)\n\n      if (alignDelimiters === true) {\n        line.push(after)\n      }\n\n      if (padding === true) {\n        line.push(space)\n      }\n\n      if (end === true || columnIndex !== columnLength - 1) {\n        line.push(verticalBar)\n      }\n    }\n\n    line = line.join('')\n\n    if (end === false) {\n      line = line.replace(trailingWhitespace, '')\n    }\n\n    lines.push(line)\n  }\n\n  return lines.join(lineFeed)\n}\n\nfunction serialize(value) {\n  return value === null || value === undefined ? '' : String(value)\n}\n\nfunction defaultStringLength(value) {\n  return value.length\n}\n\nfunction toAlignment(value) {\n  var code = typeof value === 'string' ? value.charCodeAt(0) : x\n\n  return code === L || code === l\n    ? l\n    : code === R || code === r\n    ? r\n    : code === C || code === c\n    ? c\n    : x\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/markdown-table/index.js\n");

/***/ })

};
;