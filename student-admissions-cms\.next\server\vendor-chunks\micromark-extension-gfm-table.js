/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-table";
exports.ids = ["vendor-chunks/micromark-extension-gfm-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/index.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./syntax */ \"(ssr)/./node_modules/micromark-extension-gfm-table/syntax.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tdGFibGUvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsb0hBQW9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmstZXh0ZW5zaW9uLWdmbS10YWJsZS9pbmRleC5qcz9lODc3Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9zeW50YXgnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/syntax.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/syntax.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.flow = {\n  null: {tokenize: tokenizeTable, resolve: resolveTable, interruptible: true}\n}\n\nvar createSpace = __webpack_require__(/*! micromark/dist/tokenize/factory-space */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nvar setextUnderlineMini = {tokenize: tokenizeSetextUnderlineMini, partial: true}\nvar nextPrefixedOrBlank = {tokenize: tokenizeNextPrefixedOrBlank, partial: true}\n\nfunction resolveTable(events, context) {\n  var length = events.length\n  var index = -1\n  var token\n  var inHead\n  var inDelimiterRow\n  var inRow\n  var cell\n  var content\n  var text\n  var contentStart\n  var contentEnd\n  var cellStart\n\n  while (++index < length) {\n    token = events[index][1]\n\n    if (inRow) {\n      if (token.type === 'temporaryTableCellContent') {\n        contentStart = contentStart || index\n        contentEnd = index\n      }\n\n      if (\n        // Combine separate content parts into one.\n        (token.type === 'tableCellDivider' || token.type === 'tableRow') &&\n        contentEnd\n      ) {\n        content = {\n          type: 'tableContent',\n          start: events[contentStart][1].start,\n          end: events[contentEnd][1].end\n        }\n        text = {\n          type: 'chunkText',\n          start: content.start,\n          end: content.end,\n          contentType: 'text'\n        }\n\n        events.splice(\n          contentStart,\n          contentEnd - contentStart + 1,\n          ['enter', content, context],\n          ['enter', text, context],\n          ['exit', text, context],\n          ['exit', content, context]\n        )\n        index -= contentEnd - contentStart - 3\n        length = events.length\n        contentStart = undefined\n        contentEnd = undefined\n      }\n    }\n\n    if (\n      events[index][0] === 'exit' &&\n      cellStart &&\n      cellStart + 1 < index &&\n      (token.type === 'tableCellDivider' ||\n        (token.type === 'tableRow' &&\n          (cellStart + 3 < index ||\n            events[cellStart][1].type !== 'whitespace')))\n    ) {\n      cell = {\n        type: inDelimiterRow\n          ? 'tableDelimiter'\n          : inHead\n          ? 'tableHeader'\n          : 'tableData',\n        start: events[cellStart][1].start,\n        end: events[index][1].end\n      }\n      events.splice(index + (token.type === 'tableCellDivider' ? 1 : 0), 0, [\n        'exit',\n        cell,\n        context\n      ])\n      events.splice(cellStart, 0, ['enter', cell, context])\n      index += 2\n      length = events.length\n      cellStart = index + 1\n    }\n\n    if (token.type === 'tableRow') {\n      inRow = events[index][0] === 'enter'\n\n      if (inRow) {\n        cellStart = index + 1\n      }\n    }\n\n    if (token.type === 'tableDelimiterRow') {\n      inDelimiterRow = events[index][0] === 'enter'\n\n      if (inDelimiterRow) {\n        cellStart = index + 1\n      }\n    }\n\n    if (token.type === 'tableHead') {\n      inHead = events[index][0] === 'enter'\n    }\n  }\n\n  return events\n}\n\nfunction tokenizeTable(effects, ok, nok) {\n  var align = []\n  var tableHeaderCount = 0\n  var seenDelimiter\n  var hasDash\n\n  return start\n\n  function start(code) {\n    /* istanbul ignore if - used to be passed in beta micromark versions. */\n    if (code === null || code === -5 || code === -4 || code === -3) {\n      return nok(code)\n    }\n\n    effects.enter('table')._align = align\n    effects.enter('tableHead')\n    effects.enter('tableRow')\n\n    // If we start with a pipe, we open a cell marker.\n    if (code === 124) {\n      return cellDividerHead(code)\n    }\n\n    tableHeaderCount++\n    effects.enter('temporaryTableCellContent')\n    // Can’t be space or eols at the start of a construct, so we’re in a cell.\n    return inCellContentHead(code)\n  }\n\n  function cellDividerHead(code) {\n    // Always a pipe.\n    effects.enter('tableCellDivider')\n    effects.consume(code)\n    effects.exit('tableCellDivider')\n    seenDelimiter = true\n    return cellBreakHead\n  }\n\n  function cellBreakHead(code) {\n    // EOF, CR, LF, CRLF.\n    if (code === null || code === -5 || code === -4 || code === -3) {\n      return atRowEndHead(code)\n    }\n\n    // HT, VS, SP.\n    if (code === -2 || code === -1 || code === 32) {\n      effects.enter('whitespace')\n      effects.consume(code)\n      return inWhitespaceHead\n    }\n\n    if (seenDelimiter) {\n      seenDelimiter = undefined\n      tableHeaderCount++\n    }\n\n    // `|`\n    if (code === 124) {\n      return cellDividerHead(code)\n    }\n\n    // Anything else is cell content.\n    effects.enter('temporaryTableCellContent')\n    return inCellContentHead(code)\n  }\n\n  function inWhitespaceHead(code) {\n    // HT, VS, SP.\n    if (code === -2 || code === -1 || code === 32) {\n      effects.consume(code)\n      return inWhitespaceHead\n    }\n\n    effects.exit('whitespace')\n    return cellBreakHead(code)\n  }\n\n  function inCellContentHead(code) {\n    // EOF, whitespace, pipe\n    if (code === null || code < 0 || code === 32 || code === 124) {\n      effects.exit('temporaryTableCellContent')\n      return cellBreakHead(code)\n    }\n\n    effects.consume(code)\n    // `\\`\n    return code === 92 ? inCellContentEscapeHead : inCellContentHead\n  }\n\n  function inCellContentEscapeHead(code) {\n    // `\\` or `|`\n    if (code === 92 || code === 124) {\n      effects.consume(code)\n      return inCellContentHead\n    }\n\n    // Anything else.\n    return inCellContentHead(code)\n  }\n\n  function atRowEndHead(code) {\n    if (code === null) {\n      return nok(code)\n    }\n\n    effects.exit('tableRow')\n    effects.exit('tableHead')\n\n    // Always a line ending.\n    effects.enter('lineEnding')\n    effects.consume(code)\n    effects.exit('lineEnding')\n\n    // If a setext heading, exit.\n    return effects.check(\n      setextUnderlineMini,\n      nok,\n      // Support an indent before the delimiter row.\n      createSpace(effects, rowStartDelimiter, 'linePrefix', 4)\n    )\n  }\n\n  function rowStartDelimiter(code) {\n    // If there’s another space, or we’re at the EOL/EOF, exit.\n    if (code === null || code < 0 || code === 32) {\n      return nok(code)\n    }\n\n    effects.enter('tableDelimiterRow')\n    return atDelimiterRowBreak(code)\n  }\n\n  function atDelimiterRowBreak(code) {\n    // EOF, CR, LF, CRLF.\n    if (code === null || code === -5 || code === -4 || code === -3) {\n      return rowEndDelimiter(code)\n    }\n\n    // HT, VS, SP.\n    if (code === -2 || code === -1 || code === 32) {\n      effects.enter('whitespace')\n      effects.consume(code)\n      return inWhitespaceDelimiter\n    }\n\n    // `-`\n    if (code === 45) {\n      effects.enter('tableDelimiterFiller')\n      effects.consume(code)\n      hasDash = true\n      align.push(null)\n      return inFillerDelimiter\n    }\n\n    // `:`\n    if (code === 58) {\n      effects.enter('tableDelimiterAlignment')\n      effects.consume(code)\n      effects.exit('tableDelimiterAlignment')\n      align.push('left')\n      return afterLeftAlignment\n    }\n\n    // If we start with a pipe, we open a cell marker.\n    if (code === 124) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return atDelimiterRowBreak\n    }\n\n    return nok(code)\n  }\n\n  function inWhitespaceDelimiter(code) {\n    // HT, VS, SP.\n    if (code === -2 || code === -1 || code === 32) {\n      effects.consume(code)\n      return inWhitespaceDelimiter\n    }\n\n    effects.exit('whitespace')\n    return atDelimiterRowBreak(code)\n  }\n\n  function inFillerDelimiter(code) {\n    // `-`\n    if (code === 45) {\n      effects.consume(code)\n      return inFillerDelimiter\n    }\n\n    effects.exit('tableDelimiterFiller')\n\n    // `:`\n    if (code === 58) {\n      effects.enter('tableDelimiterAlignment')\n      effects.consume(code)\n      effects.exit('tableDelimiterAlignment')\n\n      align[align.length - 1] =\n        align[align.length - 1] === 'left' ? 'center' : 'right'\n\n      return afterRightAlignment\n    }\n\n    return atDelimiterRowBreak(code)\n  }\n\n  function afterLeftAlignment(code) {\n    // `-`\n    if (code === 45) {\n      effects.enter('tableDelimiterFiller')\n      effects.consume(code)\n      hasDash = true\n      return inFillerDelimiter\n    }\n\n    // Anything else is not ok.\n    return nok(code)\n  }\n\n  function afterRightAlignment(code) {\n    // EOF, CR, LF, CRLF.\n    if (code === null || code === -5 || code === -4 || code === -3) {\n      return rowEndDelimiter(code)\n    }\n\n    // HT, VS, SP.\n    if (code === -2 || code === -1 || code === 32) {\n      effects.enter('whitespace')\n      effects.consume(code)\n      return inWhitespaceDelimiter\n    }\n\n    // `|`\n    if (code === 124) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return atDelimiterRowBreak\n    }\n\n    return nok(code)\n  }\n\n  function rowEndDelimiter(code) {\n    effects.exit('tableDelimiterRow')\n\n    // Exit if there was no dash at all, or if the header cell count is not the\n    // delimiter cell count.\n    if (!hasDash || tableHeaderCount !== align.length) {\n      return nok(code)\n    }\n\n    if (code === null) {\n      return tableClose(code)\n    }\n\n    return effects.check(nextPrefixedOrBlank, tableClose, tableContinue)(code)\n  }\n\n  function tableClose(code) {\n    effects.exit('table')\n    return ok(code)\n  }\n\n  function tableContinue(code) {\n    // Always a line ending.\n    effects.enter('lineEnding')\n    effects.consume(code)\n    effects.exit('lineEnding')\n    // We checked that it’s not a prefixed or blank line, so we’re certain a\n    // body is coming, though it may be indented.\n    return createSpace(effects, bodyStart, 'linePrefix', 4)\n  }\n\n  function bodyStart(code) {\n    effects.enter('tableBody')\n    return rowStartBody(code)\n  }\n\n  function rowStartBody(code) {\n    effects.enter('tableRow')\n\n    // If we start with a pipe, we open a cell marker.\n    if (code === 124) {\n      return cellDividerBody(code)\n    }\n\n    effects.enter('temporaryTableCellContent')\n    // Can’t be space or eols at the start of a construct, so we’re in a cell.\n    return inCellContentBody(code)\n  }\n\n  function cellDividerBody(code) {\n    // Always a pipe.\n    effects.enter('tableCellDivider')\n    effects.consume(code)\n    effects.exit('tableCellDivider')\n    return cellBreakBody\n  }\n\n  function cellBreakBody(code) {\n    // EOF, CR, LF, CRLF.\n    if (code === null || code === -5 || code === -4 || code === -3) {\n      return atRowEndBody(code)\n    }\n\n    // HT, VS, SP.\n    if (code === -2 || code === -1 || code === 32) {\n      effects.enter('whitespace')\n      effects.consume(code)\n      return inWhitespaceBody\n    }\n\n    // `|`\n    if (code === 124) {\n      return cellDividerBody(code)\n    }\n\n    // Anything else is cell content.\n    effects.enter('temporaryTableCellContent')\n    return inCellContentBody(code)\n  }\n\n  function inWhitespaceBody(code) {\n    // HT, VS, SP.\n    if (code === -2 || code === -1 || code === 32) {\n      effects.consume(code)\n      return inWhitespaceBody\n    }\n\n    effects.exit('whitespace')\n    return cellBreakBody(code)\n  }\n\n  function inCellContentBody(code) {\n    // EOF, whitespace, pipe\n    if (code === null || code < 0 || code === 32 || code === 124) {\n      effects.exit('temporaryTableCellContent')\n      return cellBreakBody(code)\n    }\n\n    effects.consume(code)\n    // `\\`\n    return code === 92 ? inCellContentEscapeBody : inCellContentBody\n  }\n\n  function inCellContentEscapeBody(code) {\n    // `\\` or `|`\n    if (code === 92 || code === 124) {\n      effects.consume(code)\n      return inCellContentBody\n    }\n\n    // Anything else.\n    return inCellContentBody(code)\n  }\n\n  function atRowEndBody(code) {\n    effects.exit('tableRow')\n\n    if (code === null) {\n      return tableBodyClose(code)\n    }\n\n    return effects.check(\n      nextPrefixedOrBlank,\n      tableBodyClose,\n      tableBodyContinue\n    )(code)\n  }\n\n  function tableBodyClose(code) {\n    effects.exit('tableBody')\n    return tableClose(code)\n  }\n\n  function tableBodyContinue(code) {\n    // Always a line ending.\n    effects.enter('lineEnding')\n    effects.consume(code)\n    effects.exit('lineEnding')\n    // Support an optional prefix, then start a body row.\n    return createSpace(effects, rowStartBody, 'linePrefix', 4)\n  }\n}\n\n// Based on micromark, but that won’t work as we’re in a table, and that expects\n// content.\n// <https://github.com/micromark/micromark/blob/main/lib/tokenize/setext-underline.js>\nfunction tokenizeSetextUnderlineMini(effects, ok, nok) {\n  return start\n\n  function start(code) {\n    // `-`\n    if (code !== 45) {\n      return nok(code)\n    }\n\n    effects.enter('setextUnderline')\n    return sequence(code)\n  }\n\n  function sequence(code) {\n    if (code === 45) {\n      effects.consume(code)\n      return sequence\n    }\n\n    return whitespace(code)\n  }\n\n  function whitespace(code) {\n    if (code === -2 || code === -1 || code === 32) {\n      effects.consume(code)\n      return whitespace\n    }\n\n    if (code === null || code === -5 || code === -4 || code === -3) {\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\nfunction tokenizeNextPrefixedOrBlank(effects, ok, nok) {\n  var size = 0\n\n  return start\n\n  function start(code) {\n    // This is a check, so we don’t care about tokens, but we open a bogus one\n    // so we’re valid.\n    effects.enter('check')\n    // EOL.\n    effects.consume(code)\n    return whitespace\n  }\n\n  function whitespace(code) {\n    // VS or SP.\n    if (code === -1 || code === 32) {\n      effects.consume(code)\n      size++\n      return size === 4 ? ok : whitespace\n    }\n\n    // EOF or whitespace\n    if (code === null || code < 0) {\n      return ok(code)\n    }\n\n    // Anything else.\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/syntax.js\n");

/***/ })

};
;