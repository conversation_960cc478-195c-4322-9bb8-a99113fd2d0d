import * as React from "react";

function SvgPankodIcon(props: React.SVGProps<SVGSVGElement>) {
    return (
        <svg
            width="1em"
            height="1em"
            viewBox="0 0 800 160"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            {...props}
        >
            <defs>
                <path
                    id="pankod-icon_svg__a"
                    d="M.322.275h90.963v120.483H.322z"
                />
                <path id="pankod-icon_svg__c" d="M0 .245h217.002V160H0z" />
            </defs>
            <g fill="none" fillRule="evenodd">
                <path
                    d="M275.067 101.866h23.453c11.562 0 20.15-7.875 20.15-18.54 0-10.5-8.588-18.375-19.654-18.375h-23.95v36.915zm0 18.54V156h-22.628V46.412h52.027c20.646 0 36.997 15.258 36.997 36.915 0 21.82-16.02 37.078-37.492 37.078h-28.904zM412.21 116.169a22.587 22.587 0 00-22.659-22.656c-12.57 0-22.824 10.088-22.824 22.656 0 12.569 10.255 22.821 22.824 22.821 12.57 0 22.66-10.252 22.66-22.821m-26.63-42.665c11.744 0 23.983 5.953 28.284 15.545h.496l.827-13.395h20.178V156h-20.178l-.827-12.545h-.496C409.399 153.377 396.663 159 385.582 159c-20.51 0-41.68-17.199-41.68-42.831s21.337-42.665 41.68-42.665zM442.683 75.646h20.13l1.319 13.348h.33c4.95-9.558 15.51-15.49 26.399-15.49 21.943 0 35.968 16.808 35.968 38.395V156h-22.77v-41.793c0-11.206-8.249-20.434-19.303-20.434-10.56 0-19.304 9.393-19.304 20.928V156h-22.77V75.646zM534.146 38.084h22.904v68.945h.332l30.705-31.264h28.546l-37.011 37.023L617.914 156h-28.707l-31.825-37.453h-.332V156h-22.904z"
                    fill="currentcolor"
                />
                <path
                    d="M680.793 117.169c0-12.402-10.439-22.656-22.866-22.656s-22.866 10.254-22.866 22.656c0 12.569 10.439 22.821 22.866 22.821s22.866-10.252 22.866-22.821m-68.598 0c0-23.483 20.546-42.665 45.732-42.665 25.185 0 45.732 19.182 45.732 42.665S683.112 160 657.927 160c-25.186 0-45.732-19.348-45.732-42.831"
                    fill="currentcolor"
                />
                <g transform="translate(708.537 37.863)">
                    <mask id="pankod-icon_svg__b" fill="#fff">
                        <use xlinkHref="#pankod-icon_svg__a" />
                    </mask>
                    <path
                        d="M68.504 78.186c0-12.492-10.07-22.519-22.618-22.519-12.546 0-22.782 10.027-22.782 22.52 0 12.492 10.236 22.682 22.782 22.682 12.547 0 22.618-10.19 22.618-22.683zM91.285.276v117.861H71.303l-.983-12.83h-.33c-4.458 9.862-17.335 15.451-28.396 15.451-20.966 0-41.272-17.094-41.272-42.572 0-25.477 20.306-42.407 40.777-42.407 11.556 0 22.947 6.082 27.074 15.45h.33V.276h22.782z"
                        fill="currentcolor"
                        mask="url(#pankod-icon_svg__b)"
                    />
                </g>
                <mask id="pankod-icon_svg__d" fill="#fff">
                    <use xlinkHref="#pankod-icon_svg__c" />
                </mask>
                <path
                    d="M182.173 72.305c-2.29.436-5.655.147-9.19-3.368-3.668-3.612-4.345-5.761-4.25-6.94.052-.648.857-.927 1.317-.465 5.78 5.814 9.82 8.131 12.257 9.045.852.319.76 1.557-.134 1.728m26.542 5.663c-6.276-4.573-27.029-25.934-49.026-43.646-6.438-6.16-14.343-12.953-24.534-19.151C77.881-19.665 18.3 17.397 18.3 17.397c69.437-26.06 111.01 9.69 111.01 9.69C75.653 7.744 14.422 29.206 0 46.055c6.595-3.596 32.116-17.178 77.408-15.215 30.387 1.852 48.579 14.189 59.277 26.497.266.38.596.772 1 1.176l.005.006c-.028.01-13.993 4.997-30.77-1.962 0 0 10.335 15.478 24.028 16.252-1.962 7.695-3.847 16.606-5.41 28.706-4.397 36.026 47.333 55.179 55.89 58.092l1.192.393s-18.86-18.315-17.052-61.137c-12.4-5.933-12.918-15.736-13.693-18.831 0 0 6.46 10.319 12.143 10.834 5.684.774 11.245-.894 13.435-1.29 3.037-.547 10.851.775 11.368 6.965.258 7.997-2.067 20.637 9.56 26.313 0 0-3.101-16.252 5.166-16.51 4.393.516 7.751.258 8.268-2.58.775-3.353 2.325-7.48 4.134-11.608 1.808-4.128 2.325-7.223-7.234-14.188"
                    fill="currentcolor"
                    mask="url(#pankod-icon_svg__d)"
                />
            </g>
        </svg>
    );
}

export default SvgPankodIcon;
