"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-from-markdown";
exports.ids = ["vendor-chunks/mdast-util-from-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-from-markdown/dist/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/mdast-util-from-markdown/dist/index.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = fromMarkdown\n\n// These three are compiled away in the `dist/`\n\nvar toString = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/index.js\")\nvar assign = __webpack_require__(/*! micromark/dist/constant/assign */ \"(ssr)/./node_modules/micromark/dist/constant/assign.js\")\nvar own = __webpack_require__(/*! micromark/dist/constant/has-own-property */ \"(ssr)/./node_modules/micromark/dist/constant/has-own-property.js\")\nvar normalizeIdentifier = __webpack_require__(/*! micromark/dist/util/normalize-identifier */ \"(ssr)/./node_modules/micromark/dist/util/normalize-identifier.js\")\nvar safeFromInt = __webpack_require__(/*! micromark/dist/util/safe-from-int */ \"(ssr)/./node_modules/micromark/dist/util/safe-from-int.js\")\nvar parser = __webpack_require__(/*! micromark/dist/parse */ \"(ssr)/./node_modules/micromark/dist/parse.js\")\nvar preprocessor = __webpack_require__(/*! micromark/dist/preprocess */ \"(ssr)/./node_modules/micromark/dist/preprocess.js\")\nvar postprocess = __webpack_require__(/*! micromark/dist/postprocess */ \"(ssr)/./node_modules/micromark/dist/postprocess.js\")\nvar decode = __webpack_require__(/*! parse-entities/decode-entity */ \"(ssr)/./node_modules/parse-entities/decode-entity.js\")\nvar stringifyPosition = __webpack_require__(/*! unist-util-stringify-position */ \"(ssr)/./node_modules/unist-util-stringify-position/index.js\")\n\nfunction fromMarkdown(value, encoding, options) {\n  if (typeof encoding !== 'string') {\n    options = encoding\n    encoding = undefined\n  }\n\n  return compiler(options)(\n    postprocess(\n      parser(options).document().write(preprocessor()(value, encoding, true))\n    )\n  )\n}\n\n// Note this compiler only understand complete buffering, not streaming.\nfunction compiler(options) {\n  var settings = options || {}\n  var config = configure(\n    {\n      transforms: [],\n      canContainEols: [\n        'emphasis',\n        'fragment',\n        'heading',\n        'paragraph',\n        'strong'\n      ],\n\n      enter: {\n        autolink: opener(link),\n        autolinkProtocol: onenterdata,\n        autolinkEmail: onenterdata,\n        atxHeading: opener(heading),\n        blockQuote: opener(blockQuote),\n        characterEscape: onenterdata,\n        characterReference: onenterdata,\n        codeFenced: opener(codeFlow),\n        codeFencedFenceInfo: buffer,\n        codeFencedFenceMeta: buffer,\n        codeIndented: opener(codeFlow, buffer),\n        codeText: opener(codeText, buffer),\n        codeTextData: onenterdata,\n        data: onenterdata,\n        codeFlowValue: onenterdata,\n        definition: opener(definition),\n        definitionDestinationString: buffer,\n        definitionLabelString: buffer,\n        definitionTitleString: buffer,\n        emphasis: opener(emphasis),\n        hardBreakEscape: opener(hardBreak),\n        hardBreakTrailing: opener(hardBreak),\n        htmlFlow: opener(html, buffer),\n        htmlFlowData: onenterdata,\n        htmlText: opener(html, buffer),\n        htmlTextData: onenterdata,\n        image: opener(image),\n        label: buffer,\n        link: opener(link),\n        listItem: opener(listItem),\n        listItemValue: onenterlistitemvalue,\n        listOrdered: opener(list, onenterlistordered),\n        listUnordered: opener(list),\n        paragraph: opener(paragraph),\n        reference: onenterreference,\n        referenceString: buffer,\n        resourceDestinationString: buffer,\n        resourceTitleString: buffer,\n        setextHeading: opener(heading),\n        strong: opener(strong),\n        thematicBreak: opener(thematicBreak)\n      },\n\n      exit: {\n        atxHeading: closer(),\n        atxHeadingSequence: onexitatxheadingsequence,\n        autolink: closer(),\n        autolinkEmail: onexitautolinkemail,\n        autolinkProtocol: onexitautolinkprotocol,\n        blockQuote: closer(),\n        characterEscapeValue: onexitdata,\n        characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n        characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n        characterReferenceValue: onexitcharacterreferencevalue,\n        codeFenced: closer(onexitcodefenced),\n        codeFencedFence: onexitcodefencedfence,\n        codeFencedFenceInfo: onexitcodefencedfenceinfo,\n        codeFencedFenceMeta: onexitcodefencedfencemeta,\n        codeFlowValue: onexitdata,\n        codeIndented: closer(onexitcodeindented),\n        codeText: closer(onexitcodetext),\n        codeTextData: onexitdata,\n        data: onexitdata,\n        definition: closer(),\n        definitionDestinationString: onexitdefinitiondestinationstring,\n        definitionLabelString: onexitdefinitionlabelstring,\n        definitionTitleString: onexitdefinitiontitlestring,\n        emphasis: closer(),\n        hardBreakEscape: closer(onexithardbreak),\n        hardBreakTrailing: closer(onexithardbreak),\n        htmlFlow: closer(onexithtmlflow),\n        htmlFlowData: onexitdata,\n        htmlText: closer(onexithtmltext),\n        htmlTextData: onexitdata,\n        image: closer(onexitimage),\n        label: onexitlabel,\n        labelText: onexitlabeltext,\n        lineEnding: onexitlineending,\n        link: closer(onexitlink),\n        listItem: closer(),\n        listOrdered: closer(),\n        listUnordered: closer(),\n        paragraph: closer(),\n        referenceString: onexitreferencestring,\n        resourceDestinationString: onexitresourcedestinationstring,\n        resourceTitleString: onexitresourcetitlestring,\n        resource: onexitresource,\n        setextHeading: closer(onexitsetextheading),\n        setextHeadingLineSequence: onexitsetextheadinglinesequence,\n        setextHeadingText: onexitsetextheadingtext,\n        strong: closer(),\n        thematicBreak: closer()\n      }\n    },\n\n    settings.mdastExtensions || []\n  )\n\n  var data = {}\n\n  return compile\n\n  function compile(events) {\n    var tree = {type: 'root', children: []}\n    var stack = [tree]\n    var tokenStack = []\n    var listStack = []\n    var index = -1\n    var handler\n    var listStart\n\n    var context = {\n      stack: stack,\n      tokenStack: tokenStack,\n      config: config,\n      enter: enter,\n      exit: exit,\n      buffer: buffer,\n      resume: resume,\n      setData: setData,\n      getData: getData\n    }\n\n    while (++index < events.length) {\n      // We preprocess lists to add `listItem` tokens, and to infer whether\n      // items the list itself are spread out.\n      if (\n        events[index][1].type === 'listOrdered' ||\n        events[index][1].type === 'listUnordered'\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          listStart = listStack.pop(index)\n          index = prepareList(events, listStart, index)\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      handler = config[events[index][0]]\n\n      if (own.call(handler, events[index][1].type)) {\n        handler[events[index][1].type].call(\n          assign({sliceSerialize: events[index][2].sliceSerialize}, context),\n          events[index][1]\n        )\n      }\n    }\n\n    if (tokenStack.length) {\n      throw new Error(\n        'Cannot close document, a token (`' +\n          tokenStack[tokenStack.length - 1].type +\n          '`, ' +\n          stringifyPosition({\n            start: tokenStack[tokenStack.length - 1].start,\n            end: tokenStack[tokenStack.length - 1].end\n          }) +\n          ') is still open'\n      )\n    }\n\n    // Figure out `root` position.\n    tree.position = {\n      start: point(\n        events.length ? events[0][1].start : {line: 1, column: 1, offset: 0}\n      ),\n\n      end: point(\n        events.length\n          ? events[events.length - 2][1].end\n          : {line: 1, column: 1, offset: 0}\n      )\n    }\n\n    index = -1\n    while (++index < config.transforms.length) {\n      tree = config.transforms[index](tree) || tree\n    }\n\n    return tree\n  }\n\n  function prepareList(events, start, length) {\n    var index = start - 1\n    var containerBalance = -1\n    var listSpread = false\n    var listItem\n    var tailIndex\n    var lineIndex\n    var tailEvent\n    var event\n    var firstBlankLineIndex\n    var atMarker\n\n    while (++index <= length) {\n      event = events[index]\n\n      if (\n        event[1].type === 'listUnordered' ||\n        event[1].type === 'listOrdered' ||\n        event[1].type === 'blockQuote'\n      ) {\n        if (event[0] === 'enter') {\n          containerBalance++\n        } else {\n          containerBalance--\n        }\n\n        atMarker = undefined\n      } else if (event[1].type === 'lineEndingBlank') {\n        if (event[0] === 'enter') {\n          if (\n            listItem &&\n            !atMarker &&\n            !containerBalance &&\n            !firstBlankLineIndex\n          ) {\n            firstBlankLineIndex = index\n          }\n\n          atMarker = undefined\n        }\n      } else if (\n        event[1].type === 'linePrefix' ||\n        event[1].type === 'listItemValue' ||\n        event[1].type === 'listItemMarker' ||\n        event[1].type === 'listItemPrefix' ||\n        event[1].type === 'listItemPrefixWhitespace'\n      ) {\n        // Empty.\n      } else {\n        atMarker = undefined\n      }\n\n      if (\n        (!containerBalance &&\n          event[0] === 'enter' &&\n          event[1].type === 'listItemPrefix') ||\n        (containerBalance === -1 &&\n          event[0] === 'exit' &&\n          (event[1].type === 'listUnordered' ||\n            event[1].type === 'listOrdered'))\n      ) {\n        if (listItem) {\n          tailIndex = index\n          lineIndex = undefined\n\n          while (tailIndex--) {\n            tailEvent = events[tailIndex]\n\n            if (\n              tailEvent[1].type === 'lineEnding' ||\n              tailEvent[1].type === 'lineEndingBlank'\n            ) {\n              if (tailEvent[0] === 'exit') continue\n\n              if (lineIndex) {\n                events[lineIndex][1].type = 'lineEndingBlank'\n                listSpread = true\n              }\n\n              tailEvent[1].type = 'lineEnding'\n              lineIndex = tailIndex\n            } else if (\n              tailEvent[1].type === 'linePrefix' ||\n              tailEvent[1].type === 'blockQuotePrefix' ||\n              tailEvent[1].type === 'blockQuotePrefixWhitespace' ||\n              tailEvent[1].type === 'blockQuoteMarker' ||\n              tailEvent[1].type === 'listItemIndent'\n            ) {\n              // Empty\n            } else {\n              break\n            }\n          }\n\n          if (\n            firstBlankLineIndex &&\n            (!lineIndex || firstBlankLineIndex < lineIndex)\n          ) {\n            listItem._spread = true\n          }\n\n          // Fix position.\n          listItem.end = point(\n            lineIndex ? events[lineIndex][1].start : event[1].end\n          )\n\n          events.splice(lineIndex || index, 0, ['exit', listItem, event[2]])\n          index++\n          length++\n        }\n\n        // Create a new list item.\n        if (event[1].type === 'listItemPrefix') {\n          listItem = {\n            type: 'listItem',\n            _spread: false,\n            start: point(event[1].start)\n          }\n\n          events.splice(index, 0, ['enter', listItem, event[2]])\n          index++\n          length++\n          firstBlankLineIndex = undefined\n          atMarker = true\n        }\n      }\n    }\n\n    events[start][1]._spread = listSpread\n    return length\n  }\n\n  function setData(key, value) {\n    data[key] = value\n  }\n\n  function getData(key) {\n    return data[key]\n  }\n\n  function point(d) {\n    return {line: d.line, column: d.column, offset: d.offset}\n  }\n\n  function opener(create, and) {\n    return open\n\n    function open(token) {\n      enter.call(this, create(token), token)\n      if (and) and.call(this, token)\n    }\n  }\n\n  function buffer() {\n    this.stack.push({type: 'fragment', children: []})\n  }\n\n  function enter(node, token) {\n    this.stack[this.stack.length - 1].children.push(node)\n    this.stack.push(node)\n    this.tokenStack.push(token)\n    node.position = {start: point(token.start)}\n    return node\n  }\n\n  function closer(and) {\n    return close\n\n    function close(token) {\n      if (and) and.call(this, token)\n      exit.call(this, token)\n    }\n  }\n\n  function exit(token) {\n    var node = this.stack.pop()\n    var open = this.tokenStack.pop()\n\n    if (!open) {\n      throw new Error(\n        'Cannot close `' +\n          token.type +\n          '` (' +\n          stringifyPosition({start: token.start, end: token.end}) +\n          '): it’s not open'\n      )\n    } else if (open.type !== token.type) {\n      throw new Error(\n        'Cannot close `' +\n          token.type +\n          '` (' +\n          stringifyPosition({start: token.start, end: token.end}) +\n          '): a different token (`' +\n          open.type +\n          '`, ' +\n          stringifyPosition({start: open.start, end: open.end}) +\n          ') is open'\n      )\n    }\n\n    node.position.end = point(token.end)\n    return node\n  }\n\n  function resume() {\n    return toString(this.stack.pop())\n  }\n\n  //\n  // Handlers.\n  //\n\n  function onenterlistordered() {\n    setData('expectingFirstListItemValue', true)\n  }\n\n  function onenterlistitemvalue(token) {\n    if (getData('expectingFirstListItemValue')) {\n      this.stack[this.stack.length - 2].start = parseInt(\n        this.sliceSerialize(token),\n        10\n      )\n\n      setData('expectingFirstListItemValue')\n    }\n  }\n\n  function onexitcodefencedfenceinfo() {\n    var data = this.resume()\n    this.stack[this.stack.length - 1].lang = data\n  }\n\n  function onexitcodefencedfencemeta() {\n    var data = this.resume()\n    this.stack[this.stack.length - 1].meta = data\n  }\n\n  function onexitcodefencedfence() {\n    // Exit if this is the closing fence.\n    if (getData('flowCodeInside')) return\n    this.buffer()\n    setData('flowCodeInside', true)\n  }\n\n  function onexitcodefenced() {\n    var data = this.resume()\n    this.stack[this.stack.length - 1].value = data.replace(\n      /^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g,\n      ''\n    )\n\n    setData('flowCodeInside')\n  }\n\n  function onexitcodeindented() {\n    var data = this.resume()\n    this.stack[this.stack.length - 1].value = data\n  }\n\n  function onexitdefinitionlabelstring(token) {\n    // Discard label, use the source content instead.\n    var label = this.resume()\n    this.stack[this.stack.length - 1].label = label\n    this.stack[this.stack.length - 1].identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n  }\n\n  function onexitdefinitiontitlestring() {\n    var data = this.resume()\n    this.stack[this.stack.length - 1].title = data\n  }\n\n  function onexitdefinitiondestinationstring() {\n    var data = this.resume()\n    this.stack[this.stack.length - 1].url = data\n  }\n\n  function onexitatxheadingsequence(token) {\n    if (!this.stack[this.stack.length - 1].depth) {\n      this.stack[this.stack.length - 1].depth = this.sliceSerialize(\n        token\n      ).length\n    }\n  }\n\n  function onexitsetextheadingtext() {\n    setData('setextHeadingSlurpLineEnding', true)\n  }\n\n  function onexitsetextheadinglinesequence(token) {\n    this.stack[this.stack.length - 1].depth =\n      this.sliceSerialize(token).charCodeAt(0) === 61 ? 1 : 2\n  }\n\n  function onexitsetextheading() {\n    setData('setextHeadingSlurpLineEnding')\n  }\n\n  function onenterdata(token) {\n    var siblings = this.stack[this.stack.length - 1].children\n    var tail = siblings[siblings.length - 1]\n\n    if (!tail || tail.type !== 'text') {\n      // Add a new text node.\n      tail = text()\n      tail.position = {start: point(token.start)}\n      this.stack[this.stack.length - 1].children.push(tail)\n    }\n\n    this.stack.push(tail)\n  }\n\n  function onexitdata(token) {\n    var tail = this.stack.pop()\n    tail.value += this.sliceSerialize(token)\n    tail.position.end = point(token.end)\n  }\n\n  function onexitlineending(token) {\n    var context = this.stack[this.stack.length - 1]\n\n    // If we’re at a hard break, include the line ending in there.\n    if (getData('atHardBreak')) {\n      context.children[context.children.length - 1].position.end = point(\n        token.end\n      )\n\n      setData('atHardBreak')\n      return\n    }\n\n    if (\n      !getData('setextHeadingSlurpLineEnding') &&\n      config.canContainEols.indexOf(context.type) > -1\n    ) {\n      onenterdata.call(this, token)\n      onexitdata.call(this, token)\n    }\n  }\n\n  function onexithardbreak() {\n    setData('atHardBreak', true)\n  }\n\n  function onexithtmlflow() {\n    var data = this.resume()\n    this.stack[this.stack.length - 1].value = data\n  }\n\n  function onexithtmltext() {\n    var data = this.resume()\n    this.stack[this.stack.length - 1].value = data\n  }\n\n  function onexitcodetext() {\n    var data = this.resume()\n    this.stack[this.stack.length - 1].value = data\n  }\n\n  function onexitlink() {\n    var context = this.stack[this.stack.length - 1]\n\n    // To do: clean.\n    if (getData('inReference')) {\n      context.type += 'Reference'\n      context.referenceType = getData('referenceType') || 'shortcut'\n      delete context.url\n      delete context.title\n    } else {\n      delete context.identifier\n      delete context.label\n      delete context.referenceType\n    }\n\n    setData('referenceType')\n  }\n\n  function onexitimage() {\n    var context = this.stack[this.stack.length - 1]\n\n    // To do: clean.\n    if (getData('inReference')) {\n      context.type += 'Reference'\n      context.referenceType = getData('referenceType') || 'shortcut'\n      delete context.url\n      delete context.title\n    } else {\n      delete context.identifier\n      delete context.label\n      delete context.referenceType\n    }\n\n    setData('referenceType')\n  }\n\n  function onexitlabeltext(token) {\n    this.stack[this.stack.length - 2].identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n  }\n\n  function onexitlabel() {\n    var fragment = this.stack[this.stack.length - 1]\n    var value = this.resume()\n\n    this.stack[this.stack.length - 1].label = value\n\n    // Assume a reference.\n    setData('inReference', true)\n\n    if (this.stack[this.stack.length - 1].type === 'link') {\n      this.stack[this.stack.length - 1].children = fragment.children\n    } else {\n      this.stack[this.stack.length - 1].alt = value\n    }\n  }\n\n  function onexitresourcedestinationstring() {\n    var data = this.resume()\n    this.stack[this.stack.length - 1].url = data\n  }\n\n  function onexitresourcetitlestring() {\n    var data = this.resume()\n    this.stack[this.stack.length - 1].title = data\n  }\n\n  function onexitresource() {\n    setData('inReference')\n  }\n\n  function onenterreference() {\n    setData('referenceType', 'collapsed')\n  }\n\n  function onexitreferencestring(token) {\n    var label = this.resume()\n    this.stack[this.stack.length - 1].label = label\n    this.stack[this.stack.length - 1].identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n    setData('referenceType', 'full')\n  }\n\n  function onexitcharacterreferencemarker(token) {\n    setData('characterReferenceType', token.type)\n  }\n\n  function onexitcharacterreferencevalue(token) {\n    var data = this.sliceSerialize(token)\n    var type = getData('characterReferenceType')\n    var value\n    var tail\n\n    if (type) {\n      value = safeFromInt(\n        data,\n        type === 'characterReferenceMarkerNumeric' ? 10 : 16\n      )\n\n      setData('characterReferenceType')\n    } else {\n      value = decode(data)\n    }\n\n    tail = this.stack.pop()\n    tail.value += value\n    tail.position.end = point(token.end)\n  }\n\n  function onexitautolinkprotocol(token) {\n    onexitdata.call(this, token)\n    this.stack[this.stack.length - 1].url = this.sliceSerialize(token)\n  }\n\n  function onexitautolinkemail(token) {\n    onexitdata.call(this, token)\n    this.stack[this.stack.length - 1].url =\n      'mailto:' + this.sliceSerialize(token)\n  }\n\n  //\n  // Creaters.\n  //\n\n  function blockQuote() {\n    return {type: 'blockquote', children: []}\n  }\n\n  function codeFlow() {\n    return {type: 'code', lang: null, meta: null, value: ''}\n  }\n\n  function codeText() {\n    return {type: 'inlineCode', value: ''}\n  }\n\n  function definition() {\n    return {\n      type: 'definition',\n      identifier: '',\n      label: null,\n      title: null,\n      url: ''\n    }\n  }\n\n  function emphasis() {\n    return {type: 'emphasis', children: []}\n  }\n\n  function heading() {\n    return {type: 'heading', depth: undefined, children: []}\n  }\n\n  function hardBreak() {\n    return {type: 'break'}\n  }\n\n  function html() {\n    return {type: 'html', value: ''}\n  }\n\n  function image() {\n    return {type: 'image', title: null, url: '', alt: null}\n  }\n\n  function link() {\n    return {type: 'link', title: null, url: '', children: []}\n  }\n\n  function list(token) {\n    return {\n      type: 'list',\n      ordered: token.type === 'listOrdered',\n      start: null,\n      spread: token._spread,\n      children: []\n    }\n  }\n\n  function listItem(token) {\n    return {\n      type: 'listItem',\n      spread: token._spread,\n      checked: null,\n      children: []\n    }\n  }\n\n  function paragraph() {\n    return {type: 'paragraph', children: []}\n  }\n\n  function strong() {\n    return {type: 'strong', children: []}\n  }\n\n  function text() {\n    return {type: 'text', value: ''}\n  }\n\n  function thematicBreak() {\n    return {type: 'thematicBreak'}\n  }\n}\n\nfunction configure(config, extensions) {\n  var index = -1\n\n  while (++index < extensions.length) {\n    extension(config, extensions[index])\n  }\n\n  return config\n}\n\nfunction extension(config, extension) {\n  var key\n  var left\n\n  for (key in extension) {\n    left = own.call(config, key) ? config[key] : (config[key] = {})\n\n    if (key === 'canContainEols' || key === 'transforms') {\n      config[key] = [].concat(left, extension[key])\n    } else {\n      Object.assign(left, extension[key])\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-from-markdown/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-from-markdown/index.js":
/*!********************************************************!*\
  !*** ./node_modules/mdast-util-from-markdown/index.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = __webpack_require__(/*! ./dist */ \"(ssr)/./node_modules/mdast-util-from-markdown/dist/index.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1mcm9tLW1hcmtkb3duL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGlIQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1mcm9tLW1hcmtkb3duL2luZGV4LmpzPzRiYjYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0JylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-from-markdown/index.js\n");

/***/ })

};
;