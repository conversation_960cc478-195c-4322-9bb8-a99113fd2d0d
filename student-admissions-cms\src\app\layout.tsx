import { Metadata } from "next";
import { cookies } from "next/headers";import React, { Suspense } from 'react'
import { Refine, GitHubBanner, } from '@refinedev/core';
import { DevtoolsProvider } from '@providers/devtools'
import { RefineKbar, RefineKbarProvider } from "@refinedev/kbar";
    import { useNotificationProvider
,RefineSnackbarProvider} from '@refinedev/mui';
import routerProvider from "@refinedev/nextjs-router";


import { authProviderClient } from "@providers/auth-provider/auth-provider.client";
import { dataProvider } from "@providers/data-provider";
import { ColorModeContextProvider } from "@contexts/color-mode";
import { Header } from "@components/header";




export const metadata: Metadata = {
    title: "Student Admissions & Enrollment Management System",
    description: "Comprehensive student lifecycle management system for educational institutions",
    icons: {
        icon: "/favicon.ico",
    },
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {

    
    
    const cookieStore = cookies();
const theme = cookieStore.get("theme");
const defaultMode = theme?.value === "dark" ? "dark" : "light";

    return (
        <html lang="en">
            <body>
            <Suspense>
                <GitHubBanner />
                <RefineKbarProvider>
                <ColorModeContextProvider defaultMode={defaultMode}>
<RefineSnackbarProvider>
                <DevtoolsProvider>
                    <Refine 
                        routerProvider={routerProvider}
                        authProvider={authProviderClient}
dataProvider={dataProvider}
notificationProvider={useNotificationProvider}
                        resources={[
                            {
                                name: "inquiries",
                                list: "/inquiries",
                                create: "/inquiries/create",
                                edit: "/inquiries/edit/:id",
                                show: "/inquiries/show/:id",
                                meta: {
                                    canDelete: true,
                                    label: "Inquiries",
                                    icon: "contact_support",
                                },
                            },
                            {
                                name: "applications",
                                list: "/applications",
                                create: "/apply",
                                edit: "/applications/edit/:id",
                                show: "/applications/show/:id",
                                meta: {
                                    canDelete: true,
                                    label: "Applications",
                                    icon: "assignment",
                                },
                            },
                            {
                                name: "interviews",
                                list: "/interviews",
                                create: "/interviews/create",
                                edit: "/interviews/edit/:id",
                                show: "/interviews/show/:id",
                                meta: {
                                    canDelete: true,
                                    label: "Interviews",
                                    icon: "event",
                                },
                            },
                            {
                                name: "enrollments",
                                list: "/enrollments",
                                create: "/enrollments/create",
                                edit: "/enrollments/edit/:id",
                                show: "/enrollments/show/:id",
                                meta: {
                                    canDelete: true,
                                    label: "Enrollments",
                                    icon: "school",
                                },
                            },
                            {
                                name: "capacity",
                                list: "/capacity",
                                meta: {
                                    label: "Capacity Management",
                                    icon: "group",
                                },
                            },
                            {
                                name: "communications",
                                list: "/communications",
                                create: "/communications/create",
                                edit: "/communications/edit/:id",
                                meta: {
                                    canDelete: true,
                                    label: "Communications",
                                    icon: "email",
                                },
                            },
                            {
                                name: "reports",
                                list: "/reports",
                                meta: {
                                    label: "Reports & Analytics",
                                    icon: "analytics",
                                },
                            }
                        ]}
                        options={{
                            syncWithLocation: true,
                            warnWhenUnsavedChanges: true,
                            useNewQueryKeys: true,
                                projectId: "7BvQym-ncceep-txvsN1",
                             
                        }}
                    >
                        {children}
                        <RefineKbar />
                    </Refine>
                </DevtoolsProvider>
                </RefineSnackbarProvider>
</ColorModeContextProvider>
                </RefineKbarProvider>
            </Suspense>
            </body>
        </html>
    );
}
