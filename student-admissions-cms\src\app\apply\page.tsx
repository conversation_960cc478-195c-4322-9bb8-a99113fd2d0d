'use client';

import React, { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { useCreate, useList } from '@refinedev/core';
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  Button,
  Typography,
  Paper,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  CardHeader,
  Alert,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

interface ApplicationFormData {
  // Personal Information
  first_name: string;
  last_name: string;
  middle_name: string;
  date_of_birth: Date | null;
  gender: string;
  nationality: string;
  address: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone: string;
  email: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
  
  // Academic Information
  previous_school: string;
  previous_qualification: string;
  previous_percentage: number;
  previous_year_of_passing: number;
  
  // Program Selection
  program_id: string;
  academic_year_id: string;
}

const steps = [
  'Personal Information',
  'Academic Background',
  'Program Selection',
  'Review & Submit',
];

const ApplyPage: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [submitted, setSubmitted] = useState(false);

  const { mutate: createApplication, isLoading } = useCreate();

  const { data: programsData } = useList({
    resource: 'academic_programs',
    filters: [{ field: 'is_active', operator: 'eq', value: true }],
  });

  const { data: academicYearsData } = useList({
    resource: 'academic_years',
    filters: [{ field: 'is_active', operator: 'eq', value: true }],
  });

  const {
    control,
    handleSubmit,
    watch,
    trigger,
    formState: { errors },
  } = useForm<ApplicationFormData>({
    defaultValues: {
      first_name: '',
      last_name: '',
      middle_name: '',
      date_of_birth: null,
      gender: '',
      nationality: '',
      address: '',
      city: '',
      state: '',
      postal_code: '',
      country: '',
      phone: '',
      email: '',
      emergency_contact_name: '',
      emergency_contact_phone: '',
      previous_school: '',
      previous_qualification: '',
      previous_percentage: 0,
      previous_year_of_passing: new Date().getFullYear(),
      program_id: '',
      academic_year_id: '',
    },
  });

  const watchedValues = watch();

  const handleNext = async () => {
    const fieldsToValidate = getFieldsForStep(activeStep);
    const isStepValid = await trigger(fieldsToValidate);
    
    if (isStepValid) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const getFieldsForStep = (step: number): (keyof ApplicationFormData)[] => {
    switch (step) {
      case 0:
        return [
          'first_name',
          'last_name',
          'date_of_birth',
          'gender',
          'nationality',
          'address',
          'city',
          'state',
          'country',
          'phone',
          'email',
        ];
      case 1:
        return [
          'previous_school',
          'previous_qualification',
          'previous_percentage',
          'previous_year_of_passing',
        ];
      case 2:
        return ['program_id', 'academic_year_id'];
      default:
        return [];
    }
  };

  const onSubmit = (data: ApplicationFormData) => {
    const formattedData = {
      ...data,
      date_of_birth: data.date_of_birth?.toISOString().split('T')[0],
      status: 'new',
    };

    createApplication(
      {
        resource: 'applications',
        values: formattedData,
      },
      {
        onSuccess: () => {
          setSubmitted(true);
        },
      }
    );
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Controller
                name="first_name"
                control={control}
                rules={{ required: 'First name is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="First Name"
                    fullWidth
                    error={!!errors.first_name}
                    helperText={errors.first_name?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                name="middle_name"
                control={control}
                render={({ field }) => (
                  <TextField {...field} label="Middle Name" fullWidth />
                )}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                name="last_name"
                control={control}
                rules={{ required: 'Last name is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Last Name"
                    fullWidth
                    error={!!errors.last_name}
                    helperText={errors.last_name?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="date_of_birth"
                control={control}
                rules={{ required: 'Date of birth is required' }}
                render={({ field }) => (
                  <DatePicker
                    {...field}
                    label="Date of Birth"
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: !!errors.date_of_birth,
                        helperText: errors.date_of_birth?.message,
                      },
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="gender"
                control={control}
                rules={{ required: 'Gender is required' }}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.gender}>
                    <InputLabel>Gender</InputLabel>
                    <Select {...field} label="Gender">
                      <MenuItem value="male">Male</MenuItem>
                      <MenuItem value="female">Female</MenuItem>
                      <MenuItem value="other">Other</MenuItem>
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="nationality"
                control={control}
                rules={{ required: 'Nationality is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Nationality"
                    fullWidth
                    error={!!errors.nationality}
                    helperText={errors.nationality?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="phone"
                control={control}
                rules={{ required: 'Phone number is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Phone Number"
                    fullWidth
                    error={!!errors.phone}
                    helperText={errors.phone?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="email"
                control={control}
                rules={{
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address',
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Email Address"
                    type="email"
                    fullWidth
                    error={!!errors.email}
                    helperText={errors.email?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="address"
                control={control}
                rules={{ required: 'Address is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Address"
                    multiline
                    rows={3}
                    fullWidth
                    error={!!errors.address}
                    helperText={errors.address?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                name="city"
                control={control}
                rules={{ required: 'City is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="City"
                    fullWidth
                    error={!!errors.city}
                    helperText={errors.city?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                name="state"
                control={control}
                rules={{ required: 'State is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="State/Province"
                    fullWidth
                    error={!!errors.state}
                    helperText={errors.state?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                name="country"
                control={control}
                rules={{ required: 'Country is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Country"
                    fullWidth
                    error={!!errors.country}
                    helperText={errors.country?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        );
      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Controller
                name="previous_school"
                control={control}
                rules={{ required: 'Previous school is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Previous School/Institution"
                    fullWidth
                    error={!!errors.previous_school}
                    helperText={errors.previous_school?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="previous_qualification"
                control={control}
                rules={{ required: 'Previous qualification is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Previous Qualification"
                    fullWidth
                    error={!!errors.previous_qualification}
                    helperText={errors.previous_qualification?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <Controller
                name="previous_percentage"
                control={control}
                rules={{
                  required: 'Percentage is required',
                  min: { value: 0, message: 'Minimum 0%' },
                  max: { value: 100, message: 'Maximum 100%' },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Percentage/CGPA"
                    type="number"
                    fullWidth
                    error={!!errors.previous_percentage}
                    helperText={errors.previous_percentage?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <Controller
                name="previous_year_of_passing"
                control={control}
                rules={{ required: 'Year of passing is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Year of Passing"
                    type="number"
                    fullWidth
                    error={!!errors.previous_year_of_passing}
                    helperText={errors.previous_year_of_passing?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        );
      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Controller
                name="program_id"
                control={control}
                rules={{ required: 'Program selection is required' }}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.program_id}>
                    <InputLabel>Select Program</InputLabel>
                    <Select {...field} label="Select Program">
                      {programsData?.data?.map((program: any) => (
                        <MenuItem key={program.id} value={program.id}>
                          {program.name} ({program.code}) - {program.degree_type}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="academic_year_id"
                control={control}
                rules={{ required: 'Academic year selection is required' }}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.academic_year_id}>
                    <InputLabel>Academic Year</InputLabel>
                    <Select {...field} label="Academic Year">
                      {academicYearsData?.data?.map((year: any) => (
                        <MenuItem key={year.id} value={year.id}>
                          {year.year_name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
          </Grid>
        );
      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review Your Application
            </Typography>
            <Card sx={{ mb: 2 }}>
              <CardHeader title="Personal Information" />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography><strong>Name:</strong> {watchedValues.first_name} {watchedValues.last_name}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography><strong>Email:</strong> {watchedValues.email}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography><strong>Phone:</strong> {watchedValues.phone}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography><strong>Nationality:</strong> {watchedValues.nationality}</Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
            <Card sx={{ mb: 2 }}>
              <CardHeader title="Academic Background" />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography><strong>Previous School:</strong> {watchedValues.previous_school}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography><strong>Qualification:</strong> {watchedValues.previous_qualification}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography><strong>Percentage:</strong> {watchedValues.previous_percentage}%</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography><strong>Year of Passing:</strong> {watchedValues.previous_year_of_passing}</Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
            <Card>
              <CardHeader title="Program Selection" />
              <CardContent>
                <Typography>
                  <strong>Program:</strong> {programsData?.data?.find((p: any) => p.id === watchedValues.program_id)?.name}
                </Typography>
                <Typography>
                  <strong>Academic Year:</strong> {academicYearsData?.data?.find((y: any) => y.id === watchedValues.academic_year_id)?.year_name}
                </Typography>
              </CardContent>
            </Card>
          </Box>
        );
      default:
        return null;
    }
  };

  if (submitted) {
    return (
      <ProtectedRoute resource="applications" action="create">
        <Box sx={{ maxWidth: 600, mx: 'auto', mt: 4 }}>
          <Alert severity="success" sx={{ mb: 3 }}>
            <Typography variant="h6">Application Submitted Successfully!</Typography>
            <Typography>
              Your application has been submitted and is under review. You will receive updates via email.
            </Typography>
          </Alert>
          <Button variant="contained" href="/applications" fullWidth>
            View My Applications
          </Button>
        </Box>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute resource="applications" action="create">
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Box sx={{ maxWidth: 800, mx: 'auto', mt: 4 }}>
          <Typography variant="h4" gutterBottom align="center">
            Student Application Form
          </Typography>
          
          <Paper sx={{ p: 3 }}>
            <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>

            <form onSubmit={handleSubmit(onSubmit)}>
              {renderStepContent(activeStep)}

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
                <Button
                  disabled={activeStep === 0}
                  onClick={handleBack}
                >
                  Back
                </Button>
                <Box>
                  {activeStep === steps.length - 1 ? (
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={isLoading}
                    >
                      Submit Application
                    </Button>
                  ) : (
                    <Button variant="contained" onClick={handleNext}>
                      Next
                    </Button>
                  )}
                </Box>
              </Box>
            </form>
          </Paper>
        </Box>
      </LocalizationProvider>
    </ProtectedRoute>
  );
};

export default ApplyPage;
