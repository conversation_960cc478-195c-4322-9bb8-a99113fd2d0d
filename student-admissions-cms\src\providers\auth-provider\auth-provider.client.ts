"use client";

import type { AuthProvider } from "@refinedev/core";
import { supabaseBrowserClient } from "@utils/supabase/client";

export const authProviderClient: AuthProvider = {
    login: async ({ email, password }) => {
        const { data, error } =
            await supabaseBrowserClient.auth.signInWithPassword({
                email,
                password,
            });

        if (error) {
            return {
                success: false,
                error,
            };
        }

        if (data?.session) {
            await supabaseBrowserClient.auth.setSession(data.session);

            return {
                success: true,
                redirectTo: "/",
            };
        }

        // for third-party login
        return {
            success: false,
            error: {
                name: "<PERSON>ginError",
                message: "Invalid username or password",
            },
        };
    },
    logout: async () => {
        const { error } = await supabaseBrowserClient.auth.signOut();

        if (error) {
            return {
                success: false,
                error,
            };
        }

        return {
            success: true,
            redirectTo: "/login",
        };
    },
    register: async ({ email, password, fullName, role = 'applicant' }) => {
        try {
            const { data, error } = await supabaseBrowserClient.auth.signUp({
                email,
                password,
                options: {
                    data: {
                        full_name: fullName,
                        role: role,
                    }
                }
            });

            if (error) {
                return {
                    success: false,
                    error,
                };
            }

            if (data?.user) {
                // Create user profile in public.users table
                const { error: profileError } = await supabaseBrowserClient
                    .from('users')
                    .insert({
                        id: data.user.id,
                        email: data.user.email!,
                        full_name: fullName,
                        role: role,
                    });

                if (profileError) {
                    console.error('Error creating user profile:', profileError);
                }

                return {
                    success: true,
                    redirectTo: role === 'applicant' ? "/applications" : "/dashboard",
                };
            }
        } catch (error: any) {
            return {
                success: false,
                error,
            };
        }

        return {
            success: false,
            error: {
                message: "Register failed",
                name: "Invalid email or password",
            },
        };
    },
    check: async () => {
        const { data, error } = await supabaseBrowserClient.auth.getUser();
        const { user } = data;

        if (error) {
            return {
                authenticated: false,
                redirectTo: "/login",
                logout: true,
            };
        }

        if (user) {
            return {
                authenticated: true,
            };
        }

        return {
            authenticated: false,
            redirectTo: "/login",
        };
    },
    getPermissions: async () => {
        const { data } = await supabaseBrowserClient.auth.getUser();

        if (data?.user) {
            // Get user role from public.users table
            const { data: userProfile } = await supabaseBrowserClient
                .from('users')
                .select('role')
                .eq('id', data.user.id)
                .single();

            return userProfile?.role || 'applicant';
        }

        return null;
    },
    getIdentity: async () => {
        const { data } = await supabaseBrowserClient.auth.getUser();

        if (data?.user) {
            // Get full user profile from public.users table
            const { data: userProfile } = await supabaseBrowserClient
                .from('users')
                .select('*')
                .eq('id', data.user.id)
                .single();

            return {
                id: data.user.id,
                email: data.user.email,
                name: userProfile?.full_name || data.user.email,
                avatar: userProfile?.avatar_url,
                role: userProfile?.role || 'applicant',
                phone: userProfile?.phone,
                isActive: userProfile?.is_active,
            };
        }

        return null;
    },
    onError: async (error) => {
        if (error?.code === "PGRST301" || error?.code === 401) {
            return {
                logout: true,
            };
        }

        return { error };
    },
};
