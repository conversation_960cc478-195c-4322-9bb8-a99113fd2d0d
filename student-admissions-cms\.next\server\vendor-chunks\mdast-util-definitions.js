"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-definitions";
exports.ids = ["vendor-chunks/mdast-util-definitions"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-definitions/index.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-definitions/index.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar visit = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/index.js\")\n\nmodule.exports = getDefinitionFactory\n\nvar own = {}.hasOwnProperty\n\n// Get a definition in `node` by `identifier`.\nfunction getDefinitionFactory(node, options) {\n  return getterFactory(gather(node, options))\n}\n\n// Gather all definitions in `node`\nfunction gather(node) {\n  var cache = {}\n\n  if (!node || !node.type) {\n    throw new Error('mdast-util-definitions expected node')\n  }\n\n  visit(node, 'definition', ondefinition)\n\n  return cache\n\n  function ondefinition(definition) {\n    var id = normalise(definition.identifier)\n    if (!own.call(cache, id)) {\n      cache[id] = definition\n    }\n  }\n}\n\n// Factory to get a node from the given definition-cache.\nfunction getterFactory(cache) {\n  return getter\n\n  // Get a node from the bound definition-cache.\n  function getter(identifier) {\n    var id = identifier && normalise(identifier)\n    return id && own.call(cache, id) ? cache[id] : null\n  }\n}\n\nfunction normalise(identifier) {\n  return identifier.toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-definitions/index.js\n");

/***/ })

};
;