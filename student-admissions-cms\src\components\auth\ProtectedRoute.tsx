'use client';

import React from 'react';
import { useGetIdentity, usePermissions } from '@refinedev/core';
import { Box, Typography, Alert } from '@mui/material';
import { UserRole, hasPermission, canAccessRoute } from '@/utils/permissions';

interface ProtectedRouteProps {
  children: React.ReactNode;
  resource?: string;
  action?: 'create' | 'read' | 'update' | 'delete' | 'list';
  route?: string;
  requiredRole?: UserRole;
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  resource,
  action,
  route,
  requiredRole,
  fallback,
}) => {
  const { data: identity, isLoading: identityLoading } = useGetIdentity();
  const { data: permissions, isLoading: permissionsLoading } = usePermissions();

  if (identityLoading || permissionsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  if (!identity) {
    return (
      <Alert severity="error">
        You must be logged in to access this page.
      </Alert>
    );
  }

  const userRole = permissions as UserRole;

  // Check route-based access
  if (route && !canAccessRoute(userRole, route)) {
    return fallback || (
      <Alert severity="warning">
        You don't have permission to access this page.
      </Alert>
    );
  }

  // Check resource and action-based access
  if (resource && action && !hasPermission(userRole, resource, action)) {
    return fallback || (
      <Alert severity="warning">
        You don't have permission to {action} {resource}.
      </Alert>
    );
  }

  // Check role-based access
  if (requiredRole && userRole !== requiredRole && userRole !== 'admin') {
    return fallback || (
      <Alert severity="warning">
        This page requires {requiredRole} role access.
      </Alert>
    );
  }

  return <>{children}</>;
};

// Higher-order component for protecting pages
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<ProtectedRouteProps, 'children'>
) => {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
};

// Hook for checking permissions in components
export const useAuth = () => {
  const { data: identity } = useGetIdentity();
  const { data: permissions } = usePermissions();

  const userRole = permissions as UserRole;

  const checkPermission = (resource: string, action: 'create' | 'read' | 'update' | 'delete' | 'list') => {
    return hasPermission(userRole, resource, action);
  };

  const checkRoute = (route: string) => {
    return canAccessRoute(userRole, route);
  };

  return {
    identity,
    userRole,
    checkPermission,
    checkRoute,
    isAdmin: userRole === 'admin',
    isAdmissionOfficer: userRole === 'admission_officer',
    isStaff: userRole === 'staff',
    isApplicant: userRole === 'applicant',
  };
};
