"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/notistack";
exports.ids = ["vendor-chunks/notistack"];
exports.modules = {

/***/ "(ssr)/./node_modules/notistack/dist/notistack.esm.js":
/*!******************************************************!*\
  !*** ./node_modules/notistack/dist/notistack.esm.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SnackbarContent: () => (/* binding */ SnackbarContent),\n/* harmony export */   SnackbarProvider: () => (/* binding */ SnackbarProvider),\n/* harmony export */   useSnackbar: () => (/* binding */ useSnackbar),\n/* harmony export */   withSnackbar: () => (/* binding */ withSnackbar)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/notistack/node_modules/clsx/dist/clsx.m.js\");\n/* harmony import */ var _mui_material_Slide__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/Slide */ \"(ssr)/./node_modules/@mui/material/Slide/Slide.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/system/esm/colorManipulator/colorManipulator.js\");\n/* harmony import */ var _mui_material_Collapse__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/Collapse */ \"(ssr)/./node_modules/@mui/material/Collapse/Collapse.js\");\n/* harmony import */ var _mui_material_SvgIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/SvgIcon */ \"(ssr)/./node_modules/@mui/material/SvgIcon/SvgIcon.js\");\n/* harmony import */ var _mui_material_ClickAwayListener__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/ClickAwayListener */ \"(ssr)/./node_modules/@mui/material/ClickAwayListener/ClickAwayListener.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hoist-non-react-statics */ \"(ssr)/./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\n\n\n\n\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nvar SnackbarContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext();\n\nvar allClasses = {\n  mui: {\n    root: {},\n    anchorOriginTopCenter: {},\n    anchorOriginBottomCenter: {},\n    anchorOriginTopRight: {},\n    anchorOriginBottomRight: {},\n    anchorOriginTopLeft: {},\n    anchorOriginBottomLeft: {}\n  },\n  container: {\n    containerRoot: {},\n    containerAnchorOriginTopCenter: {},\n    containerAnchorOriginBottomCenter: {},\n    containerAnchorOriginTopRight: {},\n    containerAnchorOriginBottomRight: {},\n    containerAnchorOriginTopLeft: {},\n    containerAnchorOriginBottomLeft: {}\n  }\n};\nvar MESSAGES = {\n  NO_PERSIST_ALL: 'WARNING - notistack: Reached maxSnack while all enqueued snackbars have \\'persist\\' flag. Notistack will dismiss the oldest snackbar anyway to allow other ones in the queue to be presented.'\n};\nvar SNACKBAR_INDENTS = {\n  view: {\n    \"default\": 20,\n    dense: 4\n  },\n  snackbar: {\n    \"default\": 6,\n    dense: 2\n  }\n};\nvar DEFAULTS = {\n  maxSnack: 3,\n  dense: false,\n  hideIconVariant: false,\n  variant: 'default',\n  autoHideDuration: 5000,\n  anchorOrigin: {\n    vertical: 'bottom',\n    horizontal: 'left'\n  },\n  TransitionComponent: _mui_material_Slide__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n  transitionDuration: {\n    enter: 225,\n    exit: 195\n  }\n};\nvar capitalise = function capitalise(text) {\n  return text.charAt(0).toUpperCase() + text.slice(1);\n};\nvar originKeyExtractor = function originKeyExtractor(anchor) {\n  return \"\" + capitalise(anchor.vertical) + capitalise(anchor.horizontal);\n};\n/**\r\n * Omit SnackbarContainer class keys that are not needed for SnackbarItem\r\n */\n\nvar omitContainerKeys = function omitContainerKeys(classes) {\n  return (// @ts-ignore\n    Object.keys(classes).filter(function (key) {\n      return !allClasses.container[key];\n    }).reduce(function (obj, key) {\n      var _extends2;\n\n      return _extends({}, obj, (_extends2 = {}, _extends2[key] = classes[key], _extends2));\n    }, {})\n  );\n};\nvar REASONS = {\n  TIMEOUT: 'timeout',\n  CLICKAWAY: 'clickaway',\n  MAXSNACK: 'maxsnack',\n  INSTRUCTED: 'instructed'\n};\n/** Tranforms classes name */\n\nvar transformer = {\n  toContainerAnchorOrigin: function toContainerAnchorOrigin(origin) {\n    return \"containerAnchorOrigin\" + origin;\n  },\n  toAnchorOrigin: function toAnchorOrigin(_ref) {\n    var vertical = _ref.vertical,\n        horizontal = _ref.horizontal;\n    return \"anchorOrigin\" + capitalise(vertical) + capitalise(horizontal);\n  },\n  toVariant: function toVariant(variant) {\n    return \"variant\" + capitalise(variant);\n  }\n};\nvar isDefined = function isDefined(value) {\n  return !!value || value === 0;\n};\n\nvar numberOrNull = function numberOrNull(numberish) {\n  return typeof numberish === 'number' || numberish === null;\n}; // @ts-ignore\n\n\nvar merge = function merge(options, props, defaults) {\n  return function (name) {\n    if (name === 'autoHideDuration') {\n      if (numberOrNull(options.autoHideDuration)) return options.autoHideDuration;\n      if (numberOrNull(props.autoHideDuration)) return props.autoHideDuration;\n      return DEFAULTS.autoHideDuration;\n    }\n\n    return options[name] || props[name] || defaults[name];\n  };\n};\nfunction objectMerge(options, props, defaults) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  if (props === void 0) {\n    props = {};\n  }\n\n  if (defaults === void 0) {\n    defaults = {};\n  }\n\n  return _extends({}, defaults, {}, props, {}, options);\n}\n\nvar componentName = 'SnackbarContent';\nvar classes = {\n  root: componentName + \"-root\"\n};\nvar Root = /*#__PURE__*/(0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__[\"default\"])('div')(function (_ref) {\n  var _ref2, _ref3;\n\n  var theme = _ref.theme;\n  return _ref3 = {}, _ref3[\"&.\" + classes.root] = (_ref2 = {\n    display: 'flex',\n    flexWrap: 'wrap',\n    flexGrow: 1\n  }, _ref2[theme.breakpoints.up('sm')] = {\n    flexGrow: 'initial',\n    minWidth: 288\n  }, _ref2), _ref3;\n});\nvar SnackbarContent = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function (_ref4, ref) {\n  var className = _ref4.className,\n      props = _objectWithoutPropertiesLoose(_ref4, [\"className\"]);\n\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Root, Object.assign({\n    ref: ref,\n    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(classes.root, className)\n  }, props));\n});\n\nvar DIRECTION = {\n  right: 'left',\n  left: 'right',\n  bottom: 'up',\n  top: 'down'\n};\nvar getTransitionDirection = function getTransitionDirection(anchorOrigin) {\n  if (anchorOrigin.horizontal !== 'center') {\n    return DIRECTION[anchorOrigin.horizontal];\n  }\n\n  return DIRECTION[anchorOrigin.vertical];\n};\n\nvar CheckIcon = function CheckIcon(props) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_mui_material_SvgIcon__WEBPACK_IMPORTED_MODULE_6__[\"default\"], Object.assign({}, props), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    d: \"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41\\n        10.59L10 14.17L17.59 6.58L19 8L10 17Z\"\n  }));\n};\n\nvar WarningIcon = function WarningIcon(props) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_mui_material_SvgIcon__WEBPACK_IMPORTED_MODULE_6__[\"default\"], Object.assign({}, props), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    d: \"M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z\"\n  }));\n};\n\nvar ErrorIcon = function ErrorIcon(props) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_mui_material_SvgIcon__WEBPACK_IMPORTED_MODULE_6__[\"default\"], Object.assign({}, props), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    d: \"M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,\\n        6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,\\n        13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z\"\n  }));\n};\n\nvar InfoIcon = function InfoIcon(props) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_mui_material_SvgIcon__WEBPACK_IMPORTED_MODULE_6__[\"default\"], Object.assign({}, props), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    d: \"M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,\\n        0 22,12A10,10 0 0,0 12,2Z\"\n  }));\n};\n\nvar iconStyles = {\n  fontSize: 20,\n  marginInlineEnd: 8\n};\nvar defaultIconVariants = {\n  \"default\": undefined,\n  success: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(CheckIcon, {\n    style: iconStyles\n  }),\n  warning: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(WarningIcon, {\n    style: iconStyles\n  }),\n  error: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ErrorIcon, {\n    style: iconStyles\n  }),\n  info: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(InfoIcon, {\n    style: iconStyles\n  })\n};\n\n/**\n * @link https://github.com/mui-org/material-ui/blob/master/packages/material-ui/src/utils/createChainedFunction.js\n */\nfunction createChainedFunction(funcs, extraArg) {\n  return funcs.reduce(function (acc, func) {\n    if (func == null) return acc;\n\n    if (true) {\n      if (typeof func !== 'function') {\n        // eslint-disable-next-line no-console\n        console.error('Invalid Argument Type. must only provide functions, undefined, or null.');\n      }\n    }\n\n    return function chainedFunction() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      var argums = [].concat(args);\n\n      if (extraArg && argums.indexOf(extraArg) === -1) {\n        argums.push(extraArg);\n      }\n\n      acc.apply(this, argums);\n      func.apply(this, argums);\n    };\n  }, function () {});\n}\n\n/**\n * @link https://github.com/mui-org/material-ui/blob/master/packages/material-ui/src/utils/useEventCallback.js\n */\nvar useEnhancedEffect = typeof window !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useEventCallback(fn) {\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fn);\n  useEnhancedEffect(function () {\n    ref.current = fn;\n  });\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    return (ref.current).apply(void 0, arguments);\n  }, []);\n}\n\nvar Snackbar = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function (props, ref) {\n  var children = props.children,\n      autoHideDuration = props.autoHideDuration,\n      ClickAwayListenerProps = props.ClickAwayListenerProps,\n      _props$disableWindowB = props.disableWindowBlurListener,\n      disableWindowBlurListener = _props$disableWindowB === void 0 ? false : _props$disableWindowB,\n      onClose = props.onClose,\n      onMouseEnter = props.onMouseEnter,\n      onMouseLeave = props.onMouseLeave,\n      open = props.open,\n      resumeHideDuration = props.resumeHideDuration,\n      other = _objectWithoutPropertiesLoose(props, [\"children\", \"autoHideDuration\", \"ClickAwayListenerProps\", \"disableWindowBlurListener\", \"onClose\", \"onMouseEnter\", \"onMouseLeave\", \"open\", \"resumeHideDuration\"]);\n\n  var timerAutoHide = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  var handleClose = useEventCallback(function () {\n    if (onClose) {\n      onClose.apply(void 0, arguments);\n    }\n  });\n  var setAutoHideTimer = useEventCallback(function (autoHideDurationParam) {\n    if (!onClose || autoHideDurationParam == null) {\n      return;\n    }\n\n    clearTimeout(timerAutoHide.current);\n    timerAutoHide.current = setTimeout(function () {\n      handleClose(null, REASONS.TIMEOUT);\n    }, autoHideDurationParam);\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (open) {\n      setAutoHideTimer(autoHideDuration);\n    }\n\n    return function () {\n      clearTimeout(timerAutoHide.current);\n    };\n  }, [open, autoHideDuration, setAutoHideTimer]);\n  /**\n   * Pause the timer when the user is interacting with the Snackbar\n   * or when the user hide the window.\n   */\n\n  var handlePause = function handlePause() {\n    clearTimeout(timerAutoHide.current);\n  };\n  /**\n   * Restart the timer when the user is no longer interacting with the Snackbar\n   * or when the window is shown back.\n   */\n\n\n  var handleResume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    if (autoHideDuration != null) {\n      setAutoHideTimer(resumeHideDuration != null ? resumeHideDuration : autoHideDuration * 0.5);\n    }\n  }, [autoHideDuration, resumeHideDuration, setAutoHideTimer]);\n\n  var handleMouseEnter = function handleMouseEnter(event) {\n    if (onMouseEnter) {\n      onMouseEnter(event);\n    }\n\n    handlePause();\n  };\n\n  var handleMouseLeave = function handleMouseLeave(event) {\n    if (onMouseLeave) {\n      onMouseLeave(event);\n    }\n\n    handleResume();\n  };\n\n  var handleClickAway = function handleClickAway(event) {\n    if (onClose) {\n      onClose(event, REASONS.CLICKAWAY);\n    }\n  };\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (!disableWindowBlurListener && open) {\n      window.addEventListener('focus', handleResume);\n      window.addEventListener('blur', handlePause);\n      return function () {\n        window.removeEventListener('focus', handleResume);\n        window.removeEventListener('blur', handlePause);\n      };\n    }\n\n    return undefined;\n  }, [disableWindowBlurListener, handleResume, open]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_mui_material_ClickAwayListener__WEBPACK_IMPORTED_MODULE_7__.ClickAwayListener, _extends({\n    onClickAway: handleClickAway\n  }, ClickAwayListenerProps), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"div\", _extends({\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    ref: ref\n  }, other), children));\n});\n\nvar componentName$1 = 'SnackbarItem';\nvar classes$1 = {\n  contentRoot: componentName$1 + \"-contentRoot\",\n  lessPadding: componentName$1 + \"-lessPadding\",\n  variantSuccess: componentName$1 + \"-variantSuccess\",\n  variantError: componentName$1 + \"-variantError\",\n  variantInfo: componentName$1 + \"-variantInfo\",\n  variantWarning: componentName$1 + \"-variantWarning\",\n  message: componentName$1 + \"-message\",\n  action: componentName$1 + \"-action\",\n  wrappedRoot: componentName$1 + \"-wrappedRoot\"\n};\nvar StyledSnackbar = /*#__PURE__*/(0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(Snackbar)(function (_ref) {\n  var _ref2;\n\n  var theme = _ref.theme;\n  var mode = theme.palette.mode || theme.palette.type;\n  var backgroundColor = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_8__.emphasize)(theme.palette.background[\"default\"], mode === 'light' ? 0.8 : 0.98);\n  return _ref2 = {}, _ref2[\"&.\" + classes$1.wrappedRoot] = {\n    position: 'relative',\n    transform: 'translateX(0)',\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }, _ref2[\".\" + classes$1.contentRoot] = _extends({}, theme.typography.body2, {\n    backgroundColor: backgroundColor,\n    color: theme.palette.getContrastText(backgroundColor),\n    alignItems: 'center',\n    padding: '6px 16px',\n    borderRadius: '4px',\n    boxShadow: '0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)'\n  }), _ref2[\".\" + classes$1.lessPadding] = {\n    paddingLeft: 8 * 2.5\n  }, _ref2[\".\" + classes$1.variantSuccess] = {\n    backgroundColor: '#43a047',\n    color: '#fff'\n  }, _ref2[\".\" + classes$1.variantError] = {\n    backgroundColor: '#d32f2f',\n    color: '#fff'\n  }, _ref2[\".\" + classes$1.variantInfo] = {\n    backgroundColor: '#2196f3',\n    color: '#fff'\n  }, _ref2[\".\" + classes$1.variantWarning] = {\n    backgroundColor: '#ff9800',\n    color: '#fff'\n  }, _ref2[\".\" + classes$1.message] = {\n    display: 'flex',\n    alignItems: 'center',\n    padding: '8px 0'\n  }, _ref2[\".\" + classes$1.action] = {\n    display: 'flex',\n    alignItems: 'center',\n    marginLeft: 'auto',\n    paddingLeft: 16,\n    marginRight: -8\n  }, _ref2;\n});\n\nvar SnackbarItem = function SnackbarItem(_ref3) {\n  var propClasses = _ref3.classes,\n      props = _objectWithoutPropertiesLoose(_ref3, [\"classes\"]);\n\n  var timeout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true),\n      collapsed = _useState[0],\n      setCollapsed = _useState[1];\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    return function () {\n      if (timeout.current) {\n        clearTimeout(timeout.current);\n      }\n    };\n  }, []);\n  var handleClose = createChainedFunction([props.snack.onClose, props.onClose], props.snack.key);\n\n  var handleEntered = function handleEntered() {\n    if (props.snack.requestClose) {\n      handleClose(null, REASONS.INSTRCUTED);\n    }\n  };\n\n  var handleExitedScreen = function handleExitedScreen() {\n    timeout.current = setTimeout(function () {\n      setCollapsed(!collapsed);\n    }, 125);\n  };\n\n  var style = props.style,\n      otherAriaAttributes = props.ariaAttributes,\n      otherClassName = props.className,\n      hideIconVariant = props.hideIconVariant,\n      iconVariant = props.iconVariant,\n      snack = props.snack,\n      otherAction = props.action,\n      otherContent = props.content,\n      otherTranComponent = props.TransitionComponent,\n      otherTranProps = props.TransitionProps,\n      otherTranDuration = props.transitionDuration,\n      other = _objectWithoutPropertiesLoose(props, [\"style\", \"dense\", \"ariaAttributes\", \"className\", \"hideIconVariant\", \"iconVariant\", \"snack\", \"action\", \"content\", \"TransitionComponent\", \"TransitionProps\", \"transitionDuration\", \"onEnter\", \"onEntered\", \"onEntering\", \"onExit\", \"onExited\", \"onExiting\"]);\n\n  var key = snack.key,\n      open = snack.open,\n      singleClassName = snack.className,\n      variant = snack.variant,\n      singleContent = snack.content,\n      singleAction = snack.action,\n      singleAriaAttributes = snack.ariaAttributes,\n      anchorOrigin = snack.anchorOrigin,\n      snackMessage = snack.message,\n      singleTranComponent = snack.TransitionComponent,\n      singleTranProps = snack.TransitionProps,\n      singleTranDuration = snack.transitionDuration,\n      singleSnackProps = _objectWithoutPropertiesLoose(snack, [\"persist\", \"key\", \"open\", \"entered\", \"requestClose\", \"className\", \"variant\", \"content\", \"action\", \"ariaAttributes\", \"anchorOrigin\", \"message\", \"TransitionComponent\", \"TransitionProps\", \"transitionDuration\", \"onEnter\", \"onEntered\", \"onEntering\", \"onExit\", \"onExited\", \"onExiting\"]);\n\n  var icon = _extends({}, defaultIconVariants, {}, iconVariant)[variant];\n\n  var ariaAttributes = _extends({\n    'aria-describedby': 'notistack-snackbar'\n  }, objectMerge(singleAriaAttributes, otherAriaAttributes));\n\n  var TransitionComponent = singleTranComponent || otherTranComponent || DEFAULTS.TransitionComponent;\n  var transitionDuration = objectMerge(singleTranDuration, otherTranDuration, DEFAULTS.transitionDuration);\n\n  var transitionProps = _extends({\n    direction: getTransitionDirection(anchorOrigin)\n  }, objectMerge(singleTranProps, otherTranProps));\n\n  var action = singleAction || otherAction;\n\n  if (typeof action === 'function') {\n    action = action(key);\n  }\n\n  var content = singleContent || otherContent;\n\n  if (typeof content === 'function') {\n    content = content(key, snack.message);\n  } // eslint-disable-next-line operator-linebreak\n\n\n  var callbacks = ['onEnter', 'onEntering', 'onEntered', 'onExit', 'onExiting', 'onExited'].reduce(function (acc, cbName) {\n    var _extends2;\n\n    return _extends({}, acc, (_extends2 = {}, _extends2[cbName] = createChainedFunction([props.snack[cbName], props[cbName]], props.snack.key), _extends2));\n  }, {});\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_mui_material_Collapse__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n    unmountOnExit: true,\n    timeout: 175,\n    \"in\": collapsed,\n    onExited: callbacks.onExited\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(StyledSnackbar, Object.assign({}, other, singleSnackProps, {\n    open: open,\n    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(propClasses.root, classes$1.wrappedRoot, propClasses[transformer.toAnchorOrigin(anchorOrigin)]),\n    onClose: handleClose\n  }), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(TransitionComponent, Object.assign({\n    appear: true,\n    \"in\": open,\n    timeout: transitionDuration\n  }, transitionProps, {\n    onExit: callbacks.onExit,\n    onExiting: callbacks.onExiting,\n    onExited: handleExitedScreen,\n    onEnter: callbacks.onEnter,\n    onEntering: callbacks.onEntering,\n    // order matters. first callbacks.onEntered to set entered: true,\n    // then handleEntered to check if there's a request for closing\n    onEntered: createChainedFunction([callbacks.onEntered, handleEntered])\n  }), content || react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SnackbarContent, Object.assign({}, ariaAttributes, {\n    role: \"alert\",\n    style: style,\n    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(classes$1.contentRoot, classes$1[transformer.toVariant(variant)], propClasses[transformer.toVariant(variant)], otherClassName, singleClassName, !hideIconVariant && icon && classes$1.lessPadding)\n  }), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: ariaAttributes['aria-describedby'],\n    className: classes$1.message\n  }, !hideIconVariant ? icon : null, snackMessage), action && react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: classes$1.action\n  }, action)))));\n};\n\nvar collapse = {\n  // Material-UI 4.12.x and above uses MuiCollapse-root; earlier versions use\n  // Mui-Collapse-container.  https://github.com/mui-org/material-ui/pull/24084\n  container: '& > .MuiCollapse-container, & > .MuiCollapse-root',\n  wrapper: '& > .MuiCollapse-container > .MuiCollapse-wrapper, & > .MuiCollapse-root > .MuiCollapse-wrapper'\n};\nvar xsWidthMargin = 16;\nvar componentName$2 = 'SnackbarContainer';\nvar classes$2 = {\n  root: componentName$2 + \"-root\",\n  rootDense: componentName$2 + \"-rootDense\",\n  top: componentName$2 + \"-top\",\n  bottom: componentName$2 + \"-bottom\",\n  left: componentName$2 + \"-left\",\n  right: componentName$2 + \"-right\",\n  center: componentName$2 + \"-center\"\n};\nvar Root$1 = /*#__PURE__*/(0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__[\"default\"])('div')(function (_ref) {\n  var _ref2, _ref3, _ref4, _ref5, _ref6, _ref7;\n\n  var theme = _ref.theme;\n  return _ref7 = {}, _ref7[\"&.\" + classes$2.root] = (_ref2 = {\n    boxSizing: 'border-box',\n    display: 'flex',\n    maxHeight: '100%',\n    position: 'fixed',\n    zIndex: theme.zIndex.snackbar,\n    height: 'auto',\n    width: 'auto',\n    transition: 'top 300ms ease 0ms, right 300ms ease 0ms, bottom 300ms ease 0ms, left 300ms ease 0ms, margin 300ms ease 0ms, max-width 300ms ease 0ms',\n    // container itself is invisible and should not block clicks, clicks should be passed to its children\n    pointerEvents: 'none'\n  }, _ref2[collapse.container] = {\n    pointerEvents: 'all'\n  }, _ref2[collapse.wrapper] = {\n    padding: SNACKBAR_INDENTS.snackbar[\"default\"] + \"px 0px\",\n    transition: 'padding 300ms ease 0ms'\n  }, _ref2.maxWidth = \"calc(100% - \" + SNACKBAR_INDENTS.view[\"default\"] * 2 + \"px)\", _ref2[theme.breakpoints.down('sm')] = {\n    width: '100%',\n    maxWidth: \"calc(100% - \" + xsWidthMargin * 2 + \"px)\"\n  }, _ref2), _ref7[\"&.\" + classes$2.rootDense] = (_ref3 = {}, _ref3[collapse.wrapper] = {\n    padding: SNACKBAR_INDENTS.snackbar.dense + \"px 0px\"\n  }, _ref3), _ref7[\"&.\" + classes$2.top] = {\n    top: SNACKBAR_INDENTS.view[\"default\"] - SNACKBAR_INDENTS.snackbar[\"default\"],\n    flexDirection: 'column'\n  }, _ref7[\"&.\" + classes$2.bottom] = {\n    bottom: SNACKBAR_INDENTS.view[\"default\"] - SNACKBAR_INDENTS.snackbar[\"default\"],\n    flexDirection: 'column-reverse'\n  }, _ref7[\"&.\" + classes$2.left] = (_ref4 = {\n    left: SNACKBAR_INDENTS.view[\"default\"]\n  }, _ref4[theme.breakpoints.up('sm')] = {\n    alignItems: 'flex-start'\n  }, _ref4[theme.breakpoints.down('sm')] = {\n    left: xsWidthMargin + \"px\"\n  }, _ref4), _ref7[\"&.\" + classes$2.right] = (_ref5 = {\n    right: SNACKBAR_INDENTS.view[\"default\"]\n  }, _ref5[theme.breakpoints.up('sm')] = {\n    alignItems: 'flex-end'\n  }, _ref5[theme.breakpoints.down('sm')] = {\n    right: xsWidthMargin + \"px\"\n  }, _ref5), _ref7[\"&.\" + classes$2.center] = (_ref6 = {\n    left: '50%',\n    transform: 'translateX(-50%)'\n  }, _ref6[theme.breakpoints.up('sm')] = {\n    alignItems: 'center'\n  }, _ref6), _ref7;\n});\n\nvar SnackbarContainer = function SnackbarContainer(props) {\n  var className = props.className,\n      anchorOrigin = props.anchorOrigin,\n      dense = props.dense,\n      other = _objectWithoutPropertiesLoose(props, [\"className\", \"anchorOrigin\", \"dense\"]);\n\n  var combinedClassname = (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(classes$2[anchorOrigin.vertical], classes$2[anchorOrigin.horizontal], classes$2.root, // root should come after others to override maxWidth\n  className, dense && classes$2.rootDense);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Root$1, Object.assign({\n    className: combinedClassname\n  }, other));\n};\n\nvar SnackbarContainer$1 = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().memo(SnackbarContainer);\n\n/* eslint-disable */\nvar __DEV__ = \"development\" !== 'production';\n\nvar warning = (function (message) {\n  if (!__DEV__) return;\n\n  if (typeof console !== 'undefined') {\n    console.error(message);\n  }\n\n  try {\n    throw new Error(message);\n  } catch (x) {}\n});\n\nvar SnackbarProvider = /*#__PURE__*/function (_Component) {\n  _inheritsLoose(SnackbarProvider, _Component);\n\n  function SnackbarProvider(props) {\n    var _this;\n\n    _this = _Component.call(this, props) || this;\n    /**\r\n     * Adds a new snackbar to the queue to be presented.\r\n     * Returns generated or user defined key referencing the new snackbar or null\r\n     */\n\n    _this.enqueueSnackbar = function (message, opts) {\n      if (opts === void 0) {\n        opts = {};\n      }\n\n      var _opts = opts,\n          key = _opts.key,\n          preventDuplicate = _opts.preventDuplicate,\n          options = _objectWithoutPropertiesLoose(_opts, [\"key\", \"preventDuplicate\"]);\n\n      var hasSpecifiedKey = isDefined(key);\n      var id = hasSpecifiedKey ? key : new Date().getTime() + Math.random();\n      var merger = merge(options, _this.props, DEFAULTS);\n\n      var snack = _extends({\n        key: id\n      }, options, {\n        message: message,\n        open: true,\n        entered: false,\n        requestClose: false,\n        variant: merger('variant'),\n        anchorOrigin: merger('anchorOrigin'),\n        autoHideDuration: merger('autoHideDuration')\n      });\n\n      if (options.persist) {\n        snack.autoHideDuration = undefined;\n      }\n\n      _this.setState(function (state) {\n        if (preventDuplicate === undefined && _this.props.preventDuplicate || preventDuplicate) {\n          var compareFunction = function compareFunction(item) {\n            return hasSpecifiedKey ? item.key === key : item.message === message;\n          };\n\n          var inQueue = state.queue.findIndex(compareFunction) > -1;\n          var inView = state.snacks.findIndex(compareFunction) > -1;\n\n          if (inQueue || inView) {\n            return state;\n          }\n        }\n\n        return _this.handleDisplaySnack(_extends({}, state, {\n          queue: [].concat(state.queue, [snack])\n        }));\n      });\n\n      return id;\n    };\n    /**\r\n     * Reducer: Display snack if there's space for it. Otherwise, immediately\r\n     * begin dismissing the oldest message to start showing the new one.\r\n     */\n\n\n    _this.handleDisplaySnack = function (state) {\n      var snacks = state.snacks;\n\n      if (snacks.length >= _this.maxSnack) {\n        return _this.handleDismissOldest(state);\n      }\n\n      return _this.processQueue(state);\n    };\n    /**\r\n     * Reducer: Display items (notifications) in the queue if there's space for them.\r\n     */\n\n\n    _this.processQueue = function (state) {\n      var queue = state.queue,\n          snacks = state.snacks;\n\n      if (queue.length > 0) {\n        return _extends({}, state, {\n          snacks: [].concat(snacks, [queue[0]]),\n          queue: queue.slice(1, queue.length)\n        });\n      }\n\n      return state;\n    };\n    /**\r\n     * Reducer: Hide oldest snackbar on the screen because there exists a new one which we have to display.\r\n     * (ignoring the one with 'persist' flag. i.e. explicitly told by user not to get dismissed).\r\n     *\r\n     * Note 1: If there is already a message leaving the screen, no new messages are dismissed.\r\n     * Note 2: If the oldest message has not yet entered the screen, only a request to close the\r\n     *         snackbar is made. Once it entered the screen, it will be immediately dismissed.\r\n     */\n\n\n    _this.handleDismissOldest = function (state) {\n      if (state.snacks.some(function (item) {\n        return !item.open || item.requestClose;\n      })) {\n        return state;\n      }\n\n      var popped = false;\n      var ignore = false;\n      var persistentCount = state.snacks.reduce(function (acc, current) {\n        return acc + (current.open && current.persist ? 1 : 0);\n      }, 0);\n\n      if (persistentCount === _this.maxSnack) {\n         true ? warning(MESSAGES.NO_PERSIST_ALL) : 0;\n        ignore = true;\n      }\n\n      var snacks = state.snacks.map(function (item) {\n        if (!popped && (!item.persist || ignore)) {\n          popped = true;\n\n          if (!item.entered) {\n            return _extends({}, item, {\n              requestClose: true\n            });\n          }\n\n          if (item.onClose) item.onClose(null, REASONS.MAXSNACK, item.key);\n          if (_this.props.onClose) _this.props.onClose(null, REASONS.MAXSNACK, item.key);\n          return _extends({}, item, {\n            open: false\n          });\n        }\n\n        return _extends({}, item);\n      });\n      return _extends({}, state, {\n        snacks: snacks\n      });\n    };\n    /**\r\n     * Set the entered state of the snackbar with the given key.\r\n     */\n\n\n    _this.handleEnteredSnack = function (node, isAppearing, key) {\n      if (!isDefined(key)) {\n        throw new Error('handleEnteredSnack Cannot be called with undefined key');\n      }\n\n      _this.setState(function (_ref) {\n        var snacks = _ref.snacks;\n        return {\n          snacks: snacks.map(function (item) {\n            return item.key === key ? _extends({}, item, {\n              entered: true\n            }) : _extends({}, item);\n          })\n        };\n      });\n    };\n    /**\r\n     * Hide a snackbar after its timeout.\r\n     */\n\n\n    _this.handleCloseSnack = function (event, reason, key) {\n      // should not use createChainedFunction for onClose.\n      // because this.closeSnackbar called this function\n      if (_this.props.onClose) {\n        _this.props.onClose(event, reason, key);\n      }\n\n      if (reason === REASONS.CLICKAWAY) return;\n      var shouldCloseAll = key === undefined;\n\n      _this.setState(function (_ref2) {\n        var snacks = _ref2.snacks,\n            queue = _ref2.queue;\n        return {\n          snacks: snacks.map(function (item) {\n            if (!shouldCloseAll && item.key !== key) {\n              return _extends({}, item);\n            }\n\n            return item.entered ? _extends({}, item, {\n              open: false\n            }) : _extends({}, item, {\n              requestClose: true\n            });\n          }),\n          queue: queue.filter(function (item) {\n            return item.key !== key;\n          })\n        };\n      });\n    };\n    /**\r\n     * Close snackbar with the given key\r\n     */\n\n\n    _this.closeSnackbar = function (key) {\n      // call individual snackbar onClose callback passed through options parameter\n      var toBeClosed = _this.state.snacks.find(function (item) {\n        return item.key === key;\n      });\n\n      if (isDefined(key) && toBeClosed && toBeClosed.onClose) {\n        toBeClosed.onClose(null, REASONS.INSTRUCTED, key);\n      }\n\n      _this.handleCloseSnack(null, REASONS.INSTRUCTED, key);\n    };\n    /**\r\n     * When we set open attribute of a snackbar to false (i.e. after we hide a snackbar),\r\n     * it leaves the screen and immediately after leaving animation is done, this method\r\n     * gets called. We remove the hidden snackbar from state and then display notifications\r\n     * waiting in the queue (if any). If after this process the queue is not empty, the\r\n     * oldest message is dismissed.\r\n     */\n    // @ts-ignore\n\n\n    _this.handleExitedSnack = function (event, key1, key2) {\n      var key = key1 || key2;\n\n      if (!isDefined(key)) {\n        throw new Error('handleExitedSnack Cannot be called with undefined key');\n      }\n\n      _this.setState(function (state) {\n        var newState = _this.processQueue(_extends({}, state, {\n          snacks: state.snacks.filter(function (item) {\n            return item.key !== key;\n          })\n        }));\n\n        if (newState.queue.length === 0) {\n          return newState;\n        }\n\n        return _this.handleDismissOldest(newState);\n      });\n    };\n\n    _this.state = {\n      snacks: [],\n      queue: [],\n      contextValue: {\n        enqueueSnackbar: _this.enqueueSnackbar.bind(_assertThisInitialized(_this)),\n        closeSnackbar: _this.closeSnackbar.bind(_assertThisInitialized(_this))\n      }\n    };\n    return _this;\n  }\n\n  var _proto = SnackbarProvider.prototype;\n\n  _proto.render = function render() {\n    var _this2 = this;\n\n    var contextValue = this.state.contextValue;\n\n    var _this$props = this.props,\n        iconVariant = _this$props.iconVariant,\n        _this$props$dense = _this$props.dense,\n        dense = _this$props$dense === void 0 ? DEFAULTS.dense : _this$props$dense,\n        _this$props$hideIconV = _this$props.hideIconVariant,\n        hideIconVariant = _this$props$hideIconV === void 0 ? DEFAULTS.hideIconVariant : _this$props$hideIconV,\n        domRoot = _this$props.domRoot,\n        children = _this$props.children,\n        _this$props$classes = _this$props.classes,\n        classes = _this$props$classes === void 0 ? {} : _this$props$classes,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"maxSnack\", \"preventDuplicate\", \"variant\", \"anchorOrigin\", \"iconVariant\", \"dense\", \"hideIconVariant\", \"domRoot\", \"children\", \"classes\"]);\n\n    var categ = this.state.snacks.reduce(function (acc, current) {\n      var _extends2;\n\n      var category = originKeyExtractor(current.anchorOrigin);\n      var existingOfCategory = acc[category] || [];\n      return _extends({}, acc, (_extends2 = {}, _extends2[category] = [].concat(existingOfCategory, [current]), _extends2));\n    }, {});\n    var snackbars = Object.keys(categ).map(function (origin) {\n      var snacks = categ[origin];\n      return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SnackbarContainer$1, {\n        key: origin,\n        dense: dense,\n        anchorOrigin: snacks[0].anchorOrigin,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(classes.containerRoot, classes[transformer.toContainerAnchorOrigin(origin)])\n      }, snacks.map(function (snack) {\n        return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SnackbarItem, Object.assign({}, props, {\n          key: snack.key,\n          snack: snack,\n          dense: dense,\n          iconVariant: iconVariant,\n          hideIconVariant: hideIconVariant,\n          classes: omitContainerKeys(classes),\n          onClose: _this2.handleCloseSnack,\n          onExited: createChainedFunction([_this2.handleExitedSnack, _this2.props.onExited]),\n          onEntered: createChainedFunction([_this2.handleEnteredSnack, _this2.props.onEntered])\n        }));\n      }));\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SnackbarContext.Provider, {\n      value: contextValue\n    }, children, domRoot ? (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(snackbars, domRoot) : snackbars);\n  };\n\n  _createClass(SnackbarProvider, [{\n    key: \"maxSnack\",\n    get: function get() {\n      return this.props.maxSnack || DEFAULTS.maxSnack;\n    }\n  }]);\n\n  return SnackbarProvider;\n}(react__WEBPACK_IMPORTED_MODULE_0__.Component);\n\n// https://github.com/JamesMGreene/Function.name/blob/58b314d4a983110c3682f1228f845d39ccca1817/Function.name.js#L3\nvar fnNameMatchRegex = /^\\s*function(?:\\s|\\s*\\/\\*.*\\*\\/\\s*)+([^(\\s/]*)\\s*/;\n\nvar getFunctionName = function getFunctionName(fn) {\n  var match = (\"\" + fn).match(fnNameMatchRegex);\n  var name = match && match[1];\n  return name || '';\n};\n/**\n * @param {function} Component\n * @param {string} fallback\n * @returns {string | undefined}\n */\n\n\nvar getFunctionComponentName = function getFunctionComponentName(Component, fallback) {\n  if (fallback === void 0) {\n    fallback = '';\n  }\n\n  return Component.displayName || Component.name || getFunctionName(Component) || fallback;\n};\n\nvar getWrappedName = function getWrappedName(outerType, innerType, wrapperName) {\n  var functionName = getFunctionComponentName(innerType);\n  return outerType.displayName || (functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName);\n};\n/** \n * From react-is\n * @link https://github.com/facebook/react/blob/master/packages/shared/ReactSymbols.js\n */\n\n\nvar ForwardRef = function ForwardRef() {\n  var symbolFor = typeof Symbol === 'function' && Symbol[\"for\"];\n  return symbolFor ? symbolFor('react.forward_ref') : 0xead0;\n};\n/**\n * https://github.com/facebook/react/blob/769b1f270e1251d9dbdce0fcbd9e92e502d059b8/packages/shared/getComponentName.js\n *\n * @param {React.ReactType} Component\n * @returns {string | undefined}\n */\n\n\nvar getDisplayName = (function (Component) {\n  if (Component == null) {\n    return undefined;\n  }\n\n  if (typeof Component === 'string') {\n    return Component;\n  }\n\n  if (typeof Component === 'function') {\n    return getFunctionComponentName(Component, 'Component');\n  }\n\n  if (typeof Component === 'object') {\n    switch (Component.$$typeof) {\n      case ForwardRef():\n        return getWrappedName(Component, Component.render, 'ForwardRef');\n\n      default:\n        return undefined;\n    }\n  }\n\n  return undefined;\n});\n\nvar withSnackbar = function withSnackbar(Component) {\n  var WrappedComponent = react__WEBPACK_IMPORTED_MODULE_0___default().forwardRef(function (props, ref) {\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SnackbarContext.Consumer, null, function (context) {\n      return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Component, _extends({}, props, {\n        ref: ref,\n        enqueueSnackbar: context.enqueueSnackbar,\n        closeSnackbar: context.closeSnackbar\n      }));\n    });\n  });\n\n  if (true) {\n    WrappedComponent.displayName = \"WithSnackbar(\" + getDisplayName(Component) + \")\";\n  }\n\n  hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default()(WrappedComponent, Component);\n  return WrappedComponent;\n};\n\nvar useSnackbar = (function () {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SnackbarContext);\n});\n\n\n//# sourceMappingURL=notistack.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/notistack/dist/notistack.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/notistack/node_modules/clsx/dist/clsx.m.js":
/*!*****************************************************************!*\
  !*** ./node_modules/notistack/node_modules/clsx/dist/clsx.m.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f);else for(t in e)e[t]&&(n&&(n+=\" \"),n+=t);return n}function clsx(){for(var e,t,f=0,n=\"\";f<arguments.length;)(e=arguments[f++])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clsx);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbm90aXN0YWNrL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsY0FBYyxhQUFhLCtDQUErQyx1REFBdUQsV0FBVywwQ0FBMEMseUNBQXlDLFNBQWdCLGdCQUFnQixxQkFBcUIsbUJBQW1CLGtEQUFrRCxTQUFTLGlFQUFlLElBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL25vdGlzdGFjay9ub2RlX21vZHVsZXMvY2xzeC9kaXN0L2Nsc3gubS5qcz9hMjVkIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHIoZSl7dmFyIHQsZixuPVwiXCI7aWYoXCJzdHJpbmdcIj09dHlwZW9mIGV8fFwibnVtYmVyXCI9PXR5cGVvZiBlKW4rPWU7ZWxzZSBpZihcIm9iamVjdFwiPT10eXBlb2YgZSlpZihBcnJheS5pc0FycmF5KGUpKWZvcih0PTA7dDxlLmxlbmd0aDt0KyspZVt0XSYmKGY9cihlW3RdKSkmJihuJiYobis9XCIgXCIpLG4rPWYpO2Vsc2UgZm9yKHQgaW4gZSllW3RdJiYobiYmKG4rPVwiIFwiKSxuKz10KTtyZXR1cm4gbn1leHBvcnQgZnVuY3Rpb24gY2xzeCgpe2Zvcih2YXIgZSx0LGY9MCxuPVwiXCI7Zjxhcmd1bWVudHMubGVuZ3RoOykoZT1hcmd1bWVudHNbZisrXSkmJih0PXIoZSkpJiYobiYmKG4rPVwiIFwiKSxuKz10KTtyZXR1cm4gbn1leHBvcnQgZGVmYXVsdCBjbHN4OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/notistack/node_modules/clsx/dist/clsx.m.js\n");

/***/ })

};
;