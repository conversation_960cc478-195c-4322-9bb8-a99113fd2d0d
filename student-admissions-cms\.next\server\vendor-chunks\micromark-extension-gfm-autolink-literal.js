/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-autolink-literal";
exports.ids = ["vendor-chunks/micromark-extension-gfm-autolink-literal"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-autolink-literal/index.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./syntax */ \"(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/syntax.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tYXV0b2xpbmstbGl0ZXJhbC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSwrSEFBb0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay1leHRlbnNpb24tZ2ZtLWF1dG9saW5rLWxpdGVyYWwvaW5kZXguanM/MDRhMCJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vc3ludGF4JylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/syntax.js":
/*!*************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-autolink-literal/syntax.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var asciiAlpha = __webpack_require__(/*! micromark/dist/character/ascii-alpha */ \"(ssr)/./node_modules/micromark/dist/character/ascii-alpha.js\")\nvar asciiAlphanumeric = __webpack_require__(/*! micromark/dist/character/ascii-alphanumeric */ \"(ssr)/./node_modules/micromark/dist/character/ascii-alphanumeric.js\")\nvar asciiControl = __webpack_require__(/*! micromark/dist/character/ascii-control */ \"(ssr)/./node_modules/micromark/dist/character/ascii-control.js\")\nvar markdownLineEnding = __webpack_require__(/*! micromark/dist/character/markdown-line-ending */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar unicodePunctuation = __webpack_require__(/*! micromark/dist/character/unicode-punctuation */ \"(ssr)/./node_modules/micromark/dist/character/unicode-punctuation.js\")\nvar unicodeWhitespace = __webpack_require__(/*! micromark/dist/character/unicode-whitespace */ \"(ssr)/./node_modules/micromark/dist/character/unicode-whitespace.js\")\n\nvar www = {tokenize: tokenizeWww, partial: true}\nvar domain = {tokenize: tokenizeDomain, partial: true}\nvar path = {tokenize: tokenizePath, partial: true}\nvar punctuation = {tokenize: tokenizePunctuation, partial: true}\nvar namedCharacterReference = {\n  tokenize: tokenizeNamedCharacterReference,\n  partial: true\n}\n\nvar wwwAutolink = {tokenize: tokenizeWwwAutolink, previous: previousWww}\nvar httpAutolink = {tokenize: tokenizeHttpAutolink, previous: previousHttp}\nvar emailAutolink = {tokenize: tokenizeEmailAutolink, previous: previousEmail}\n\nvar text = {}\n\n// Export hooked constructs.\nexports.text = text\n\n// `0`\nvar code = 48\n\n// While the code is smaller than `{`.\nwhile (code < 123) {\n  text[code] = emailAutolink\n  code++\n  // Jump from `:` -> `A`\n  if (code === 58) code = 65\n  // Jump from `[` -> `a`\n  else if (code === 91) code = 97\n}\n\n// `+`\ntext[43] = emailAutolink\n// `-`\ntext[45] = emailAutolink\n// `.`\ntext[46] = emailAutolink\n// `_`\ntext[95] = emailAutolink\n// `h`.\ntext[72] = [emailAutolink, httpAutolink]\ntext[104] = [emailAutolink, httpAutolink]\n// `w`.\ntext[87] = [emailAutolink, wwwAutolink]\ntext[119] = [emailAutolink, wwwAutolink]\n\nfunction tokenizeEmailAutolink(effects, ok, nok) {\n  var self = this\n  var hasDot\n\n  return start\n\n  function start(code) {\n    /* istanbul ignore next - hooks. */\n    if (\n      !gfmAtext(code) ||\n      !previousEmail(self.previous) ||\n      previous(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkEmail')\n    return atext(code)\n  }\n\n  function atext(code) {\n    if (gfmAtext(code)) {\n      effects.consume(code)\n      return atext\n    }\n\n    // `@`\n    if (code === 64) {\n      effects.consume(code)\n      return label\n    }\n\n    return nok(code)\n  }\n\n  function label(code) {\n    // `.`\n    if (code === 46) {\n      return effects.check(punctuation, done, dotContinuation)(code)\n    }\n\n    if (\n      // `-`\n      code === 45 ||\n      // `_`\n      code === 95\n    ) {\n      return effects.check(punctuation, nok, dashOrUnderscoreContinuation)(code)\n    }\n\n    if (asciiAlphanumeric(code)) {\n      effects.consume(code)\n      return label\n    }\n\n    return done(code)\n  }\n\n  function dotContinuation(code) {\n    effects.consume(code)\n    hasDot = true\n    return label\n  }\n\n  function dashOrUnderscoreContinuation(code) {\n    effects.consume(code)\n    return afterDashOrUnderscore\n  }\n\n  function afterDashOrUnderscore(code) {\n    // `.`\n    if (code === 46) {\n      return effects.check(punctuation, nok, dotContinuation)(code)\n    }\n\n    return label(code)\n  }\n\n  function done(code) {\n    if (hasDot) {\n      effects.exit('literalAutolinkEmail')\n      effects.exit('literalAutolink')\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\nfunction tokenizeWwwAutolink(effects, ok, nok) {\n  var self = this\n\n  return start\n\n  function start(code) {\n    /* istanbul ignore next - hooks. */\n    if (\n      (code !== 87 && code - 32 !== 87) ||\n      !previousWww(self.previous) ||\n      previous(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkWww')\n    // For `www.` we check instead of attempt, because when it matches, GH\n    // treats it as part of a domain (yes, it says a valid domain must come\n    // after `www.`, but that’s not how it’s implemented by them).\n    return effects.check(\n      www,\n      effects.attempt(domain, effects.attempt(path, done), nok),\n      nok\n    )(code)\n  }\n\n  function done(code) {\n    effects.exit('literalAutolinkWww')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\nfunction tokenizeHttpAutolink(effects, ok, nok) {\n  var self = this\n\n  return start\n\n  function start(code) {\n    /* istanbul ignore next - hooks. */\n    if (\n      (code !== 72 && code - 32 !== 72) ||\n      !previousHttp(self.previous) ||\n      previous(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkHttp')\n    effects.consume(code)\n    return t1\n  }\n\n  function t1(code) {\n    // `t`\n    if (code === 84 || code - 32 === 84) {\n      effects.consume(code)\n      return t2\n    }\n\n    return nok(code)\n  }\n\n  function t2(code) {\n    // `t`\n    if (code === 84 || code - 32 === 84) {\n      effects.consume(code)\n      return p\n    }\n\n    return nok(code)\n  }\n\n  function p(code) {\n    // `p`\n    if (code === 80 || code - 32 === 80) {\n      effects.consume(code)\n      return s\n    }\n\n    return nok(code)\n  }\n\n  function s(code) {\n    // `s`\n    if (code === 83 || code - 32 === 83) {\n      effects.consume(code)\n      return colon\n    }\n\n    return colon(code)\n  }\n\n  function colon(code) {\n    // `:`\n    if (code === 58) {\n      effects.consume(code)\n      return slash1\n    }\n\n    return nok(code)\n  }\n\n  function slash1(code) {\n    // `/`\n    if (code === 47) {\n      effects.consume(code)\n      return slash2\n    }\n\n    return nok(code)\n  }\n\n  function slash2(code) {\n    // `/`\n    if (code === 47) {\n      effects.consume(code)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  function after(code) {\n    return asciiControl(code) ||\n      unicodeWhitespace(code) ||\n      unicodePunctuation(code)\n      ? nok(code)\n      : effects.attempt(domain, effects.attempt(path, done), nok)(code)\n  }\n\n  function done(code) {\n    effects.exit('literalAutolinkHttp')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\nfunction tokenizeWww(effects, ok, nok) {\n  return start\n\n  function start(code) {\n    // Assume a `w`.\n    effects.consume(code)\n    return w2\n  }\n\n  function w2(code) {\n    // `w`\n    if (code === 87 || code - 32 === 87) {\n      effects.consume(code)\n      return w3\n    }\n\n    return nok(code)\n  }\n\n  function w3(code) {\n    // `w`\n    if (code === 87 || code - 32 === 87) {\n      effects.consume(code)\n      return dot\n    }\n\n    return nok(code)\n  }\n\n  function dot(code) {\n    // `.`\n    if (code === 46) {\n      effects.consume(code)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  function after(code) {\n    return code === null || markdownLineEnding(code) ? nok(code) : ok(code)\n  }\n}\n\nfunction tokenizeDomain(effects, ok, nok) {\n  var hasUnderscoreInLastSegment\n  var hasUnderscoreInLastLastSegment\n\n  return domain\n\n  function domain(code) {\n    // `&`\n    if (code === 38) {\n      return effects.check(\n        namedCharacterReference,\n        done,\n        punctuationContinuation\n      )(code)\n    }\n\n    if (code === 46 /* `.` */ || code === 95 /* `_` */) {\n      return effects.check(punctuation, done, punctuationContinuation)(code)\n    }\n\n    // GH documents that only alphanumerics (other than `-`, `.`, and `_`) can\n    // occur, which sounds like ASCII only, but they also support `www.點看.com`,\n    // so that’s Unicode.\n    // Instead of some new production for Unicode alphanumerics, markdown\n    // already has that for Unicode punctuation and whitespace, so use those.\n    if (\n      asciiControl(code) ||\n      unicodeWhitespace(code) ||\n      (code !== 45 /* `-` */ && unicodePunctuation(code))\n    ) {\n      return done(code)\n    }\n\n    effects.consume(code)\n    return domain\n  }\n\n  function punctuationContinuation(code) {\n    // `.`\n    if (code === 46) {\n      hasUnderscoreInLastLastSegment = hasUnderscoreInLastSegment\n      hasUnderscoreInLastSegment = undefined\n      effects.consume(code)\n      return domain\n    }\n\n    // `_`\n    if (code === 95) hasUnderscoreInLastSegment = true\n\n    effects.consume(code)\n    return domain\n  }\n\n  function done(code) {\n    if (!hasUnderscoreInLastLastSegment && !hasUnderscoreInLastSegment) {\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\nfunction tokenizePath(effects, ok) {\n  var balance = 0\n\n  return inPath\n\n  function inPath(code) {\n    // `&`\n    if (code === 38) {\n      return effects.check(\n        namedCharacterReference,\n        ok,\n        continuedPunctuation\n      )(code)\n    }\n\n    // `(`\n    if (code === 40) {\n      balance++\n    }\n\n    // `)`\n    if (code === 41) {\n      return effects.check(\n        punctuation,\n        parenAtPathEnd,\n        continuedPunctuation\n      )(code)\n    }\n\n    if (pathEnd(code)) {\n      return ok(code)\n    }\n\n    if (trailingPunctuation(code)) {\n      return effects.check(punctuation, ok, continuedPunctuation)(code)\n    }\n\n    effects.consume(code)\n    return inPath\n  }\n\n  function continuedPunctuation(code) {\n    effects.consume(code)\n    return inPath\n  }\n\n  function parenAtPathEnd(code) {\n    balance--\n    return balance < 0 ? ok(code) : continuedPunctuation(code)\n  }\n}\n\nfunction tokenizeNamedCharacterReference(effects, ok, nok) {\n  return start\n\n  function start(code) {\n    // Assume an ampersand.\n    effects.consume(code)\n    return inside\n  }\n\n  function inside(code) {\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return inside\n    }\n\n    // `;`\n    if (code === 59) {\n      effects.consume(code)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  function after(code) {\n    // If the named character reference is followed by the end of the path, it’s\n    // not continued punctuation.\n    return pathEnd(code) ? ok(code) : nok(code)\n  }\n}\n\nfunction tokenizePunctuation(effects, ok, nok) {\n  return start\n\n  function start(code) {\n    // Always a valid trailing punctuation marker.\n    effects.consume(code)\n    return after\n  }\n\n  function after(code) {\n    // Check the next.\n    if (trailingPunctuation(code)) {\n      effects.consume(code)\n      return after\n    }\n\n    // If the punctuation marker is followed by the end of the path, it’s not\n    // continued punctuation.\n    return pathEnd(code) ? ok(code) : nok(code)\n  }\n}\n\nfunction trailingPunctuation(code) {\n  return (\n    // `!`\n    code === 33 ||\n    // `\"`\n    code === 34 ||\n    // `'`\n    code === 39 ||\n    // `)`\n    code === 41 ||\n    // `*`\n    code === 42 ||\n    // `,`\n    code === 44 ||\n    // `.`\n    code === 46 ||\n    // `:`\n    code === 58 ||\n    // `;`\n    code === 59 ||\n    // `<`\n    code === 60 ||\n    // `?`\n    code === 63 ||\n    // `_`.\n    code === 95 ||\n    // `~`\n    code === 126\n  )\n}\n\nfunction pathEnd(code) {\n  return (\n    // EOF.\n    code === null ||\n    // CR, LF, CRLF, HT, VS.\n    code < 0 ||\n    // Space.\n    code === 32 ||\n    // `<`\n    code === 60\n  )\n}\n\nfunction gfmAtext(code) {\n  return (\n    code === 43 /* `+` */ ||\n    code === 45 /* `-` */ ||\n    code === 46 /* `.` */ ||\n    code === 95 /* `_` */ ||\n    asciiAlphanumeric(code)\n  )\n}\n\nfunction previousWww(code) {\n  return (\n    code === null ||\n    code < 0 ||\n    code === 32 /* ` ` */ ||\n    code === 40 /* `(` */ ||\n    code === 42 /* `*` */ ||\n    code === 95 /* `_` */ ||\n    code === 126 /* `~` */\n  )\n}\n\nfunction previousHttp(code) {\n  return code === null || !asciiAlpha(code)\n}\n\nfunction previousEmail(code) {\n  return code !== 47 /* `/` */ && previousHttp(code)\n}\n\nfunction previous(events) {\n  var index = events.length\n\n  while (index--) {\n    if (\n      (events[index][1].type === 'labelLink' ||\n        events[index][1].type === 'labelImage') &&\n      !events[index][1]._balanced\n    ) {\n      return true\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/syntax.js\n");

/***/ })

};
;