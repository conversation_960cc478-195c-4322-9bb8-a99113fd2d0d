/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-table";
exports.ids = ["vendor-chunks/mdast-util-gfm-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-table/from-markdown.js":
/*!************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-table/from-markdown.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("exports.enter = {\n  table: enterTable,\n  tableData: enterCell,\n  tableHeader: enterCell,\n  tableRow: enterRow\n}\nexports.exit = {\n  codeText: exitCodeText,\n  table: exitTable,\n  tableData: exit,\n  tableHeader: exit,\n  tableRow: exit\n}\n\nfunction enterTable(token) {\n  this.enter({type: 'table', align: token._align, children: []}, token)\n  this.setData('inTable', true)\n}\n\nfunction exitTable(token) {\n  this.exit(token)\n  this.setData('inTable')\n}\n\nfunction enterRow(token) {\n  this.enter({type: 'tableRow', children: []}, token)\n}\n\nfunction exit(token) {\n  this.exit(token)\n}\n\nfunction enterCell(token) {\n  this.enter({type: 'tableCell', children: []}, token)\n}\n\n// Overwrite the default code text data handler to unescape escaped pipes when\n// they are in tables.\nfunction exitCodeText(token) {\n  var value = this.resume()\n\n  if (this.getData('inTable')) {\n    value = value.replace(/\\\\([\\\\|])/g, replace)\n  }\n\n  this.stack[this.stack.length - 1].value = value\n  this.exit(token)\n}\n\nfunction replace($0, $1) {\n  // Pipes work, backslashes don’t (but can’t escape pipes).\n  return $1 === '|' ? $1 : $0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-table/from-markdown.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm-table/to-markdown.js":
/*!**********************************************************!*\
  !*** ./node_modules/mdast-util-gfm-table/to-markdown.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var phrasing = __webpack_require__(/*! mdast-util-to-markdown/lib/util/container-phrasing */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\")\nvar defaultInlineCode = __webpack_require__(/*! mdast-util-to-markdown/lib/handle/inline-code */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\")\nvar markdownTable = __webpack_require__(/*! markdown-table */ \"(ssr)/./node_modules/markdown-table/index.js\")\n\nmodule.exports = toMarkdown\n\nfunction toMarkdown(options) {\n  var settings = options || {}\n  var padding = settings.tableCellPadding\n  var alignDelimiters = settings.tablePipeAlign\n  var stringLength = settings.stringLength\n  var around = padding ? ' ' : '|'\n\n  return {\n    unsafe: [\n      {character: '\\r', inConstruct: 'tableCell'},\n      {character: '\\n', inConstruct: 'tableCell'},\n      // A pipe, when followed by a tab or space (padding), or a dash or colon\n      // (unpadded delimiter row), could result in a table.\n      {atBreak: true, character: '|', after: '[\\t :-]'},\n      // A pipe in a cell must be encoded.\n      {character: '|', inConstruct: 'tableCell'},\n      // A colon must be followed by a dash, in which case it could start a\n      // delimiter row.\n      {atBreak: true, character: ':', after: '-'},\n      // A delimiter row can also start with a dash, when followed by more\n      // dashes, a colon, or a pipe.\n      // This is a stricter version than the built in check for lists, thematic\n      // breaks, and setex heading underlines though:\n      // <https://github.com/syntax-tree/mdast-util-to-markdown/blob/51a2038/lib/unsafe.js#L57>\n      {atBreak: true, character: '-', after: '[:|-]'}\n    ],\n    handlers: {\n      table: handleTable,\n      tableRow: handleTableRow,\n      tableCell: handleTableCell,\n      inlineCode: inlineCodeWithTable\n    }\n  }\n\n  function handleTable(node, _, context) {\n    return serializeData(handleTableAsData(node, context), node.align)\n  }\n\n  // This function isn’t really used normally, because we handle rows at the\n  // table level.\n  // But, if someone passes in a table row, this ensures we make somewhat sense.\n  function handleTableRow(node, _, context) {\n    var row = handleTableRowAsData(node, context)\n    // `markdown-table` will always add an align row\n    var value = serializeData([row])\n    return value.slice(0, value.indexOf('\\n'))\n  }\n\n  function handleTableCell(node, _, context) {\n    var exit = context.enter('tableCell')\n    var value = phrasing(node, context, {before: around, after: around})\n    exit()\n    return value\n  }\n\n  function serializeData(matrix, align) {\n    return markdownTable(matrix, {\n      align: align,\n      alignDelimiters: alignDelimiters,\n      padding: padding,\n      stringLength: stringLength\n    })\n  }\n\n  function handleTableAsData(node, context) {\n    var children = node.children\n    var index = -1\n    var length = children.length\n    var result = []\n    var subexit = context.enter('table')\n\n    while (++index < length) {\n      result[index] = handleTableRowAsData(children[index], context)\n    }\n\n    subexit()\n\n    return result\n  }\n\n  function handleTableRowAsData(node, context) {\n    var children = node.children\n    var index = -1\n    var length = children.length\n    var result = []\n    var subexit = context.enter('tableRow')\n\n    while (++index < length) {\n      result[index] = handleTableCell(children[index], node, context)\n    }\n\n    subexit()\n\n    return result\n  }\n\n  function inlineCodeWithTable(node, parent, context) {\n    var value = defaultInlineCode(node, parent, context)\n\n    if (context.stack.indexOf('tableCell') !== -1) {\n      value = value.replace(/\\|/g, '\\\\$&')\n    }\n\n    return value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-table/to-markdown.js\n");

/***/ })

};
;