"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-find-and-replace";
exports.ids = ["vendor-chunks/mdast-util-find-and-replace"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-find-and-replace/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/mdast-util-find-and-replace/index.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = findAndReplace\n\nvar visit = __webpack_require__(/*! unist-util-visit-parents */ \"(ssr)/./node_modules/unist-util-visit-parents/index.js\")\nvar convert = __webpack_require__(/*! unist-util-is/convert */ \"(ssr)/./node_modules/unist-util-is/convert.js\")\nvar escape = __webpack_require__(/*! escape-string-regexp */ \"(ssr)/./node_modules/escape-string-regexp/index.js\")\n\nvar splice = [].splice\n\nfunction findAndReplace(tree, find, replace, options) {\n  var settings\n  var schema\n\n  if (typeof find === 'string' || (find && typeof find.exec === 'function')) {\n    schema = [[find, replace]]\n  } else {\n    schema = find\n    options = replace\n  }\n\n  settings = options || {}\n\n  search(tree, settings, handlerFactory(toPairs(schema)))\n\n  return tree\n\n  function handlerFactory(pairs) {\n    var pair = pairs[0]\n\n    return handler\n\n    function handler(node, parent) {\n      var find = pair[0]\n      var replace = pair[1]\n      var nodes = []\n      var start = 0\n      var index = parent.children.indexOf(node)\n      var position\n      var match\n      var subhandler\n      var value\n\n      find.lastIndex = 0\n\n      match = find.exec(node.value)\n\n      while (match) {\n        position = match.index\n        value = replace.apply(\n          null,\n          [].concat(match, {index: match.index, input: match.input})\n        )\n\n        if (value !== false) {\n          if (start !== position) {\n            nodes.push({type: 'text', value: node.value.slice(start, position)})\n          }\n\n          if (typeof value === 'string' && value.length > 0) {\n            value = {type: 'text', value: value}\n          }\n\n          if (value) {\n            nodes = [].concat(nodes, value)\n          }\n\n          start = position + match[0].length\n        }\n\n        if (!find.global) {\n          break\n        }\n\n        match = find.exec(node.value)\n      }\n\n      if (position === undefined) {\n        nodes = [node]\n        index--\n      } else {\n        if (start < node.value.length) {\n          nodes.push({type: 'text', value: node.value.slice(start)})\n        }\n\n        nodes.unshift(index, 1)\n        splice.apply(parent.children, nodes)\n      }\n\n      if (pairs.length > 1) {\n        subhandler = handlerFactory(pairs.slice(1))\n        position = -1\n\n        while (++position < nodes.length) {\n          node = nodes[position]\n\n          if (node.type === 'text') {\n            subhandler(node, parent)\n          } else {\n            search(node, settings, subhandler)\n          }\n        }\n      }\n\n      return index + nodes.length + 1\n    }\n  }\n}\n\nfunction search(tree, settings, handler) {\n  var ignored = convert(settings.ignore || [])\n  var result = []\n\n  visit(tree, 'text', visitor)\n\n  return result\n\n  function visitor(node, parents) {\n    var index = -1\n    var parent\n    var grandparent\n\n    while (++index < parents.length) {\n      parent = parents[index]\n\n      if (\n        ignored(\n          parent,\n          grandparent ? grandparent.children.indexOf(parent) : undefined,\n          grandparent\n        )\n      ) {\n        return\n      }\n\n      grandparent = parent\n    }\n\n    return handler(node, grandparent)\n  }\n}\n\nfunction toPairs(schema) {\n  var result = []\n  var key\n  var index\n\n  if (typeof schema !== 'object') {\n    throw new Error('Expected array or object as schema')\n  }\n\n  if ('length' in schema) {\n    index = -1\n\n    while (++index < schema.length) {\n      result.push([\n        toExpression(schema[index][0]),\n        toFunction(schema[index][1])\n      ])\n    }\n  } else {\n    for (key in schema) {\n      result.push([toExpression(key), toFunction(schema[key])])\n    }\n  }\n\n  return result\n}\n\nfunction toExpression(find) {\n  return typeof find === 'string' ? new RegExp(escape(find), 'g') : find\n}\n\nfunction toFunction(replace) {\n  return typeof replace === 'function' ? replace : returner\n\n  function returner() {\n    return replace\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-find-and-replace/index.js\n");

/***/ })

};
;