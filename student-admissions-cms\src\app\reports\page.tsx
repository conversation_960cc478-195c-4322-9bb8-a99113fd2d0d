'use client';

import React, { useState } from 'react';
import { useList } from '@refinedev/core';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  Alert,
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  Assessment as AssessmentIcon,
  GetApp as DownloadIcon,
  DateRange as DateRangeIcon,
} from '@mui/icons-material';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`reports-tabpanel-${index}`}
      aria-labelledby={`reports-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const ReportsPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedProgram, setSelectedProgram] = useState('all');
  const [selectedYear, setSelectedYear] = useState('2024');
  const [dateRange, setDateRange] = useState({
    start: new Date(new Date().getFullYear(), 0, 1),
    end: new Date(),
  });

  // Mock data - in real implementation, this would come from API
  const { data: applicationsData } = useList({
    resource: 'applications',
    pagination: { pageSize: 1000 },
  });

  const { data: programsData } = useList({
    resource: 'academic_programs',
  });

  const { data: enrollmentsData } = useList({
    resource: 'enrollments',
  });

  // Mock analytics data
  const applicationStatusData = [
    { name: 'New', count: 45, percentage: 15 },
    { name: 'In Review', count: 78, percentage: 26 },
    { name: 'Interview Scheduled', count: 32, percentage: 11 },
    { name: 'Offered', count: 56, percentage: 19 },
    { name: 'Enrolled', count: 67, percentage: 22 },
    { name: 'Rejected', count: 22, percentage: 7 },
  ];

  const programWiseData = [
    { program: 'Computer Science', applications: 120, enrolled: 45, conversion: 37.5 },
    { program: 'Business Admin', applications: 95, enrolled: 38, conversion: 40.0 },
    { program: 'Engineering', applications: 85, enrolled: 32, conversion: 37.6 },
    { program: 'Medicine', applications: 150, enrolled: 25, conversion: 16.7 },
    { program: 'Arts', applications: 60, enrolled: 28, conversion: 46.7 },
  ];

  const monthlyTrendData = [
    { month: 'Jan', applications: 45, enrollments: 12 },
    { month: 'Feb', applications: 52, enrollments: 18 },
    { month: 'Mar', applications: 78, enrollments: 25 },
    { month: 'Apr', applications: 65, enrollments: 22 },
    { month: 'May', applications: 89, enrollments: 35 },
    { month: 'Jun', applications: 95, enrollments: 42 },
    { month: 'Jul', applications: 72, enrollments: 28 },
    { month: 'Aug', applications: 68, enrollments: 31 },
  ];

  const demographicData = [
    { category: 'Male', count: 180, percentage: 60 },
    { category: 'Female', count: 120, percentage: 40 },
  ];

  const sourceData = [
    { source: 'Website', count: 120, percentage: 40 },
    { source: 'Referral', count: 75, percentage: 25 },
    { source: 'Social Media', count: 45, percentage: 15 },
    { source: 'Education Fair', count: 30, percentage: 10 },
    { source: 'Walk-in', count: 30, percentage: 10 },
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  const exportReport = (reportType: string) => {
    console.log(`Exporting ${reportType} report...`);
    // Implementation would generate and download report
  };

  const totalApplications = applicationStatusData.reduce((sum, item) => sum + item.count, 0);
  const totalEnrollments = programWiseData.reduce((sum, item) => sum + item.enrolled, 0);
  const overallConversion = ((totalEnrollments / totalApplications) * 100).toFixed(1);

  return (
    <ProtectedRoute resource="reports" action="read">
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Box>
          {/* Header with Filters */}
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h4">Reports & Analytics</Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Program</InputLabel>
                <Select
                  value={selectedProgram}
                  onChange={(e) => setSelectedProgram(e.target.value)}
                  label="Program"
                >
                  <MenuItem value="all">All Programs</MenuItem>
                  {programsData?.data?.map((program: any) => (
                    <MenuItem key={program.id} value={program.id}>
                      {program.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Year</InputLabel>
                <Select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(e.target.value)}
                  label="Year"
                >
                  <MenuItem value="2024">2024</MenuItem>
                  <MenuItem value="2023">2023</MenuItem>
                  <MenuItem value="2022">2022</MenuItem>
                </Select>
              </FormControl>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={() => exportReport('comprehensive')}
              >
                Export Report
              </Button>
            </Box>
          </Box>

          {/* Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="textSecondary" gutterBottom>
                        Total Applications
                      </Typography>
                      <Typography variant="h4" color="primary">
                        {totalApplications.toLocaleString()}
                      </Typography>
                    </Box>
                    <PeopleIcon sx={{ fontSize: 40, color: 'primary.main' }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="textSecondary" gutterBottom>
                        Total Enrollments
                      </Typography>
                      <Typography variant="h4" color="success.main">
                        {totalEnrollments.toLocaleString()}
                      </Typography>
                    </Box>
                    <SchoolIcon sx={{ fontSize: 40, color: 'success.main' }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="textSecondary" gutterBottom>
                        Conversion Rate
                      </Typography>
                      <Typography variant="h4" color="info.main">
                        {overallConversion}%
                      </Typography>
                    </Box>
                    <TrendingUpIcon sx={{ fontSize: 40, color: 'info.main' }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="textSecondary" gutterBottom>
                        Active Programs
                      </Typography>
                      <Typography variant="h4" color="warning.main">
                        {programsData?.data?.length || 0}
                      </Typography>
                    </Box>
                    <AssessmentIcon sx={{ fontSize: 40, color: 'warning.main' }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Tabs for Different Reports */}
          <Paper sx={{ mb: 3 }}>
            <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
              <Tab label="Application Status" />
              <Tab label="Program Analysis" />
              <Tab label="Trends & Forecasting" />
              <Tab label="Demographics" />
            </Tabs>

            <TabPanel value={tabValue} index={0}>
              <Grid container spacing={3} sx={{ p: 3 }}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Application Status Distribution" />
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={applicationStatusData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percentage }) => `${name} (${percentage}%)`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="count"
                          >
                            {applicationStatusData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Status Breakdown" />
                    <CardContent>
                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Status</TableCell>
                              <TableCell align="right">Count</TableCell>
                              <TableCell align="right">Percentage</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {applicationStatusData.map((row) => (
                              <TableRow key={row.name}>
                                <TableCell>
                                  <Chip label={row.name} size="small" />
                                </TableCell>
                                <TableCell align="right">{row.count}</TableCell>
                                <TableCell align="right">
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <LinearProgress
                                      variant="determinate"
                                      value={row.percentage}
                                      sx={{ width: 60, height: 6 }}
                                    />
                                    {row.percentage}%
                                  </Box>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <Grid container spacing={3} sx={{ p: 3 }}>
                <Grid item xs={12}>
                  <Card>
                    <CardHeader title="Program-wise Performance" />
                    <CardContent>
                      <ResponsiveContainer width="100%" height={400}>
                        <BarChart data={programWiseData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="program" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="applications" fill="#8884d8" name="Applications" />
                          <Bar dataKey="enrolled" fill="#82ca9d" name="Enrolled" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12}>
                  <Card>
                    <CardHeader title="Conversion Rates by Program" />
                    <CardContent>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Program</TableCell>
                              <TableCell align="right">Applications</TableCell>
                              <TableCell align="right">Enrolled</TableCell>
                              <TableCell align="right">Conversion Rate</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {programWiseData.map((row) => (
                              <TableRow key={row.program}>
                                <TableCell>{row.program}</TableCell>
                                <TableCell align="right">{row.applications}</TableCell>
                                <TableCell align="right">{row.enrolled}</TableCell>
                                <TableCell align="right">
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <LinearProgress
                                      variant="determinate"
                                      value={row.conversion}
                                      sx={{ width: 80, height: 6 }}
                                    />
                                    {row.conversion}%
                                  </Box>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <Grid container spacing={3} sx={{ p: 3 }}>
                <Grid item xs={12}>
                  <Card>
                    <CardHeader title="Monthly Application & Enrollment Trends" />
                    <CardContent>
                      <ResponsiveContainer width="100%" height={400}>
                        <AreaChart data={monthlyTrendData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Area
                            type="monotone"
                            dataKey="applications"
                            stackId="1"
                            stroke="#8884d8"
                            fill="#8884d8"
                            name="Applications"
                          />
                          <Area
                            type="monotone"
                            dataKey="enrollments"
                            stackId="2"
                            stroke="#82ca9d"
                            fill="#82ca9d"
                            name="Enrollments"
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12}>
                  <Alert severity="info">
                    <Typography variant="h6" gutterBottom>
                      Trend Analysis & Forecasting
                    </Typography>
                    <Typography>
                      Based on current trends, the system can provide:
                    </Typography>
                    <ul>
                      <li>Seasonal application patterns</li>
                      <li>Enrollment forecasting for capacity planning</li>
                      <li>Program popularity trends</li>
                      <li>Conversion rate optimization insights</li>
                    </ul>
                  </Alert>
                </Grid>
              </Grid>
            </TabPanel>

            <TabPanel value={tabValue} index={3}>
              <Grid container spacing={3} sx={{ p: 3 }}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Gender Distribution" />
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={demographicData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ category, percentage }) => `${category} (${percentage}%)`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="count"
                          >
                            {demographicData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Application Sources" />
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={sourceData} layout="horizontal">
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis type="number" />
                          <YAxis dataKey="source" type="category" width={100} />
                          <Tooltip />
                          <Bar dataKey="count" fill="#8884d8" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12}>
                  <Alert severity="info">
                    <Typography variant="h6" gutterBottom>
                      Demographic Insights
                    </Typography>
                    <Typography>
                      Additional demographic analysis would include:
                    </Typography>
                    <ul>
                      <li>Age distribution of applicants</li>
                      <li>Geographic distribution (city, state, country)</li>
                      <li>Educational background analysis</li>
                      <li>Socioeconomic indicators</li>
                      <li>Previous institution analysis</li>
                    </ul>
                  </Alert>
                </Grid>
              </Grid>
            </TabPanel>
          </Paper>
        </Box>
      </LocalizationProvider>
    </ProtectedRoute>
  );
};

export default ReportsPage;
