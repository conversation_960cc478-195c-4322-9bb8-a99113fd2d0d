'use client';

import React, { useState } from 'react';
// import { useList, useCreate, useUpdate } from '@refinedev/core';
import { List, CreateButton } from '@refinedev/mui';
import {
  DataGrid,
  GridColDef,
  GridActionsCellItem,
  GridRowParams,
} from '@mui/x-data-grid';
import {
  Box,
  Chip,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  Rating,
} from '@mui/material';
import {
  Event as EventIcon,
  Edit as EditIcon,
  VideoCall as VideoIcon,
  LocationOn as LocationIcon,
  Score as ScoreIcon,
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useForm, Controller } from 'react-hook-form';

interface InterviewFormData {
  application_id: string;
  interview_type: string;
  scheduled_date: Date | null;
  duration_minutes: number;
  location: string;
  meeting_link: string;
  interviewer_ids: string[];
  notes: string;
}

interface ScoreFormData {
  score: number;
  recommendation: string;
  notes: string;
}

const InterviewsPage: React.FC = () => {
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);
  const [scoreDialogOpen, setScoreDialogOpen] = useState(false);
  const [selectedInterview, setSelectedInterview] = useState<any>(null);

  // Mock interviews data
  const mockInterviews = [
    {
      id: '1',
      interview_type: 'personal',
      scheduled_date: '2024-01-20T10:00:00Z',
      duration_minutes: 30,
      location: 'Room 101',
      meeting_link: '',
      status: 'scheduled',
      score: null,
      applications: {
        application_number: 'APP2024-001234',
        first_name: 'John',
        last_name: 'Doe'
      }
    },
    {
      id: '2',
      interview_type: 'technical',
      scheduled_date: '2024-01-21T14:00:00Z',
      duration_minutes: 45,
      location: '',
      meeting_link: 'https://zoom.us/j/123456789',
      status: 'completed',
      score: 8.5,
      applications: {
        application_number: 'APP2024-001235',
        first_name: 'Jane',
        last_name: 'Smith'
      }
    }
  ];

  const dataGridProps = {
    rows: mockInterviews,
    loading: false,
    rowCount: mockInterviews.length,
  };

  const createInterview = (data: any) => {
    console.log('Creating interview:', data);
  };

  const updateInterview = (data: any) => {
    console.log('Updating interview:', data);
  };

  // Mock applications data
  const applicationsData = {
    data: [
      { id: '1', application_number: 'APP2024-001234', first_name: 'John', last_name: 'Doe' },
      { id: '2', application_number: 'APP2024-001235', first_name: 'Jane', last_name: 'Smith' }
    ]
  };

  // Mock users data
  const usersData = {
    data: [
      { id: '1', full_name: 'Dr. Smith', role: 'admission_officer' },
      { id: '2', full_name: 'Prof. Johnson', role: 'admin' }
    ]
  };

  const {
    control: scheduleControl,
    handleSubmit: handleScheduleSubmit,
    reset: resetSchedule,
    formState: { errors: scheduleErrors },
  } = useForm<InterviewFormData>({
    defaultValues: {
      application_id: '',
      interview_type: 'personal',
      scheduled_date: null,
      duration_minutes: 30,
      location: '',
      meeting_link: '',
      interviewer_ids: [],
      notes: '',
    },
  });

  const {
    control: scoreControl,
    handleSubmit: handleScoreSubmit,
    reset: resetScore,
    formState: { errors: scoreErrors },
  } = useForm<ScoreFormData>({
    defaultValues: {
      score: 0,
      recommendation: '',
      notes: '',
    },
  });

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      scheduled: 'info',
      completed: 'success',
      cancelled: 'error',
      rescheduled: 'warning',
    };
    return colors[status] || 'default';
  };

  const onScheduleSubmit = (data: InterviewFormData) => {
    createInterview(
      {
        resource: 'interviews',
        values: {
          ...data,
          scheduled_date: data.scheduled_date?.toISOString(),
          status: 'scheduled',
        },
      },
      {
        onSuccess: () => {
          resetSchedule();
          setScheduleDialogOpen(false);
        },
      }
    );
  };

  const onScoreSubmit = (data: ScoreFormData) => {
    updateInterview(
      {
        resource: 'interviews',
        id: selectedInterview.id,
        values: {
          ...data,
          status: 'completed',
          completed_at: new Date().toISOString(),
        },
      },
      {
        onSuccess: () => {
          resetScore();
          setScoreDialogOpen(false);
          setSelectedInterview(null);
        },
      }
    );
  };

  const columns: GridColDef[] = [
    {
      field: 'application_number',
      headerName: 'Application #',
      width: 150,
      valueGetter: ({ row }) => row.applications?.application_number,
      renderCell: ({ value }) => (
        <Typography variant="body2" fontWeight="bold" color="primary">
          {value}
        </Typography>
      ),
    },
    {
      field: 'applicant_name',
      headerName: 'Applicant',
      width: 200,
      valueGetter: ({ row }) => 
        `${row.applications?.first_name} ${row.applications?.last_name}`,
    },
    {
      field: 'interview_type',
      headerName: 'Type',
      width: 120,
      renderCell: ({ value }) => (
        <Chip label={value?.toUpperCase()} size="small" variant="outlined" />
      ),
    },
    {
      field: 'scheduled_date',
      headerName: 'Scheduled Date',
      width: 180,
      type: 'dateTime',
      valueGetter: ({ value }) => new Date(value),
    },
    {
      field: 'duration_minutes',
      headerName: 'Duration',
      width: 100,
      renderCell: ({ value }) => `${value} min`,
    },
    {
      field: 'location',
      headerName: 'Location/Link',
      width: 200,
      renderCell: ({ row }) => (
        <Box display="flex" alignItems="center" gap={1}>
          {row.meeting_link ? (
            <>
              <VideoIcon fontSize="small" color="primary" />
              <Typography variant="body2">Online</Typography>
            </>
          ) : (
            <>
              <LocationIcon fontSize="small" color="action" />
              <Typography variant="body2">{row.location}</Typography>
            </>
          )}
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: ({ value }) => (
        <Chip
          label={value?.toUpperCase()}
          color={getStatusColor(value)}
          size="small"
        />
      ),
    },
    {
      field: 'score',
      headerName: 'Score',
      width: 100,
      renderCell: ({ value }) => (
        <Box display="flex" alignItems="center" gap={1}>
          {value ? (
            <>
              <ScoreIcon fontSize="small" color="success" />
              <Typography variant="body2" fontWeight="bold">
                {value}/10
              </Typography>
            </>
          ) : (
            <Typography variant="body2" color="text.secondary">
              Not scored
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 150,
      getActions: ({ row }: GridRowParams) => [
        <GridActionsCellItem
          key="edit"
          icon={<EditIcon />}
          label="Edit"
          onClick={() => console.log('Edit interview:', row)}
        />,
        <GridActionsCellItem
          key="score"
          icon={<ScoreIcon />}
          label="Score"
          onClick={() => {
            setSelectedInterview(row);
            setScoreDialogOpen(true);
          }}
          disabled={row.status !== 'scheduled'}
        />,
      ],
    },
  ];

  return (
    <ProtectedRoute resource="interviews" action="list">
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <List
          title="Interview Management"
          headerButtons={
            <CreateButton
              onClick={() => setScheduleDialogOpen(true)}
              startIcon={<EventIcon />}
            >
              Schedule Interview
            </CreateButton>
          }
        >
          <DataGrid
            {...dataGridProps}
            columns={columns}
            autoHeight
            pageSizeOptions={[10, 25, 50]}
            disableRowSelectionOnClick
          />
        </List>

        {/* Schedule Interview Dialog */}
        <Dialog
          open={scheduleDialogOpen}
          onClose={() => setScheduleDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Schedule Interview</DialogTitle>
          <form onSubmit={handleScheduleSubmit(onScheduleSubmit)}>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Controller
                    name="application_id"
                    control={scheduleControl}
                    rules={{ required: 'Application selection is required' }}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!scheduleErrors.application_id}>
                        <InputLabel>Select Application</InputLabel>
                        <Select {...field} label="Select Application">
                          {applicationsData?.data?.map((app: any) => (
                            <MenuItem key={app.id} value={app.id}>
                              {app.application_number} - {app.first_name} {app.last_name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="interview_type"
                    control={scheduleControl}
                    render={({ field }) => (
                      <FormControl fullWidth>
                        <InputLabel>Interview Type</InputLabel>
                        <Select {...field} label="Interview Type">
                          <MenuItem value="personal">Personal Interview</MenuItem>
                          <MenuItem value="technical">Technical Interview</MenuItem>
                          <MenuItem value="group">Group Discussion</MenuItem>
                          <MenuItem value="online">Online Interview</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="duration_minutes"
                    control={scheduleControl}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Duration (minutes)"
                        type="number"
                        fullWidth
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="scheduled_date"
                    control={scheduleControl}
                    rules={{ required: 'Schedule date is required' }}
                    render={({ field }) => (
                      <DateTimePicker
                        {...field}
                        label="Scheduled Date & Time"
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            error: !!scheduleErrors.scheduled_date,
                            helperText: scheduleErrors.scheduled_date?.message,
                          },
                        }}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="location"
                    control={scheduleControl}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Location"
                        fullWidth
                        placeholder="Room number, building, etc."
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="meeting_link"
                    control={scheduleControl}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Meeting Link"
                        fullWidth
                        placeholder="Zoom, Teams, or other meeting link"
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="interviewer_ids"
                    control={scheduleControl}
                    render={({ field }) => (
                      <FormControl fullWidth>
                        <InputLabel>Interviewers</InputLabel>
                        <Select
                          {...field}
                          multiple
                          label="Interviewers"
                          value={field.value || []}
                        >
                          {usersData?.data?.map((user: any) => (
                            <MenuItem key={user.id} value={user.id}>
                              {user.full_name} ({user.role})
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="notes"
                    control={scheduleControl}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Notes"
                        multiline
                        rows={3}
                        fullWidth
                        placeholder="Special instructions or notes"
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setScheduleDialogOpen(false)}>Cancel</Button>
              <Button type="submit" variant="contained">
                Schedule Interview
              </Button>
            </DialogActions>
          </form>
        </Dialog>

        {/* Score Interview Dialog */}
        <Dialog
          open={scoreDialogOpen}
          onClose={() => setScoreDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Score Interview</DialogTitle>
          <form onSubmit={handleScoreSubmit(onScoreSubmit)}>
            <DialogContent>
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Interview Details
                  </Typography>
                  <Typography>
                    <strong>Applicant:</strong> {selectedInterview?.applications?.first_name} {selectedInterview?.applications?.last_name}
                  </Typography>
                  <Typography>
                    <strong>Application #:</strong> {selectedInterview?.applications?.application_number}
                  </Typography>
                  <Typography>
                    <strong>Type:</strong> {selectedInterview?.interview_type}
                  </Typography>
                </CardContent>
              </Card>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography component="legend">Interview Score (1-10)</Typography>
                  <Controller
                    name="score"
                    control={scoreControl}
                    rules={{ required: 'Score is required', min: 1, max: 10 }}
                    render={({ field }) => (
                      <Box>
                        <Rating
                          {...field}
                          max={10}
                          size="large"
                          onChange={(_, value) => field.onChange(value)}
                        />
                        <Typography variant="body2" color="text.secondary">
                          Score: {field.value}/10
                        </Typography>
                      </Box>
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="recommendation"
                    control={scoreControl}
                    render={({ field }) => (
                      <FormControl fullWidth>
                        <InputLabel>Recommendation</InputLabel>
                        <Select {...field} label="Recommendation">
                          <MenuItem value="strongly_recommend">Strongly Recommend</MenuItem>
                          <MenuItem value="recommend">Recommend</MenuItem>
                          <MenuItem value="neutral">Neutral</MenuItem>
                          <MenuItem value="not_recommend">Not Recommend</MenuItem>
                          <MenuItem value="strongly_not_recommend">Strongly Not Recommend</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="notes"
                    control={scoreControl}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Interview Notes"
                        multiline
                        rows={4}
                        fullWidth
                        placeholder="Detailed feedback and observations"
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setScoreDialogOpen(false)}>Cancel</Button>
              <Button type="submit" variant="contained">
                Submit Score
              </Button>
            </DialogActions>
          </form>
        </Dialog>
      </LocalizationProvider>
    </ProtectedRoute>
  );
};

export default InterviewsPage;
