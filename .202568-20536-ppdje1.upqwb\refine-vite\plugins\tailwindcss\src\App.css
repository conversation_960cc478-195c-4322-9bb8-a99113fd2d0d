@tailwind base;
@tailwind components;
@tailwind utilities;

/* 
* These styles were added to improve the appearance of the example pages.
* It is safe to delete them, and they are not intended to be used by Refine components.
*/
@layer base {
    input {
        @apply border border-gray-300 rounded-sm;
    }

    input[type="submit"] {
        @apply bg-[#EFEFEF];
    }

    button {
        @apply border border-gray-300 px-2 rounded-sm bg-[#EFEFEF];
    }

    h1 {
        @apply text-2xl font-bold my-6;
    }

    ul {
        @apply list-disc my-4;
    }

    a {
        @apply text-blue-700 underline;
    }

    label {
        @apply text-sm font-medium;
    }

    body {
        @apply m-0 bg-white;
    }

    table {
        @apply border border-black border-collapse;
    }

    table th,
    table td {
        @apply m-0 p-2 border-b border-r border-black;
    }

    table tr:last-child td {
        @apply border-b-0;
    }

    table th:last-child,
    table td:last-child {
        @apply border-r-0;
    }

    .layout {
        @apply flex gap-4;
    }

    @media (max-width: 751px) {
        .layout {
            @apply block;
        }
    }

    .layout .content {
        @apply flex flex-col flex-grow;
    }

    .breadcrumb {
        @apply flex gap-6 p-2 px-4 border-b border-gray-300;
        list-style-type: "/ ";
    }

    .breadcrumb a {
        @apply text-blue-700 no-underline;
    }

    .menu {
        @apply flex-shrink-0 p-2 pr-4 border-r border-gray-300;
    }

    .menu a {
        @apply text-black;
    }

    .menu .active {
        @apply font-bold;
    }

    @media (max-width: 751px) {
        .menu {
            @apply border-r-0 border-b border-gray-300;
        }
    }

    .menu ul {
        @apply pl-4;
    }
}
