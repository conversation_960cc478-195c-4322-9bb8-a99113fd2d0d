"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse-entities";
exports.ids = ["vendor-chunks/parse-entities"];
exports.modules = {

/***/ "(ssr)/./node_modules/parse-entities/decode-entity.js":
/*!******************************************************!*\
  !*** ./node_modules/parse-entities/decode-entity.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar characterEntities = __webpack_require__(/*! character-entities */ \"(ssr)/./node_modules/character-entities/index.json\")\n\nmodule.exports = decodeEntity\n\nvar own = {}.hasOwnProperty\n\nfunction decodeEntity(characters) {\n  return own.call(characterEntities, characters)\n    ? characterEntities[characters]\n    : false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFyc2UtZW50aXRpZXMvZGVjb2RlLWVudGl0eS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWix3QkFBd0IsbUJBQU8sQ0FBQyw4RUFBb0I7O0FBRXBEOztBQUVBLFlBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvcGFyc2UtZW50aXRpZXMvZGVjb2RlLWVudGl0eS5qcz8xNzVlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgY2hhcmFjdGVyRW50aXRpZXMgPSByZXF1aXJlKCdjaGFyYWN0ZXItZW50aXRpZXMnKVxuXG5tb2R1bGUuZXhwb3J0cyA9IGRlY29kZUVudGl0eVxuXG52YXIgb3duID0ge30uaGFzT3duUHJvcGVydHlcblxuZnVuY3Rpb24gZGVjb2RlRW50aXR5KGNoYXJhY3RlcnMpIHtcbiAgcmV0dXJuIG93bi5jYWxsKGNoYXJhY3RlckVudGl0aWVzLCBjaGFyYWN0ZXJzKVxuICAgID8gY2hhcmFjdGVyRW50aXRpZXNbY2hhcmFjdGVyc11cbiAgICA6IGZhbHNlXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/parse-entities/decode-entity.js\n");

/***/ })

};
;