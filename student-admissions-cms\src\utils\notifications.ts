// Notification and Communication System
export interface NotificationTemplate {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'whatsapp' | 'in_app';
  subject?: string;
  content: string;
  variables: string[];
  is_active: boolean;
}

export interface NotificationData {
  recipient_id: string;
  application_id?: string;
  type: 'email' | 'sms' | 'whatsapp' | 'in_app';
  subject: string;
  message: string;
  template_name?: string;
  metadata?: Record<string, any>;
}

export interface TemplateVariable {
  key: string;
  label: string;
  description: string;
  example: string;
}

// Available template variables
export const TEMPLATE_VARIABLES: TemplateVariable[] = [
  {
    key: 'applicant_name',
    label: 'Applicant Name',
    description: 'Full name of the applicant',
    example: '<PERSON>',
  },
  {
    key: 'first_name',
    label: 'First Name',
    description: 'First name of the applicant',
    example: '<PERSON>',
  },
  {
    key: 'last_name',
    label: 'Last Name',
    description: 'Last name of the applicant',
    example: '<PERSON><PERSON>',
  },
  {
    key: 'application_number',
    label: 'Application Number',
    description: 'Unique application identifier',
    example: 'APP2024-001234',
  },
  {
    key: 'program_name',
    label: 'Program Name',
    description: 'Name of the academic program',
    example: 'Bachelor of Computer Science',
  },
  {
    key: 'interview_date',
    label: 'Interview Date',
    description: 'Scheduled interview date',
    example: 'March 15, 2024',
  },
  {
    key: 'interview_time',
    label: 'Interview Time',
    description: 'Scheduled interview time',
    example: '10:00 AM',
  },
  {
    key: 'interview_location',
    label: 'Interview Location',
    description: 'Interview venue or meeting link',
    example: 'Room 101, Admin Building',
  },
  {
    key: 'offer_deadline',
    label: 'Offer Acceptance Deadline',
    description: 'Deadline to accept the offer',
    example: 'April 30, 2024',
  },
  {
    key: 'total_fees',
    label: 'Total Fees',
    description: 'Total program fees',
    example: '$25,000',
  },
  {
    key: 'student_id',
    label: 'Student ID',
    description: 'Assigned student identification number',
    example: 'STU2024001',
  },
  {
    key: 'class_name',
    label: 'Class Name',
    description: 'Assigned class or section',
    example: 'CS-A-2024',
  },
  {
    key: 'academic_year',
    label: 'Academic Year',
    description: 'Academic year of enrollment',
    example: '2024-2025',
  },
  {
    key: 'institution_name',
    label: 'Institution Name',
    description: 'Name of the educational institution',
    example: 'University of Excellence',
  },
  {
    key: 'contact_email',
    label: 'Contact Email',
    description: 'Institution contact email',
    example: '<EMAIL>',
  },
  {
    key: 'contact_phone',
    label: 'Contact Phone',
    description: 'Institution contact phone',
    example: '+****************',
  },
];

// Default notification templates
export const DEFAULT_TEMPLATES: Omit<NotificationTemplate, 'id'>[] = [
  {
    name: 'Application Received',
    type: 'email',
    subject: 'Application Received - {{application_number}}',
    content: `Dear {{applicant_name}},

Thank you for submitting your application to {{institution_name}} for the {{program_name}} program.

Your application number is: {{application_number}}

We have received your application and it is currently under review. You will be notified of any updates regarding your application status.

If you have any questions, please contact us at {{contact_email}} or {{contact_phone}}.

Best regards,
Admissions Office
{{institution_name}}`,
    variables: ['applicant_name', 'institution_name', 'program_name', 'application_number', 'contact_email', 'contact_phone'],
    is_active: true,
  },
  {
    name: 'Documents Required',
    type: 'email',
    subject: 'Documents Required - {{application_number}}',
    content: `Dear {{applicant_name}},

We are reviewing your application ({{application_number}}) for the {{program_name}} program.

To complete your application, we require the following documents:
- Academic transcripts
- Letters of recommendation
- Statement of purpose
- Passport copy

Please upload these documents through your application portal as soon as possible.

Best regards,
Admissions Office
{{institution_name}}`,
    variables: ['applicant_name', 'application_number', 'program_name', 'institution_name'],
    is_active: true,
  },
  {
    name: 'Interview Scheduled',
    type: 'email',
    subject: 'Interview Scheduled - {{application_number}}',
    content: `Dear {{applicant_name}},

Congratulations! Your application ({{application_number}}) for the {{program_name}} program has progressed to the interview stage.

Interview Details:
- Date: {{interview_date}}
- Time: {{interview_time}}
- Location: {{interview_location}}

Please arrive 15 minutes early and bring a valid photo ID.

If you need to reschedule, please contact us at {{contact_email}} at least 24 hours in advance.

Best regards,
Admissions Office
{{institution_name}}`,
    variables: ['applicant_name', 'application_number', 'program_name', 'interview_date', 'interview_time', 'interview_location', 'contact_email', 'institution_name'],
    is_active: true,
  },
  {
    name: 'Application Accepted',
    type: 'email',
    subject: 'Congratulations! Application Accepted - {{application_number}}',
    content: `Dear {{applicant_name}},

Congratulations! We are pleased to inform you that your application ({{application_number}}) for the {{program_name}} program has been ACCEPTED.

Offer Details:
- Program: {{program_name}}
- Total Fees: {{total_fees}}
- Acceptance Deadline: {{offer_deadline}}

To secure your place, please accept this offer by {{offer_deadline}} through your application portal.

We look forward to welcoming you to {{institution_name}}.

Best regards,
Admissions Office
{{institution_name}}`,
    variables: ['applicant_name', 'application_number', 'program_name', 'total_fees', 'offer_deadline', 'institution_name'],
    is_active: true,
  },
  {
    name: 'Application Rejected',
    type: 'email',
    subject: 'Application Status Update - {{application_number}}',
    content: `Dear {{applicant_name}},

Thank you for your interest in the {{program_name}} program at {{institution_name}}.

After careful consideration, we regret to inform you that we are unable to offer you admission for the current academic year.

This decision was difficult due to the highly competitive nature of our admissions process and the limited number of available seats.

We encourage you to apply again in the future and wish you success in your academic pursuits.

Best regards,
Admissions Office
{{institution_name}}`,
    variables: ['applicant_name', 'program_name', 'institution_name', 'application_number'],
    is_active: true,
  },
  {
    name: 'Enrollment Confirmation',
    type: 'email',
    subject: 'Enrollment Confirmed - Welcome to {{institution_name}}',
    content: `Dear {{applicant_name}},

Welcome to {{institution_name}}! Your enrollment has been confirmed.

Enrollment Details:
- Student ID: {{student_id}}
- Program: {{program_name}}
- Class: {{class_name}}
- Academic Year: {{academic_year}}

You will receive additional information about orientation, course registration, and other important details in the coming days.

If you have any questions, please contact us at {{contact_email}}.

Welcome aboard!

Best regards,
Admissions Office
{{institution_name}}`,
    variables: ['applicant_name', 'student_id', 'program_name', 'class_name', 'academic_year', 'institution_name', 'contact_email'],
    is_active: true,
  },
  {
    name: 'Interview Reminder',
    type: 'sms',
    subject: '',
    content: `Hi {{first_name}}, reminder: Your interview for {{program_name}} is scheduled for {{interview_date}} at {{interview_time}}. Location: {{interview_location}}. Good luck! - {{institution_name}}`,
    variables: ['first_name', 'program_name', 'interview_date', 'interview_time', 'interview_location', 'institution_name'],
    is_active: true,
  },
  {
    name: 'Offer Deadline Reminder',
    type: 'email',
    subject: 'Reminder: Offer Acceptance Deadline - {{application_number}}',
    content: `Dear {{applicant_name}},

This is a friendly reminder that your offer acceptance deadline for the {{program_name}} program is approaching.

Deadline: {{offer_deadline}}

Please log into your application portal to accept or decline the offer before the deadline.

If you have any questions, please contact us at {{contact_email}}.

Best regards,
Admissions Office
{{institution_name}}`,
    variables: ['applicant_name', 'program_name', 'offer_deadline', 'application_number', 'contact_email', 'institution_name'],
    is_active: true,
  },
];

// Template processing functions
export const processTemplate = (
  template: string,
  variables: Record<string, any>
): string => {
  let processedContent = template;
  
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    processedContent = processedContent.replace(regex, String(value || ''));
  });
  
  return processedContent;
};

export const extractVariablesFromTemplate = (template: string): string[] => {
  const regex = /{{(\w+)}}/g;
  const variables: string[] = [];
  let match;
  
  while ((match = regex.exec(template)) !== null) {
    if (!variables.includes(match[1])) {
      variables.push(match[1]);
    }
  }
  
  return variables;
};

export const validateTemplate = (template: string): {
  isValid: boolean;
  errors: string[];
  variables: string[];
} => {
  const errors: string[] = [];
  const variables = extractVariablesFromTemplate(template);
  
  // Check for unclosed variables
  const openBraces = (template.match(/{{/g) || []).length;
  const closeBraces = (template.match(/}}/g) || []).length;
  
  if (openBraces !== closeBraces) {
    errors.push('Template has unclosed variable brackets');
  }
  
  // Check for invalid variable names
  variables.forEach(variable => {
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(variable)) {
      errors.push(`Invalid variable name: ${variable}`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors,
    variables,
  };
};

// Notification sending functions (would integrate with actual services)
export const sendEmail = async (
  to: string,
  subject: string,
  content: string,
  metadata?: Record<string, any>
): Promise<boolean> => {
  try {
    // Integration with email service (e.g., SendGrid, AWS SES, etc.)
    console.log('Sending email:', { to, subject, content, metadata });
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return true;
  } catch (error) {
    console.error('Failed to send email:', error);
    return false;
  }
};

export const sendSMS = async (
  to: string,
  message: string,
  metadata?: Record<string, any>
): Promise<boolean> => {
  try {
    // Integration with SMS service (e.g., Twilio, AWS SNS, etc.)
    console.log('Sending SMS:', { to, message, metadata });
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return true;
  } catch (error) {
    console.error('Failed to send SMS:', error);
    return false;
  }
};

export const sendWhatsApp = async (
  to: string,
  message: string,
  metadata?: Record<string, any>
): Promise<boolean> => {
  try {
    // Integration with WhatsApp Business API
    console.log('Sending WhatsApp:', { to, message, metadata });
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 800));
    
    return true;
  } catch (error) {
    console.error('Failed to send WhatsApp:', error);
    return false;
  }
};

// Batch notification sending
export const sendBatchNotifications = async (
  notifications: NotificationData[]
): Promise<{ success: number; failed: number; results: boolean[] }> => {
  const results: boolean[] = [];
  let success = 0;
  let failed = 0;
  
  for (const notification of notifications) {
    let result = false;
    
    try {
      switch (notification.type) {
        case 'email':
          result = await sendEmail(
            notification.recipient_id,
            notification.subject,
            notification.message,
            notification.metadata
          );
          break;
        case 'sms':
          result = await sendSMS(
            notification.recipient_id,
            notification.message,
            notification.metadata
          );
          break;
        case 'whatsapp':
          result = await sendWhatsApp(
            notification.recipient_id,
            notification.message,
            notification.metadata
          );
          break;
        default:
          console.warn('Unsupported notification type:', notification.type);
      }
      
      if (result) {
        success++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      failed++;
    }
    
    results.push(result);
  }
  
  return { success, failed, results };
};
