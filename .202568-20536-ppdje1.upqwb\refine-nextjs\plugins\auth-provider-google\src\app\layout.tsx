import type { Metadata } from "next";
<%- (_app.nextjsImport || []).join("\n") _%>
import React, { Suspense } from "react";
import { RefineContext } from "./_refine_context";

export const metadata: Metadata = {
    title: "Refine",
    description: "Generated by create refine app",
    icons: {
        icon: "/favicon.ico",
    },
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    
    <%- (_app.nextjsInner || []).join("\n") %>

    return (
        <html lang="en">
            <body>
                <Suspense>
                    <RefineContext  <%- (_app.refineContextProps || []).join("\n") %>
                    >
                        {children}
                    </RefineContext>
                </Suspense>
            </body>
        </html>
    );
}
