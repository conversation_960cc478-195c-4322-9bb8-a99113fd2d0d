'use client';

import React, { createContext, useContext } from 'react';

// Mock data for the entire application
export const mockData = {
  inquiries: [
    {
      id: '1',
      inquiry_number: 'INQ2024-001',
      full_name: '<PERSON>',
      email: '<EMAIL>',
      phone: '******-0123',
      program_name: 'Computer Science',
      inquiry_source: 'Website',
      status: 'new',
      inquiry_date: '2024-01-15',
      follow_up_date: '2024-01-20',
      converted_to_application: false,
      academic_programs: { name: 'Bachelor of Computer Science' }
    },
    {
      id: '2',
      inquiry_number: 'INQ2024-002',
      full_name: '<PERSON>',
      email: '<EMAIL>',
      phone: '******-0124',
      program_name: 'Business Administration',
      inquiry_source: 'Referral',
      status: 'contacted',
      inquiry_date: '2024-01-16',
      follow_up_date: '2024-01-22',
      converted_to_application: false,
      academic_programs: { name: 'Master of Business Administration' }
    }
  ],
  
  applications: [
    {
      id: '1',
      application_number: 'APP2024-001234',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '******-0123',
      status: 'in_review',
      submission_date: '2024-01-15T10:30:00Z',
      merit_score: 85.5,
      date_of_birth: '2000-05-15',
      academic_programs: { name: 'Bachelor of Computer Science' }
    },
    {
      id: '2',
      application_number: 'APP2024-001235',
      first_name: 'Jane',
      last_name: 'Smith',
      email: '<EMAIL>',
      phone: '******-0124',
      status: 'offered',
      submission_date: '2024-01-16T14:20:00Z',
      merit_score: 92.3,
      date_of_birth: '1999-08-22',
      academic_programs: { name: 'Master of Business Administration' }
    }
  ],
  
  interviews: [
    {
      id: '1',
      interview_type: 'personal',
      scheduled_date: '2024-01-20T10:00:00Z',
      duration_minutes: 30,
      location: 'Room 101',
      meeting_link: '',
      status: 'scheduled',
      score: null,
      applications: {
        application_number: 'APP2024-001234',
        first_name: 'John',
        last_name: 'Doe'
      }
    },
    {
      id: '2',
      interview_type: 'technical',
      scheduled_date: '2024-01-21T14:00:00Z',
      duration_minutes: 45,
      location: '',
      meeting_link: 'https://zoom.us/j/123456789',
      status: 'completed',
      score: 8.5,
      applications: {
        application_number: 'APP2024-001235',
        first_name: 'Jane',
        last_name: 'Smith'
      }
    }
  ],
  
  enrollments: [
    {
      id: '1',
      student_id: 'STU2024001',
      enrollment_date: '2024-01-25',
      status: 'enrolled',
      fees_paid: 15000,
      total_fees: 25000,
      applications: {
        first_name: 'Mike',
        last_name: 'Johnson'
      },
      classes: {
        class_name: 'CS-A-2024',
        academic_programs: { name: 'Bachelor of Computer Science' }
      },
      academic_years: { year_name: '2024-2025' }
    }
  ],
  
  classes: [
    {
      id: '1',
      class_name: 'CS-A-2024',
      level: 1,
      capacity: 30,
      current_enrollment: 25,
      is_active: true,
      academic_programs: { name: 'Bachelor of Computer Science' }
    },
    {
      id: '2',
      class_name: 'MBA-A-2024',
      level: 1,
      capacity: 25,
      current_enrollment: 20,
      is_active: true,
      academic_programs: { name: 'Master of Business Administration' }
    }
  ],
  
  programs: [
    {
      id: '1',
      name: 'Bachelor of Computer Science',
      code: 'BCS',
      duration_years: 4,
      degree_type: 'Bachelor',
      department: 'Computer Science',
      is_active: true
    },
    {
      id: '2',
      name: 'Master of Business Administration',
      code: 'MBA',
      duration_years: 2,
      degree_type: 'Master',
      department: 'Business',
      is_active: true
    }
  ],
  
  academicYears: [
    {
      id: '1',
      year_name: '2024-2025',
      start_date: '2024-09-01',
      end_date: '2025-06-30',
      is_current: true,
      is_active: true
    }
  ],
  
  users: [
    {
      id: '1',
      full_name: 'Dr. Smith',
      role: 'admission_officer',
      email: '<EMAIL>'
    },
    {
      id: '2',
      full_name: 'Prof. Johnson',
      role: 'admin',
      email: '<EMAIL>'
    }
  ],
  
  templates: [
    {
      id: '1',
      name: 'Application Received',
      type: 'email',
      subject: 'Application Received - {{application_number}}',
      content: 'Dear {{applicant_name}}, Your application has been received.',
      variables: ['applicant_name', 'application_number'],
      is_active: true,
      created_at: '2024-01-01T00:00:00Z'
    }
  ]
};

// Mock hooks to replace Refine hooks
export const useMockList = (resource: string) => {
  const data = mockData[resource as keyof typeof mockData] || [];
  return {
    data: { data, total: data.length },
    isLoading: false,
    error: null
  };
};

export const useMockDataGrid = (config: any) => {
  const resource = config.resource;
  const data = mockData[resource as keyof typeof mockData] || [];
  
  return {
    dataGridProps: {
      rows: data,
      loading: false,
      rowCount: data.length,
    }
  };
};

export const useMockCreate = () => {
  return {
    mutate: (params: any) => {
      console.log('Mock create:', params);
      if (params.onSuccess) params.onSuccess();
    },
    isLoading: false
  };
};

export const useMockUpdate = () => {
  return {
    mutate: (params: any) => {
      console.log('Mock update:', params);
      if (params.onSuccess) params.onSuccess();
    },
    isLoading: false
  };
};

export const useMockDelete = () => {
  return {
    mutate: (params: any) => {
      console.log('Mock delete:', params);
      if (params.onSuccess) params.onSuccess();
    },
    isLoading: false
  };
};

// Context for dummy data
const DummyDataContext = createContext(mockData);

export const DummyDataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <DummyDataContext.Provider value={mockData}>
      {children}
    </DummyDataContext.Provider>
  );
};

export const useDummyData = () => {
  return useContext(DummyDataContext);
};
