'use client';

import React from 'react';
import { useList, useCreate, useUpdate, useDelete } from '@refinedev/core';
import {
  List,
  useDataGrid,
  CreateButton,
  EditButton,
  DeleteButton,
  ShowButton,
} from '@refinedev/mui';
import {
  DataGrid,
  GridColDef,
  GridActionsCellItem,
  GridRowParams,
} from '@mui/x-data-grid';
import {
  Box,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  PersonAdd as ConvertIcon,
} from '@mui/icons-material';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useState } from 'react';

const InquiriesPage: React.FC = () => {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedInquiry, setSelectedInquiry] = useState<any>(null);

  const { dataGridProps } = useDataGrid({
    resource: 'inquiries',
    initialSorter: [
      {
        field: 'created_at',
        order: 'desc',
      },
    ],
  });

  const { mutate: createInquiry } = useCreate();
  const { mutate: updateInquiry } = useUpdate();
  const { mutate: deleteInquiry } = useDelete();

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      new: 'info',
      contacted: 'primary',
      follow_up: 'warning',
      converted: 'success',
      closed: 'default',
    };
    return colors[status] || 'default';
  };

  const handleConvertToApplication = (inquiry: any) => {
    // Logic to convert inquiry to application
    console.log('Converting inquiry to application:', inquiry);
  };

  const columns: GridColDef[] = [
    {
      field: 'inquiry_number',
      headerName: 'Inquiry #',
      width: 120,
      renderCell: ({ value }) => (
        <Typography variant="body2" fontWeight="bold">
          {value}
        </Typography>
      ),
    },
    {
      field: 'full_name',
      headerName: 'Full Name',
      width: 200,
      renderCell: ({ value }) => (
        <Typography variant="body2">{value}</Typography>
      ),
    },
    {
      field: 'email',
      headerName: 'Email',
      width: 200,
      renderCell: ({ value }) => (
        <Box display="flex" alignItems="center" gap={1}>
          <EmailIcon fontSize="small" color="action" />
          <Typography variant="body2">{value}</Typography>
        </Box>
      ),
    },
    {
      field: 'phone',
      headerName: 'Phone',
      width: 150,
      renderCell: ({ value }) => (
        <Box display="flex" alignItems="center" gap={1}>
          <PhoneIcon fontSize="small" color="action" />
          <Typography variant="body2">{value}</Typography>
        </Box>
      ),
    },
    {
      field: 'program_name',
      headerName: 'Program',
      width: 180,
      valueGetter: ({ row }) => row.academic_programs?.name || 'Not specified',
    },
    {
      field: 'inquiry_source',
      headerName: 'Source',
      width: 120,
      renderCell: ({ value }) => (
        <Chip label={value} size="small" variant="outlined" />
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: ({ value }) => (
        <Chip
          label={value?.replace('_', ' ').toUpperCase()}
          color={getStatusColor(value)}
          size="small"
        />
      ),
    },
    {
      field: 'inquiry_date',
      headerName: 'Date',
      width: 120,
      type: 'date',
      valueGetter: ({ value }) => new Date(value),
    },
    {
      field: 'follow_up_date',
      headerName: 'Follow Up',
      width: 120,
      type: 'date',
      valueGetter: ({ value }) => value ? new Date(value) : null,
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 200,
      getActions: ({ row }: GridRowParams) => [
        <GridActionsCellItem
          key="view"
          icon={<ViewIcon />}
          label="View"
          onClick={() => console.log('View inquiry:', row)}
        />,
        <GridActionsCellItem
          key="edit"
          icon={<EditIcon />}
          label="Edit"
          onClick={() => {
            setSelectedInquiry(row);
            setEditDialogOpen(true);
          }}
        />,
        <GridActionsCellItem
          key="convert"
          icon={<ConvertIcon />}
          label="Convert to Application"
          onClick={() => handleConvertToApplication(row)}
          disabled={row.converted_to_application}
        />,
        <GridActionsCellItem
          key="delete"
          icon={<DeleteIcon />}
          label="Delete"
          onClick={() => {
            if (confirm('Are you sure you want to delete this inquiry?')) {
              deleteInquiry({
                resource: 'inquiries',
                id: row.id,
              });
            }
          }}
        />,
      ],
    },
  ];

  return (
    <ProtectedRoute resource="inquiries" action="list">
      <List
        title="Inquiries Management"
        headerButtons={
          <CreateButton
            onClick={() => setCreateDialogOpen(true)}
            startIcon={<AddIcon />}
          >
            New Inquiry
          </CreateButton>
        }
      >
        <DataGrid
          {...dataGridProps}
          columns={columns}
          autoHeight
          pageSizeOptions={[10, 25, 50]}
          disableRowSelectionOnClick
        />
      </List>
    </ProtectedRoute>
  );
};

export default InquiriesPage;
