import { FetchExample } from "./index";
import "./mocks";
import { mockResponse } from "./mocks";

<%_ if (testing === 'testing-library') { _%>
import { render, waitFor } from "test";
<%_ } _%>

<%_ if (testing === 'enzyme') { _%>
import mount from "test/mount";
<%_ } _%>
    
<%_ if (testing === 'testing-library') { _%>
describe("Fetch testing with testing-library and nock", () => {
  it("renders the mock result", async () => {
    const { getByText, getByTestId } = render(<FetchExample />);

    await waitFor(() => {
      expect(getByTestId("joke-container")).toBeDefined();
      expect(getByText(mockResponse[0].setup)).toBeDefined();
      expect(getByText(mockResponse[0].punchline)).toBeDefined();
    });
  });
});
<%_ } _%>

<%_ if (testing === 'enzyme') { _%>
describe("Fetch testing with enzyme and nock", () => {
    it("renders without crashing", async () => {
        expect(true).toBeTruthy();
    });
    });
<%_ } _%>
    