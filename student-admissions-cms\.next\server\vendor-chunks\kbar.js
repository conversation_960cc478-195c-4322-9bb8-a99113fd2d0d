"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/kbar";
exports.ids = ["vendor-chunks/kbar"];
exports.modules = {

/***/ "(ssr)/./node_modules/kbar/lib/InternalEvents.js":
/*!*************************************************!*\
  !*** ./node_modules/kbar/lib/InternalEvents.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InternalEvents = void 0;\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar tinykeys_1 = __importDefault(__webpack_require__(/*! ./tinykeys */ \"(ssr)/./node_modules/kbar/lib/tinykeys.js\"));\nvar types_1 = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/kbar/lib/types.js\");\nvar useKBar_1 = __webpack_require__(/*! ./useKBar */ \"(ssr)/./node_modules/kbar/lib/useKBar.js\");\nvar utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/kbar/lib/utils.js\");\nfunction InternalEvents() {\n    useToggleHandler();\n    useDocumentLock();\n    useShortcuts();\n    useFocusHandler();\n    return null;\n}\nexports.InternalEvents = InternalEvents;\n/**\n * `useToggleHandler` handles the keyboard events for toggling kbar.\n */\nfunction useToggleHandler() {\n    var _a, _b;\n    var _c = (0, useKBar_1.useKBar)(function (state) { return ({\n        visualState: state.visualState,\n        showing: state.visualState !== types_1.VisualState.hidden,\n        disabled: state.disabled,\n    }); }), query = _c.query, options = _c.options, visualState = _c.visualState, showing = _c.showing, disabled = _c.disabled;\n    React.useEffect(function () {\n        var _a;\n        var close = function () {\n            query.setVisualState(function (vs) {\n                if (vs === types_1.VisualState.hidden || vs === types_1.VisualState.animatingOut) {\n                    return vs;\n                }\n                return types_1.VisualState.animatingOut;\n            });\n        };\n        if (disabled) {\n            close();\n            return;\n        }\n        var shortcut = options.toggleShortcut || \"$mod+k\";\n        var unsubscribe = (0, tinykeys_1.default)(window, (_a = {},\n            _a[shortcut] = function (event) {\n                var _a, _b, _c, _d;\n                if (event.defaultPrevented)\n                    return;\n                event.preventDefault();\n                query.toggle();\n                if (showing) {\n                    (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n                }\n                else {\n                    (_d = (_c = options.callbacks) === null || _c === void 0 ? void 0 : _c.onOpen) === null || _d === void 0 ? void 0 : _d.call(_c);\n                }\n            },\n            _a.Escape = function (event) {\n                var _a, _b;\n                if (showing) {\n                    event.stopPropagation();\n                    event.preventDefault();\n                    (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n                }\n                close();\n            },\n            _a));\n        return function () {\n            unsubscribe();\n        };\n    }, [options.callbacks, options.toggleShortcut, query, showing, disabled]);\n    var timeoutRef = React.useRef();\n    var runAnimateTimer = React.useCallback(function (vs) {\n        var _a, _b;\n        var ms = 0;\n        if (vs === types_1.VisualState.animatingIn) {\n            ms = ((_a = options.animations) === null || _a === void 0 ? void 0 : _a.enterMs) || 0;\n        }\n        if (vs === types_1.VisualState.animatingOut) {\n            ms = ((_b = options.animations) === null || _b === void 0 ? void 0 : _b.exitMs) || 0;\n        }\n        clearTimeout(timeoutRef.current);\n        timeoutRef.current = setTimeout(function () {\n            var backToRoot = false;\n            // TODO: setVisualState argument should be a function or just a VisualState value.\n            query.setVisualState(function () {\n                var finalVs = vs === types_1.VisualState.animatingIn\n                    ? types_1.VisualState.showing\n                    : types_1.VisualState.hidden;\n                if (finalVs === types_1.VisualState.hidden) {\n                    backToRoot = true;\n                }\n                return finalVs;\n            });\n            if (backToRoot) {\n                query.setCurrentRootAction(null);\n            }\n        }, ms);\n    }, [(_a = options.animations) === null || _a === void 0 ? void 0 : _a.enterMs, (_b = options.animations) === null || _b === void 0 ? void 0 : _b.exitMs, query]);\n    React.useEffect(function () {\n        switch (visualState) {\n            case types_1.VisualState.animatingIn:\n            case types_1.VisualState.animatingOut:\n                runAnimateTimer(visualState);\n                break;\n        }\n    }, [runAnimateTimer, visualState]);\n}\n/**\n * `useDocumentLock` is a simple implementation for preventing the\n * underlying page content from scrolling when kbar is open.\n */\nfunction useDocumentLock() {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        visualState: state.visualState,\n    }); }), visualState = _a.visualState, options = _a.options;\n    React.useEffect(function () {\n        if (options.disableDocumentLock)\n            return;\n        if (visualState === types_1.VisualState.animatingIn) {\n            document.body.style.overflow = \"hidden\";\n            if (!options.disableScrollbarManagement) {\n                var scrollbarWidth = (0, utils_1.getScrollbarWidth)();\n                // take into account the margins explicitly added by the consumer\n                var mr = getComputedStyle(document.body)[\"margin-right\"];\n                if (mr) {\n                    // remove non-numeric values; px, rem, em, etc.\n                    scrollbarWidth += Number(mr.replace(/\\D/g, \"\"));\n                }\n                document.body.style.marginRight = scrollbarWidth + \"px\";\n            }\n        }\n        else if (visualState === types_1.VisualState.hidden) {\n            document.body.style.removeProperty(\"overflow\");\n            if (!options.disableScrollbarManagement) {\n                document.body.style.removeProperty(\"margin-right\");\n            }\n        }\n    }, [\n        options.disableDocumentLock,\n        options.disableScrollbarManagement,\n        visualState,\n    ]);\n}\n/**\n * Reference: https://github.com/jamiebuilds/tinykeys/issues/37\n *\n * Fixes an issue where simultaneous key commands for shortcuts;\n * ie given two actions with shortcuts ['t','s'] and ['s'], pressing\n * 't' and 's' consecutively will cause both shortcuts to fire.\n *\n * `wrap` sets each keystroke event in a WeakSet, and ensures that\n * if ['t', 's'] are pressed, then the subsequent ['s'] event will\n * be ignored. This depends on the order in which we register the\n * shortcuts to tinykeys, which is handled below.\n */\nvar handled = new WeakSet();\nfunction wrap(handler) {\n    return function (event) {\n        if (handled.has(event))\n            return;\n        handler(event);\n        handled.add(event);\n    };\n}\n/**\n * `useShortcuts` registers and listens to keyboard strokes and\n * performs actions for patterns that match the user defined `shortcut`.\n */\nfunction useShortcuts() {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        actions: state.actions,\n        open: state.visualState === types_1.VisualState.showing,\n        disabled: state.disabled,\n    }); }), actions = _a.actions, query = _a.query, open = _a.open, options = _a.options, disabled = _a.disabled;\n    React.useEffect(function () {\n        var _a;\n        if (open || disabled)\n            return;\n        var actionsList = Object.keys(actions).map(function (key) { return actions[key]; });\n        var actionsWithShortcuts = [];\n        for (var _i = 0, actionsList_1 = actionsList; _i < actionsList_1.length; _i++) {\n            var action = actionsList_1[_i];\n            if (!((_a = action.shortcut) === null || _a === void 0 ? void 0 : _a.length)) {\n                continue;\n            }\n            actionsWithShortcuts.push(action);\n        }\n        actionsWithShortcuts = actionsWithShortcuts.sort(function (a, b) { return b.shortcut.join(\" \").length - a.shortcut.join(\" \").length; });\n        var shortcutsMap = {};\n        var _loop_1 = function (action) {\n            var shortcut = action.shortcut.join(\" \");\n            shortcutsMap[shortcut] = wrap(function (event) {\n                var _a, _b, _c, _d, _e, _f;\n                if ((0, utils_1.shouldRejectKeystrokes)())\n                    return;\n                event.preventDefault();\n                if ((_a = action.children) === null || _a === void 0 ? void 0 : _a.length) {\n                    query.setCurrentRootAction(action.id);\n                    query.toggle();\n                    (_c = (_b = options.callbacks) === null || _b === void 0 ? void 0 : _b.onOpen) === null || _c === void 0 ? void 0 : _c.call(_b);\n                }\n                else {\n                    (_d = action.command) === null || _d === void 0 ? void 0 : _d.perform();\n                    (_f = (_e = options.callbacks) === null || _e === void 0 ? void 0 : _e.onSelectAction) === null || _f === void 0 ? void 0 : _f.call(_e, action);\n                }\n            });\n        };\n        for (var _b = 0, actionsWithShortcuts_1 = actionsWithShortcuts; _b < actionsWithShortcuts_1.length; _b++) {\n            var action = actionsWithShortcuts_1[_b];\n            _loop_1(action);\n        }\n        var unsubscribe = (0, tinykeys_1.default)(window, shortcutsMap, {\n            timeout: 400,\n        });\n        return function () {\n            unsubscribe();\n        };\n    }, [actions, open, options.callbacks, query, disabled]);\n}\n/**\n * `useFocusHandler` ensures that focus is set back on the element which was\n * in focus prior to kbar being triggered.\n */\nfunction useFocusHandler() {\n    var rFirstRender = React.useRef(true);\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        isShowing: state.visualState === types_1.VisualState.showing ||\n            state.visualState === types_1.VisualState.animatingIn,\n    }); }), isShowing = _a.isShowing, query = _a.query;\n    var activeElementRef = React.useRef(null);\n    React.useEffect(function () {\n        if (rFirstRender.current) {\n            rFirstRender.current = false;\n            return;\n        }\n        if (isShowing) {\n            activeElementRef.current = document.activeElement;\n            return;\n        }\n        // This fixes an issue on Safari where closing kbar causes the entire\n        // page to scroll to the bottom. The reason this was happening was due\n        // to the search input still in focus when we removed it from the dom.\n        var currentActiveElement = document.activeElement;\n        if ((currentActiveElement === null || currentActiveElement === void 0 ? void 0 : currentActiveElement.tagName.toLowerCase()) === \"input\") {\n            currentActiveElement.blur();\n        }\n        var activeElement = activeElementRef.current;\n        if (activeElement && activeElement !== currentActiveElement) {\n            activeElement.focus();\n        }\n    }, [isShowing]);\n    // When focus is blurred from the search input while kbar is still\n    // open, any keystroke should set focus back to the search input.\n    React.useEffect(function () {\n        function handler(event) {\n            var input = query.getInput();\n            if (event.target !== input) {\n                input.focus();\n            }\n        }\n        if (isShowing) {\n            window.addEventListener(\"keydown\", handler);\n            return function () {\n                window.removeEventListener(\"keydown\", handler);\n            };\n        }\n    }, [isShowing, query]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/InternalEvents.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/KBarAnimator.js":
/*!***********************************************!*\
  !*** ./node_modules/kbar/lib/KBarAnimator.js ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KBarAnimator = void 0;\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar types_1 = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/kbar/lib/types.js\");\nvar useKBar_1 = __webpack_require__(/*! ./useKBar */ \"(ssr)/./node_modules/kbar/lib/useKBar.js\");\nvar utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/kbar/lib/utils.js\");\nvar appearanceAnimationKeyframes = [\n    {\n        opacity: 0,\n        transform: \"scale(.99)\",\n    },\n    { opacity: 1, transform: \"scale(1.01)\" },\n    { opacity: 1, transform: \"scale(1)\" },\n];\nvar bumpAnimationKeyframes = [\n    {\n        transform: \"scale(1)\",\n    },\n    {\n        transform: \"scale(.98)\",\n    },\n    {\n        transform: \"scale(1)\",\n    },\n];\nvar KBarAnimator = function (_a) {\n    var _b, _c;\n    var children = _a.children, style = _a.style, className = _a.className, disableCloseOnOuterClick = _a.disableCloseOnOuterClick;\n    var _d = (0, useKBar_1.useKBar)(function (state) { return ({\n        visualState: state.visualState,\n        currentRootActionId: state.currentRootActionId,\n    }); }), visualState = _d.visualState, currentRootActionId = _d.currentRootActionId, query = _d.query, options = _d.options;\n    var outerRef = React.useRef(null);\n    var innerRef = React.useRef(null);\n    var enterMs = ((_b = options === null || options === void 0 ? void 0 : options.animations) === null || _b === void 0 ? void 0 : _b.enterMs) || 0;\n    var exitMs = ((_c = options === null || options === void 0 ? void 0 : options.animations) === null || _c === void 0 ? void 0 : _c.exitMs) || 0;\n    // Show/hide animation\n    React.useEffect(function () {\n        if (visualState === types_1.VisualState.showing) {\n            return;\n        }\n        var duration = visualState === types_1.VisualState.animatingIn ? enterMs : exitMs;\n        var element = outerRef.current;\n        element === null || element === void 0 ? void 0 : element.animate(appearanceAnimationKeyframes, {\n            duration: duration,\n            easing: \n            // TODO: expose easing in options\n            visualState === types_1.VisualState.animatingOut ? \"ease-in\" : \"ease-out\",\n            direction: visualState === types_1.VisualState.animatingOut ? \"reverse\" : \"normal\",\n            fill: \"forwards\",\n        });\n    }, [options, visualState, enterMs, exitMs]);\n    // Height animation\n    var previousHeight = React.useRef();\n    React.useEffect(function () {\n        // Only animate if we're actually showing\n        if (visualState === types_1.VisualState.showing) {\n            var outer_1 = outerRef.current;\n            var inner_1 = innerRef.current;\n            if (!outer_1 || !inner_1) {\n                return;\n            }\n            var ro_1 = new ResizeObserver(function (entries) {\n                for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                    var entry = entries_1[_i];\n                    var cr = entry.contentRect;\n                    if (!previousHeight.current) {\n                        previousHeight.current = cr.height;\n                    }\n                    outer_1.animate([\n                        {\n                            height: previousHeight.current + \"px\",\n                        },\n                        {\n                            height: cr.height + \"px\",\n                        },\n                    ], {\n                        duration: enterMs / 2,\n                        // TODO: expose configs here\n                        easing: \"ease-out\",\n                        fill: \"forwards\",\n                    });\n                    previousHeight.current = cr.height;\n                }\n            });\n            ro_1.observe(inner_1);\n            return function () {\n                ro_1.unobserve(inner_1);\n            };\n        }\n    }, [visualState, options, enterMs, exitMs]);\n    // Bump animation between nested actions\n    var firstRender = React.useRef(true);\n    React.useEffect(function () {\n        if (firstRender.current) {\n            firstRender.current = false;\n            return;\n        }\n        var element = outerRef.current;\n        if (element) {\n            element.animate(bumpAnimationKeyframes, {\n                duration: enterMs,\n                easing: \"ease-out\",\n            });\n        }\n    }, [currentRootActionId, enterMs]);\n    (0, utils_1.useOuterClick)(outerRef, function () {\n        var _a, _b;\n        if (disableCloseOnOuterClick) {\n            return;\n        }\n        query.setVisualState(types_1.VisualState.animatingOut);\n        (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n    });\n    return (React.createElement(\"div\", { ref: outerRef, style: __assign(__assign(__assign({}, appearanceAnimationKeyframes[0]), style), { pointerEvents: \"auto\" }), className: className },\n        React.createElement(\"div\", { ref: innerRef }, children)));\n};\nexports.KBarAnimator = KBarAnimator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/KBarAnimator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/KBarContextProvider.js":
/*!******************************************************!*\
  !*** ./node_modules/kbar/lib/KBarContextProvider.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KBarProvider = exports.KBarContext = void 0;\nvar useStore_1 = __webpack_require__(/*! ./useStore */ \"(ssr)/./node_modules/kbar/lib/useStore.js\");\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar InternalEvents_1 = __webpack_require__(/*! ./InternalEvents */ \"(ssr)/./node_modules/kbar/lib/InternalEvents.js\");\nexports.KBarContext = React.createContext({});\nvar KBarProvider = function (props) {\n    var contextValue = (0, useStore_1.useStore)(props);\n    return (React.createElement(exports.KBarContext.Provider, { value: contextValue },\n        React.createElement(InternalEvents_1.InternalEvents, null),\n        props.children));\n};\nexports.KBarProvider = KBarProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/KBarContextProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/KBarPortal.js":
/*!*********************************************!*\
  !*** ./node_modules/kbar/lib/KBarPortal.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KBarPortal = void 0;\nvar react_portal_1 = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.js\");\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar types_1 = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/kbar/lib/types.js\");\nvar useKBar_1 = __webpack_require__(/*! ./useKBar */ \"(ssr)/./node_modules/kbar/lib/useKBar.js\");\nfunction KBarPortal(_a) {\n    var children = _a.children, container = _a.container;\n    var showing = (0, useKBar_1.useKBar)(function (state) { return ({\n        showing: state.visualState !== types_1.VisualState.hidden,\n    }); }).showing;\n    if (!showing) {\n        return null;\n    }\n    return React.createElement(react_portal_1.Portal, { container: container }, children);\n}\nexports.KBarPortal = KBarPortal;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/KBarPortal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/KBarPositioner.js":
/*!*************************************************!*\
  !*** ./node_modules/kbar/lib/KBarPositioner.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KBarPositioner = void 0;\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar defaultStyle = {\n    position: \"fixed\",\n    display: \"flex\",\n    alignItems: \"flex-start\",\n    justifyContent: \"center\",\n    width: \"100%\",\n    inset: \"0px\",\n    padding: \"14vh 16px 16px\",\n};\nfunction getStyle(style) {\n    return style ? __assign(__assign({}, defaultStyle), style) : defaultStyle;\n}\nexports.KBarPositioner = React.forwardRef(function (_a, ref) {\n    var style = _a.style, children = _a.children, props = __rest(_a, [\"style\", \"children\"]);\n    return (React.createElement(\"div\", __assign({ ref: ref, style: getStyle(style) }, props), children));\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/KBarPositioner.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/KBarResults.js":
/*!**********************************************!*\
  !*** ./node_modules/kbar/lib/KBarResults.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KBarResults = void 0;\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar react_virtual_1 = __webpack_require__(/*! react-virtual */ \"(ssr)/./node_modules/react-virtual/dist/react-virtual.mjs\");\nvar KBarSearch_1 = __webpack_require__(/*! ./KBarSearch */ \"(ssr)/./node_modules/kbar/lib/KBarSearch.js\");\nvar useKBar_1 = __webpack_require__(/*! ./useKBar */ \"(ssr)/./node_modules/kbar/lib/useKBar.js\");\nvar utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/kbar/lib/utils.js\");\nvar START_INDEX = 0;\nvar KBarResults = function (props) {\n    var activeRef = React.useRef(null);\n    var parentRef = React.useRef(null);\n    // store a ref to all items so we do not have to pass\n    // them as a dependency when setting up event listeners.\n    var itemsRef = React.useRef(props.items);\n    itemsRef.current = props.items;\n    var rowVirtualizer = (0, react_virtual_1.useVirtual)({\n        size: itemsRef.current.length,\n        parentRef: parentRef,\n    });\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        search: state.searchQuery,\n        currentRootActionId: state.currentRootActionId,\n        activeIndex: state.activeIndex,\n    }); }), query = _a.query, search = _a.search, currentRootActionId = _a.currentRootActionId, activeIndex = _a.activeIndex, options = _a.options;\n    React.useEffect(function () {\n        var handler = function (event) {\n            var _a;\n            if (event.isComposing) {\n                return;\n            }\n            if (event.key === \"ArrowUp\" || (event.ctrlKey && event.key === \"p\")) {\n                event.preventDefault();\n                event.stopPropagation();\n                query.setActiveIndex(function (index) {\n                    var nextIndex = index > START_INDEX ? index - 1 : index;\n                    // avoid setting active index on a group\n                    if (typeof itemsRef.current[nextIndex] === \"string\") {\n                        if (nextIndex === 0)\n                            return index;\n                        nextIndex -= 1;\n                    }\n                    return nextIndex;\n                });\n            }\n            else if (event.key === \"ArrowDown\" ||\n                (event.ctrlKey && event.key === \"n\")) {\n                event.preventDefault();\n                event.stopPropagation();\n                query.setActiveIndex(function (index) {\n                    var nextIndex = index < itemsRef.current.length - 1 ? index + 1 : index;\n                    // avoid setting active index on a group\n                    if (typeof itemsRef.current[nextIndex] === \"string\") {\n                        if (nextIndex === itemsRef.current.length - 1)\n                            return index;\n                        nextIndex += 1;\n                    }\n                    return nextIndex;\n                });\n            }\n            else if (event.key === \"Enter\") {\n                event.preventDefault();\n                event.stopPropagation();\n                // storing the active dom element in a ref prevents us from\n                // having to calculate the current action to perform based\n                // on the `activeIndex`, which we would have needed to add\n                // as part of the dependencies array.\n                (_a = activeRef.current) === null || _a === void 0 ? void 0 : _a.click();\n            }\n        };\n        window.addEventListener(\"keydown\", handler, { capture: true });\n        return function () { return window.removeEventListener(\"keydown\", handler, { capture: true }); };\n    }, [query]);\n    // destructuring here to prevent linter warning to pass\n    // entire rowVirtualizer in the dependencies array.\n    var scrollToIndex = rowVirtualizer.scrollToIndex;\n    React.useEffect(function () {\n        scrollToIndex(activeIndex, {\n            // ensure that if the first item in the list is a group\n            // name and we are focused on the second item, to not\n            // scroll past that group, hiding it.\n            align: activeIndex <= 1 ? \"end\" : \"auto\",\n        });\n    }, [activeIndex, scrollToIndex]);\n    React.useEffect(function () {\n        // TODO(tim): fix scenario where async actions load in\n        // and active index is reset to the first item. i.e. when\n        // users register actions and bust the `useRegisterActions`\n        // cache, we won't want to reset their active index as they\n        // are navigating the list.\n        query.setActiveIndex(\n        // avoid setting active index on a group\n        typeof props.items[START_INDEX] === \"string\"\n            ? START_INDEX + 1\n            : START_INDEX);\n    }, [search, currentRootActionId, props.items, query]);\n    var execute = React.useCallback(function (item) {\n        var _a, _b;\n        if (typeof item === \"string\")\n            return;\n        if (item.command) {\n            item.command.perform(item);\n            query.toggle();\n        }\n        else {\n            query.setSearch(\"\");\n            query.setCurrentRootAction(item.id);\n        }\n        (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onSelectAction) === null || _b === void 0 ? void 0 : _b.call(_a, item);\n    }, [query, options]);\n    var pointerMoved = (0, utils_1.usePointerMovedSinceMount)();\n    return (React.createElement(\"div\", { ref: parentRef, style: {\n            maxHeight: props.maxHeight || 400,\n            position: \"relative\",\n            overflow: \"auto\",\n        } },\n        React.createElement(\"div\", { role: \"listbox\", id: KBarSearch_1.KBAR_LISTBOX, style: {\n                height: rowVirtualizer.totalSize + \"px\",\n                width: \"100%\",\n            } }, rowVirtualizer.virtualItems.map(function (virtualRow) {\n            var item = itemsRef.current[virtualRow.index];\n            var handlers = typeof item !== \"string\" && {\n                onPointerMove: function () {\n                    return pointerMoved &&\n                        activeIndex !== virtualRow.index &&\n                        query.setActiveIndex(virtualRow.index);\n                },\n                onPointerDown: function () { return query.setActiveIndex(virtualRow.index); },\n                onClick: function () { return execute(item); },\n            };\n            var active = virtualRow.index === activeIndex;\n            return (React.createElement(\"div\", __assign({ ref: active ? activeRef : null, id: (0, KBarSearch_1.getListboxItemId)(virtualRow.index), role: \"option\", \"aria-selected\": active, key: virtualRow.index, style: {\n                    position: \"absolute\",\n                    top: 0,\n                    left: 0,\n                    width: \"100%\",\n                    transform: \"translateY(\" + virtualRow.start + \"px)\",\n                } }, handlers), React.cloneElement(props.onRender({\n                item: item,\n                active: active,\n            }), {\n                ref: virtualRow.measureRef,\n            })));\n        }))));\n};\nexports.KBarResults = KBarResults;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMva2Jhci9saWIvS0JhclJlc3VsdHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0EsaURBQWlELE9BQU87QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsb0NBQW9DLGdCQUFnQjtBQUN2RixDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLDBDQUEwQyw0QkFBNEI7QUFDdEUsQ0FBQztBQUNEO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG1CQUFtQjtBQUNuQix5QkFBeUIsbUJBQU8sQ0FBQyx3R0FBTztBQUN4QyxzQkFBc0IsbUJBQU8sQ0FBQyxnRkFBZTtBQUM3QyxtQkFBbUIsbUJBQU8sQ0FBQyxpRUFBYztBQUN6QyxnQkFBZ0IsbUJBQU8sQ0FBQywyREFBVztBQUNuQyxjQUFjLG1CQUFPLENBQUMsdURBQVM7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCx1REFBdUQ7QUFDdkQ7QUFDQTtBQUNBO0FBQ0EsS0FBSyxJQUFJO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCxlQUFlO0FBQ3JFLDZCQUE2Qix3REFBd0QsZUFBZTtBQUNwRyxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCxxQ0FBcUM7QUFDckM7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsNkNBQTZDLGdEQUFnRDtBQUM3Rix1Q0FBdUMsdUJBQXVCO0FBQzlEO0FBQ0E7QUFDQSwwREFBMEQ7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBLG1CQUFtQiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMva2Jhci9saWIvS0JhclJlc3VsdHMuanM/YmIxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2Fzc2lnbiA9ICh0aGlzICYmIHRoaXMuX19hc3NpZ24pIHx8IGZ1bmN0aW9uICgpIHtcbiAgICBfX2Fzc2lnbiA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24odCkge1xuICAgICAgICBmb3IgKHZhciBzLCBpID0gMSwgbiA9IGFyZ3VtZW50cy5sZW5ndGg7IGkgPCBuOyBpKyspIHtcbiAgICAgICAgICAgIHMgPSBhcmd1bWVudHNbaV07XG4gICAgICAgICAgICBmb3IgKHZhciBwIGluIHMpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgcCkpXG4gICAgICAgICAgICAgICAgdFtwXSA9IHNbcF07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHQ7XG4gICAgfTtcbiAgICByZXR1cm4gX19hc3NpZ24uYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn07XG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfSk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgb1trMl0gPSBtW2tdO1xufSkpO1xudmFyIF9fc2V0TW9kdWxlRGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19zZXRNb2R1bGVEZWZhdWx0KSB8fCAoT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCB2KSB7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIFwiZGVmYXVsdFwiLCB7IGVudW1lcmFibGU6IHRydWUsIHZhbHVlOiB2IH0pO1xufSkgOiBmdW5jdGlvbihvLCB2KSB7XG4gICAgb1tcImRlZmF1bHRcIl0gPSB2O1xufSk7XG52YXIgX19pbXBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydFN0YXIpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICBpZiAobW9kICYmIG1vZC5fX2VzTW9kdWxlKSByZXR1cm4gbW9kO1xuICAgIHZhciByZXN1bHQgPSB7fTtcbiAgICBpZiAobW9kICE9IG51bGwpIGZvciAodmFyIGsgaW4gbW9kKSBpZiAoayAhPT0gXCJkZWZhdWx0XCIgJiYgT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKG1vZCwgaykpIF9fY3JlYXRlQmluZGluZyhyZXN1bHQsIG1vZCwgayk7XG4gICAgX19zZXRNb2R1bGVEZWZhdWx0KHJlc3VsdCwgbW9kKTtcbiAgICByZXR1cm4gcmVzdWx0O1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuS0JhclJlc3VsdHMgPSB2b2lkIDA7XG52YXIgUmVhY3QgPSBfX2ltcG9ydFN0YXIocmVxdWlyZShcInJlYWN0XCIpKTtcbnZhciByZWFjdF92aXJ0dWFsXzEgPSByZXF1aXJlKFwicmVhY3QtdmlydHVhbFwiKTtcbnZhciBLQmFyU2VhcmNoXzEgPSByZXF1aXJlKFwiLi9LQmFyU2VhcmNoXCIpO1xudmFyIHVzZUtCYXJfMSA9IHJlcXVpcmUoXCIuL3VzZUtCYXJcIik7XG52YXIgdXRpbHNfMSA9IHJlcXVpcmUoXCIuL3V0aWxzXCIpO1xudmFyIFNUQVJUX0lOREVYID0gMDtcbnZhciBLQmFyUmVzdWx0cyA9IGZ1bmN0aW9uIChwcm9wcykge1xuICAgIHZhciBhY3RpdmVSZWYgPSBSZWFjdC51c2VSZWYobnVsbCk7XG4gICAgdmFyIHBhcmVudFJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgICAvLyBzdG9yZSBhIHJlZiB0byBhbGwgaXRlbXMgc28gd2UgZG8gbm90IGhhdmUgdG8gcGFzc1xuICAgIC8vIHRoZW0gYXMgYSBkZXBlbmRlbmN5IHdoZW4gc2V0dGluZyB1cCBldmVudCBsaXN0ZW5lcnMuXG4gICAgdmFyIGl0ZW1zUmVmID0gUmVhY3QudXNlUmVmKHByb3BzLml0ZW1zKTtcbiAgICBpdGVtc1JlZi5jdXJyZW50ID0gcHJvcHMuaXRlbXM7XG4gICAgdmFyIHJvd1ZpcnR1YWxpemVyID0gKDAsIHJlYWN0X3ZpcnR1YWxfMS51c2VWaXJ0dWFsKSh7XG4gICAgICAgIHNpemU6IGl0ZW1zUmVmLmN1cnJlbnQubGVuZ3RoLFxuICAgICAgICBwYXJlbnRSZWY6IHBhcmVudFJlZixcbiAgICB9KTtcbiAgICB2YXIgX2EgPSAoMCwgdXNlS0Jhcl8xLnVzZUtCYXIpKGZ1bmN0aW9uIChzdGF0ZSkgeyByZXR1cm4gKHtcbiAgICAgICAgc2VhcmNoOiBzdGF0ZS5zZWFyY2hRdWVyeSxcbiAgICAgICAgY3VycmVudFJvb3RBY3Rpb25JZDogc3RhdGUuY3VycmVudFJvb3RBY3Rpb25JZCxcbiAgICAgICAgYWN0aXZlSW5kZXg6IHN0YXRlLmFjdGl2ZUluZGV4LFxuICAgIH0pOyB9KSwgcXVlcnkgPSBfYS5xdWVyeSwgc2VhcmNoID0gX2Euc2VhcmNoLCBjdXJyZW50Um9vdEFjdGlvbklkID0gX2EuY3VycmVudFJvb3RBY3Rpb25JZCwgYWN0aXZlSW5kZXggPSBfYS5hY3RpdmVJbmRleCwgb3B0aW9ucyA9IF9hLm9wdGlvbnM7XG4gICAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIGhhbmRsZXIgPSBmdW5jdGlvbiAoZXZlbnQpIHtcbiAgICAgICAgICAgIHZhciBfYTtcbiAgICAgICAgICAgIGlmIChldmVudC5pc0NvbXBvc2luZykge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChldmVudC5rZXkgPT09IFwiQXJyb3dVcFwiIHx8IChldmVudC5jdHJsS2V5ICYmIGV2ZW50LmtleSA9PT0gXCJwXCIpKSB7XG4gICAgICAgICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICBldmVudC5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICBxdWVyeS5zZXRBY3RpdmVJbmRleChmdW5jdGlvbiAoaW5kZXgpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIG5leHRJbmRleCA9IGluZGV4ID4gU1RBUlRfSU5ERVggPyBpbmRleCAtIDEgOiBpbmRleDtcbiAgICAgICAgICAgICAgICAgICAgLy8gYXZvaWQgc2V0dGluZyBhY3RpdmUgaW5kZXggb24gYSBncm91cFxuICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGl0ZW1zUmVmLmN1cnJlbnRbbmV4dEluZGV4XSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG5leHRJbmRleCA9PT0gMClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gaW5kZXg7XG4gICAgICAgICAgICAgICAgICAgICAgICBuZXh0SW5kZXggLT0gMTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbmV4dEluZGV4O1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAoZXZlbnQua2V5ID09PSBcIkFycm93RG93blwiIHx8XG4gICAgICAgICAgICAgICAgKGV2ZW50LmN0cmxLZXkgJiYgZXZlbnQua2V5ID09PSBcIm5cIikpIHtcbiAgICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgIGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgIHF1ZXJ5LnNldEFjdGl2ZUluZGV4KGZ1bmN0aW9uIChpbmRleCkge1xuICAgICAgICAgICAgICAgICAgICB2YXIgbmV4dEluZGV4ID0gaW5kZXggPCBpdGVtc1JlZi5jdXJyZW50Lmxlbmd0aCAtIDEgPyBpbmRleCArIDEgOiBpbmRleDtcbiAgICAgICAgICAgICAgICAgICAgLy8gYXZvaWQgc2V0dGluZyBhY3RpdmUgaW5kZXggb24gYSBncm91cFxuICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGl0ZW1zUmVmLmN1cnJlbnRbbmV4dEluZGV4XSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG5leHRJbmRleCA9PT0gaXRlbXNSZWYuY3VycmVudC5sZW5ndGggLSAxKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBpbmRleDtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5leHRJbmRleCArPSAxO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXh0SW5kZXg7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChldmVudC5rZXkgPT09IFwiRW50ZXJcIikge1xuICAgICAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgZXZlbnQuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgLy8gc3RvcmluZyB0aGUgYWN0aXZlIGRvbSBlbGVtZW50IGluIGEgcmVmIHByZXZlbnRzIHVzIGZyb21cbiAgICAgICAgICAgICAgICAvLyBoYXZpbmcgdG8gY2FsY3VsYXRlIHRoZSBjdXJyZW50IGFjdGlvbiB0byBwZXJmb3JtIGJhc2VkXG4gICAgICAgICAgICAgICAgLy8gb24gdGhlIGBhY3RpdmVJbmRleGAsIHdoaWNoIHdlIHdvdWxkIGhhdmUgbmVlZGVkIHRvIGFkZFxuICAgICAgICAgICAgICAgIC8vIGFzIHBhcnQgb2YgdGhlIGRlcGVuZGVuY2llcyBhcnJheS5cbiAgICAgICAgICAgICAgICAoX2EgPSBhY3RpdmVSZWYuY3VycmVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNsaWNrKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCBoYW5kbGVyLCB7IGNhcHR1cmU6IHRydWUgfSk7XG4gICAgICAgIHJldHVybiBmdW5jdGlvbiAoKSB7IHJldHVybiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlciwgeyBjYXB0dXJlOiB0cnVlIH0pOyB9O1xuICAgIH0sIFtxdWVyeV0pO1xuICAgIC8vIGRlc3RydWN0dXJpbmcgaGVyZSB0byBwcmV2ZW50IGxpbnRlciB3YXJuaW5nIHRvIHBhc3NcbiAgICAvLyBlbnRpcmUgcm93VmlydHVhbGl6ZXIgaW4gdGhlIGRlcGVuZGVuY2llcyBhcnJheS5cbiAgICB2YXIgc2Nyb2xsVG9JbmRleCA9IHJvd1ZpcnR1YWxpemVyLnNjcm9sbFRvSW5kZXg7XG4gICAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgc2Nyb2xsVG9JbmRleChhY3RpdmVJbmRleCwge1xuICAgICAgICAgICAgLy8gZW5zdXJlIHRoYXQgaWYgdGhlIGZpcnN0IGl0ZW0gaW4gdGhlIGxpc3QgaXMgYSBncm91cFxuICAgICAgICAgICAgLy8gbmFtZSBhbmQgd2UgYXJlIGZvY3VzZWQgb24gdGhlIHNlY29uZCBpdGVtLCB0byBub3RcbiAgICAgICAgICAgIC8vIHNjcm9sbCBwYXN0IHRoYXQgZ3JvdXAsIGhpZGluZyBpdC5cbiAgICAgICAgICAgIGFsaWduOiBhY3RpdmVJbmRleCA8PSAxID8gXCJlbmRcIiA6IFwiYXV0b1wiLFxuICAgICAgICB9KTtcbiAgICB9LCBbYWN0aXZlSW5kZXgsIHNjcm9sbFRvSW5kZXhdKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgICAgICAvLyBUT0RPKHRpbSk6IGZpeCBzY2VuYXJpbyB3aGVyZSBhc3luYyBhY3Rpb25zIGxvYWQgaW5cbiAgICAgICAgLy8gYW5kIGFjdGl2ZSBpbmRleCBpcyByZXNldCB0byB0aGUgZmlyc3QgaXRlbS4gaS5lLiB3aGVuXG4gICAgICAgIC8vIHVzZXJzIHJlZ2lzdGVyIGFjdGlvbnMgYW5kIGJ1c3QgdGhlIGB1c2VSZWdpc3RlckFjdGlvbnNgXG4gICAgICAgIC8vIGNhY2hlLCB3ZSB3b24ndCB3YW50IHRvIHJlc2V0IHRoZWlyIGFjdGl2ZSBpbmRleCBhcyB0aGV5XG4gICAgICAgIC8vIGFyZSBuYXZpZ2F0aW5nIHRoZSBsaXN0LlxuICAgICAgICBxdWVyeS5zZXRBY3RpdmVJbmRleChcbiAgICAgICAgLy8gYXZvaWQgc2V0dGluZyBhY3RpdmUgaW5kZXggb24gYSBncm91cFxuICAgICAgICB0eXBlb2YgcHJvcHMuaXRlbXNbU1RBUlRfSU5ERVhdID09PSBcInN0cmluZ1wiXG4gICAgICAgICAgICA/IFNUQVJUX0lOREVYICsgMVxuICAgICAgICAgICAgOiBTVEFSVF9JTkRFWCk7XG4gICAgfSwgW3NlYXJjaCwgY3VycmVudFJvb3RBY3Rpb25JZCwgcHJvcHMuaXRlbXMsIHF1ZXJ5XSk7XG4gICAgdmFyIGV4ZWN1dGUgPSBSZWFjdC51c2VDYWxsYmFjayhmdW5jdGlvbiAoaXRlbSkge1xuICAgICAgICB2YXIgX2EsIF9iO1xuICAgICAgICBpZiAodHlwZW9mIGl0ZW0gPT09IFwic3RyaW5nXCIpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIGlmIChpdGVtLmNvbW1hbmQpIHtcbiAgICAgICAgICAgIGl0ZW0uY29tbWFuZC5wZXJmb3JtKGl0ZW0pO1xuICAgICAgICAgICAgcXVlcnkudG9nZ2xlKCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBxdWVyeS5zZXRTZWFyY2goXCJcIik7XG4gICAgICAgICAgICBxdWVyeS5zZXRDdXJyZW50Um9vdEFjdGlvbihpdGVtLmlkKTtcbiAgICAgICAgfVxuICAgICAgICAoX2IgPSAoX2EgPSBvcHRpb25zLmNhbGxiYWNrcykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLm9uU2VsZWN0QWN0aW9uKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IuY2FsbChfYSwgaXRlbSk7XG4gICAgfSwgW3F1ZXJ5LCBvcHRpb25zXSk7XG4gICAgdmFyIHBvaW50ZXJNb3ZlZCA9ICgwLCB1dGlsc18xLnVzZVBvaW50ZXJNb3ZlZFNpbmNlTW91bnQpKCk7XG4gICAgcmV0dXJuIChSZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHsgcmVmOiBwYXJlbnRSZWYsIHN0eWxlOiB7XG4gICAgICAgICAgICBtYXhIZWlnaHQ6IHByb3BzLm1heEhlaWdodCB8fCA0MDAsXG4gICAgICAgICAgICBwb3NpdGlvbjogXCJyZWxhdGl2ZVwiLFxuICAgICAgICAgICAgb3ZlcmZsb3c6IFwiYXV0b1wiLFxuICAgICAgICB9IH0sXG4gICAgICAgIFJlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwgeyByb2xlOiBcImxpc3Rib3hcIiwgaWQ6IEtCYXJTZWFyY2hfMS5LQkFSX0xJU1RCT1gsIHN0eWxlOiB7XG4gICAgICAgICAgICAgICAgaGVpZ2h0OiByb3dWaXJ0dWFsaXplci50b3RhbFNpemUgKyBcInB4XCIsXG4gICAgICAgICAgICAgICAgd2lkdGg6IFwiMTAwJVwiLFxuICAgICAgICAgICAgfSB9LCByb3dWaXJ0dWFsaXplci52aXJ0dWFsSXRlbXMubWFwKGZ1bmN0aW9uICh2aXJ0dWFsUm93KSB7XG4gICAgICAgICAgICB2YXIgaXRlbSA9IGl0ZW1zUmVmLmN1cnJlbnRbdmlydHVhbFJvdy5pbmRleF07XG4gICAgICAgICAgICB2YXIgaGFuZGxlcnMgPSB0eXBlb2YgaXRlbSAhPT0gXCJzdHJpbmdcIiAmJiB7XG4gICAgICAgICAgICAgICAgb25Qb2ludGVyTW92ZTogZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gcG9pbnRlck1vdmVkICYmXG4gICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVJbmRleCAhPT0gdmlydHVhbFJvdy5pbmRleCAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgcXVlcnkuc2V0QWN0aXZlSW5kZXgodmlydHVhbFJvdy5pbmRleCk7XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBvblBvaW50ZXJEb3duOiBmdW5jdGlvbiAoKSB7IHJldHVybiBxdWVyeS5zZXRBY3RpdmVJbmRleCh2aXJ0dWFsUm93LmluZGV4KTsgfSxcbiAgICAgICAgICAgICAgICBvbkNsaWNrOiBmdW5jdGlvbiAoKSB7IHJldHVybiBleGVjdXRlKGl0ZW0pOyB9LFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHZhciBhY3RpdmUgPSB2aXJ0dWFsUm93LmluZGV4ID09PSBhY3RpdmVJbmRleDtcbiAgICAgICAgICAgIHJldHVybiAoUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCBfX2Fzc2lnbih7IHJlZjogYWN0aXZlID8gYWN0aXZlUmVmIDogbnVsbCwgaWQ6ICgwLCBLQmFyU2VhcmNoXzEuZ2V0TGlzdGJveEl0ZW1JZCkodmlydHVhbFJvdy5pbmRleCksIHJvbGU6IFwib3B0aW9uXCIsIFwiYXJpYS1zZWxlY3RlZFwiOiBhY3RpdmUsIGtleTogdmlydHVhbFJvdy5pbmRleCwgc3R5bGU6IHtcbiAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246IFwiYWJzb2x1dGVcIixcbiAgICAgICAgICAgICAgICAgICAgdG9wOiAwLFxuICAgICAgICAgICAgICAgICAgICBsZWZ0OiAwLFxuICAgICAgICAgICAgICAgICAgICB3aWR0aDogXCIxMDAlXCIsXG4gICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogXCJ0cmFuc2xhdGVZKFwiICsgdmlydHVhbFJvdy5zdGFydCArIFwicHgpXCIsXG4gICAgICAgICAgICAgICAgfSB9LCBoYW5kbGVycyksIFJlYWN0LmNsb25lRWxlbWVudChwcm9wcy5vblJlbmRlcih7XG4gICAgICAgICAgICAgICAgaXRlbTogaXRlbSxcbiAgICAgICAgICAgICAgICBhY3RpdmU6IGFjdGl2ZSxcbiAgICAgICAgICAgIH0pLCB7XG4gICAgICAgICAgICAgICAgcmVmOiB2aXJ0dWFsUm93Lm1lYXN1cmVSZWYsXG4gICAgICAgICAgICB9KSkpO1xuICAgICAgICB9KSkpKTtcbn07XG5leHBvcnRzLktCYXJSZXN1bHRzID0gS0JhclJlc3VsdHM7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/KBarResults.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/KBarSearch.js":
/*!*********************************************!*\
  !*** ./node_modules/kbar/lib/KBarSearch.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KBarSearch = exports.getListboxItemId = exports.KBAR_LISTBOX = void 0;\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar types_1 = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/kbar/lib/types.js\");\nvar useKBar_1 = __webpack_require__(/*! ./useKBar */ \"(ssr)/./node_modules/kbar/lib/useKBar.js\");\nexports.KBAR_LISTBOX = \"kbar-listbox\";\nvar getListboxItemId = function (id) { return \"kbar-listbox-item-\" + id; };\nexports.getListboxItemId = getListboxItemId;\nfunction KBarSearch(props) {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        search: state.searchQuery,\n        currentRootActionId: state.currentRootActionId,\n        actions: state.actions,\n        activeIndex: state.activeIndex,\n        showing: state.visualState === types_1.VisualState.showing,\n    }); }), query = _a.query, search = _a.search, actions = _a.actions, currentRootActionId = _a.currentRootActionId, activeIndex = _a.activeIndex, showing = _a.showing, options = _a.options;\n    var _b = React.useState(search), inputValue = _b[0], setInputValue = _b[1];\n    React.useEffect(function () {\n        query.setSearch(inputValue);\n    }, [inputValue, query]);\n    var defaultPlaceholder = props.defaultPlaceholder, rest = __rest(props, [\"defaultPlaceholder\"]);\n    React.useEffect(function () {\n        query.setSearch(\"\");\n        query.getInput().focus();\n        return function () { return query.setSearch(\"\"); };\n    }, [currentRootActionId, query]);\n    var placeholder = React.useMemo(function () {\n        var defaultText = defaultPlaceholder !== null && defaultPlaceholder !== void 0 ? defaultPlaceholder : \"Type a command or search…\";\n        return currentRootActionId && actions[currentRootActionId]\n            ? actions[currentRootActionId].name\n            : defaultText;\n    }, [actions, currentRootActionId, defaultPlaceholder]);\n    return (React.createElement(\"input\", __assign({}, rest, { ref: query.inputRefSetter, autoFocus: true, autoComplete: \"off\", role: \"combobox\", spellCheck: \"false\", \"aria-expanded\": showing, \"aria-controls\": exports.KBAR_LISTBOX, \"aria-activedescendant\": (0, exports.getListboxItemId)(activeIndex), value: inputValue, placeholder: placeholder, onChange: function (event) {\n            var _a, _b, _c;\n            (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, event);\n            setInputValue(event.target.value);\n            (_c = (_b = options === null || options === void 0 ? void 0 : options.callbacks) === null || _b === void 0 ? void 0 : _b.onQueryChange) === null || _c === void 0 ? void 0 : _c.call(_b, event.target.value);\n        }, onKeyDown: function (event) {\n            var _a;\n            (_a = props.onKeyDown) === null || _a === void 0 ? void 0 : _a.call(props, event);\n            if (currentRootActionId && !search && event.key === \"Backspace\") {\n                var parent_1 = actions[currentRootActionId].parent;\n                query.setCurrentRootAction(parent_1);\n            }\n        } })));\n}\nexports.KBarSearch = KBarSearch;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/KBarSearch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/action/ActionImpl.js":
/*!****************************************************!*\
  !*** ./node_modules/kbar/lib/action/ActionImpl.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ActionImpl = void 0;\nvar tiny_invariant_1 = __importDefault(__webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/tiny-invariant.cjs.js\"));\nvar Command_1 = __webpack_require__(/*! ./Command */ \"(ssr)/./node_modules/kbar/lib/action/Command.js\");\nvar utils_1 = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/kbar/lib/utils.js\");\n/**\n * Extends the configured keywords to include the section\n * This allows section names to be searched for.\n */\nvar extendKeywords = function (_a) {\n    var _b = _a.keywords, keywords = _b === void 0 ? \"\" : _b, _c = _a.section, section = _c === void 0 ? \"\" : _c;\n    return (keywords + \" \" + (typeof section === \"string\" ? section : section.name)).trim();\n};\nvar ActionImpl = /** @class */ (function () {\n    function ActionImpl(action, options) {\n        var _this = this;\n        var _a;\n        this.priority = utils_1.Priority.NORMAL;\n        this.ancestors = [];\n        this.children = [];\n        Object.assign(this, action);\n        this.id = action.id;\n        this.name = action.name;\n        this.keywords = extendKeywords(action);\n        var perform = action.perform;\n        this.command =\n            perform &&\n                new Command_1.Command({\n                    perform: function () { return perform(_this); },\n                }, {\n                    history: options.history,\n                });\n        // Backwards compatibility\n        this.perform = (_a = this.command) === null || _a === void 0 ? void 0 : _a.perform;\n        if (action.parent) {\n            var parentActionImpl = options.store[action.parent];\n            (0, tiny_invariant_1.default)(parentActionImpl, \"attempted to create an action whos parent: \" + action.parent + \" does not exist in the store.\");\n            parentActionImpl.addChild(this);\n        }\n    }\n    ActionImpl.prototype.addChild = function (childActionImpl) {\n        // add all ancestors for the child action\n        childActionImpl.ancestors.unshift(this);\n        var parent = this.parentActionImpl;\n        while (parent) {\n            childActionImpl.ancestors.unshift(parent);\n            parent = parent.parentActionImpl;\n        }\n        // we ensure that order of adding always goes\n        // parent -> children, so no need to recurse\n        this.children.push(childActionImpl);\n    };\n    ActionImpl.prototype.removeChild = function (actionImpl) {\n        var _this = this;\n        // recursively remove all children\n        var index = this.children.indexOf(actionImpl);\n        if (index !== -1) {\n            this.children.splice(index, 1);\n        }\n        if (actionImpl.children) {\n            actionImpl.children.forEach(function (child) {\n                _this.removeChild(child);\n            });\n        }\n    };\n    Object.defineProperty(ActionImpl.prototype, \"parentActionImpl\", {\n        // easily access parentActionImpl after creation\n        get: function () {\n            return this.ancestors[this.ancestors.length - 1];\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ActionImpl.create = function (action, options) {\n        return new ActionImpl(action, options);\n    };\n    return ActionImpl;\n}());\nexports.ActionImpl = ActionImpl;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/action/ActionImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/action/ActionInterface.js":
/*!*********************************************************!*\
  !*** ./node_modules/kbar/lib/action/ActionInterface.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ActionInterface = void 0;\nvar tiny_invariant_1 = __importDefault(__webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/tiny-invariant.cjs.js\"));\nvar ActionImpl_1 = __webpack_require__(/*! ./ActionImpl */ \"(ssr)/./node_modules/kbar/lib/action/ActionImpl.js\");\nvar ActionInterface = /** @class */ (function () {\n    function ActionInterface(actions, options) {\n        if (actions === void 0) { actions = []; }\n        if (options === void 0) { options = {}; }\n        this.actions = {};\n        this.options = options;\n        this.add(actions);\n    }\n    ActionInterface.prototype.add = function (actions) {\n        for (var i = 0; i < actions.length; i++) {\n            var action = actions[i];\n            if (action.parent) {\n                (0, tiny_invariant_1.default)(this.actions[action.parent], \"Attempted to create action \\\"\" + action.name + \"\\\" without registering its parent \\\"\" + action.parent + \"\\\" first.\");\n            }\n            this.actions[action.id] = ActionImpl_1.ActionImpl.create(action, {\n                history: this.options.historyManager,\n                store: this.actions,\n            });\n        }\n        return __assign({}, this.actions);\n    };\n    ActionInterface.prototype.remove = function (actions) {\n        var _this = this;\n        actions.forEach(function (action) {\n            var actionImpl = _this.actions[action.id];\n            if (!actionImpl)\n                return;\n            var children = actionImpl.children;\n            while (children.length) {\n                var child = children.pop();\n                if (!child)\n                    return;\n                delete _this.actions[child.id];\n                if (child.parentActionImpl)\n                    child.parentActionImpl.removeChild(child);\n                if (child.children)\n                    children.push.apply(children, child.children);\n            }\n            if (actionImpl.parentActionImpl) {\n                actionImpl.parentActionImpl.removeChild(actionImpl);\n            }\n            delete _this.actions[action.id];\n        });\n        return __assign({}, this.actions);\n    };\n    return ActionInterface;\n}());\nexports.ActionInterface = ActionInterface;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/action/ActionInterface.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/action/Command.js":
/*!*************************************************!*\
  !*** ./node_modules/kbar/lib/action/Command.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Command = void 0;\nvar Command = /** @class */ (function () {\n    function Command(command, options) {\n        var _this = this;\n        if (options === void 0) { options = {}; }\n        this.perform = function () {\n            var negate = command.perform();\n            // no need for history if non negatable\n            if (typeof negate !== \"function\")\n                return;\n            // return if no history enabled\n            var history = options.history;\n            if (!history)\n                return;\n            // since we are performing the same action, we'll clean up the\n            // previous call to the action and create a new history record\n            if (_this.historyItem) {\n                history.remove(_this.historyItem);\n            }\n            _this.historyItem = history.add({\n                perform: command.perform,\n                negate: negate,\n            });\n            _this.history = {\n                undo: function () { return history.undo(_this.historyItem); },\n                redo: function () { return history.redo(_this.historyItem); },\n            };\n        };\n    }\n    return Command;\n}());\nexports.Command = Command;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/action/Command.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/action/HistoryImpl.js":
/*!*****************************************************!*\
  !*** ./node_modules/kbar/lib/action/HistoryImpl.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.history = exports.HistoryItemImpl = void 0;\nvar utils_1 = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/kbar/lib/utils.js\");\nvar HistoryItemImpl = /** @class */ (function () {\n    function HistoryItemImpl(item) {\n        this.perform = item.perform;\n        this.negate = item.negate;\n    }\n    HistoryItemImpl.create = function (item) {\n        return new HistoryItemImpl(item);\n    };\n    return HistoryItemImpl;\n}());\nexports.HistoryItemImpl = HistoryItemImpl;\nvar HistoryImpl = /** @class */ (function () {\n    function HistoryImpl() {\n        this.undoStack = [];\n        this.redoStack = [];\n        if (!HistoryImpl.instance) {\n            HistoryImpl.instance = this;\n            this.init();\n        }\n        return HistoryImpl.instance;\n    }\n    HistoryImpl.prototype.init = function () {\n        var _this = this;\n        if (typeof window === \"undefined\")\n            return;\n        window.addEventListener(\"keydown\", function (event) {\n            var _a;\n            if ((!_this.redoStack.length && !_this.undoStack.length) ||\n                (0, utils_1.shouldRejectKeystrokes)()) {\n                return;\n            }\n            var key = (_a = event.key) === null || _a === void 0 ? void 0 : _a.toLowerCase();\n            if (event.metaKey && key === \"z\" && event.shiftKey) {\n                _this.redo();\n            }\n            else if (event.metaKey && key === \"z\") {\n                _this.undo();\n            }\n        });\n    };\n    HistoryImpl.prototype.add = function (item) {\n        var historyItem = HistoryItemImpl.create(item);\n        this.undoStack.push(historyItem);\n        return historyItem;\n    };\n    HistoryImpl.prototype.remove = function (item) {\n        var undoIndex = this.undoStack.findIndex(function (i) { return i === item; });\n        if (undoIndex !== -1) {\n            this.undoStack.splice(undoIndex, 1);\n            return;\n        }\n        var redoIndex = this.redoStack.findIndex(function (i) { return i === item; });\n        if (redoIndex !== -1) {\n            this.redoStack.splice(redoIndex, 1);\n        }\n    };\n    HistoryImpl.prototype.undo = function (item) {\n        // if not undoing a specific item, just undo the latest\n        if (!item) {\n            var item_1 = this.undoStack.pop();\n            if (!item_1)\n                return;\n            item_1 === null || item_1 === void 0 ? void 0 : item_1.negate();\n            this.redoStack.push(item_1);\n            return item_1;\n        }\n        // else undo the specific item\n        var index = this.undoStack.findIndex(function (i) { return i === item; });\n        if (index === -1)\n            return;\n        this.undoStack.splice(index, 1);\n        item.negate();\n        this.redoStack.push(item);\n        return item;\n    };\n    HistoryImpl.prototype.redo = function (item) {\n        if (!item) {\n            var item_2 = this.redoStack.pop();\n            if (!item_2)\n                return;\n            item_2 === null || item_2 === void 0 ? void 0 : item_2.perform();\n            this.undoStack.push(item_2);\n            return item_2;\n        }\n        var index = this.redoStack.findIndex(function (i) { return i === item; });\n        if (index === -1)\n            return;\n        this.redoStack.splice(index, 1);\n        item.perform();\n        this.undoStack.push(item);\n        return item;\n    };\n    HistoryImpl.prototype.reset = function () {\n        this.undoStack.splice(0);\n        this.redoStack.splice(0);\n    };\n    return HistoryImpl;\n}());\nvar history = new HistoryImpl();\nexports.history = history;\nObject.freeze(history);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/action/HistoryImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/action/index.js":
/*!***********************************************!*\
  !*** ./node_modules/kbar/lib/action/index.js ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./ActionInterface */ \"(ssr)/./node_modules/kbar/lib/action/ActionInterface.js\"), exports);\n__exportStar(__webpack_require__(/*! ./ActionImpl */ \"(ssr)/./node_modules/kbar/lib/action/ActionImpl.js\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMva2Jhci9saWIvYWN0aW9uL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBLG1DQUFtQyxvQ0FBb0MsZ0JBQWdCO0FBQ3ZGLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxhQUFhLG1CQUFPLENBQUMsa0ZBQW1CO0FBQ3hDLGFBQWEsbUJBQU8sQ0FBQyx3RUFBYyIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMva2Jhci9saWIvYWN0aW9uL2luZGV4LmpzP2ZjY2MiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfSk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgb1trMl0gPSBtW2tdO1xufSkpO1xudmFyIF9fZXhwb3J0U3RhciA9ICh0aGlzICYmIHRoaXMuX19leHBvcnRTdGFyKSB8fCBmdW5jdGlvbihtLCBleHBvcnRzKSB7XG4gICAgZm9yICh2YXIgcCBpbiBtKSBpZiAocCAhPT0gXCJkZWZhdWx0XCIgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChleHBvcnRzLCBwKSkgX19jcmVhdGVCaW5kaW5nKGV4cG9ydHMsIG0sIHApO1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9BY3Rpb25JbnRlcmZhY2VcIiksIGV4cG9ydHMpO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL0FjdGlvbkltcGxcIiksIGV4cG9ydHMpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/action/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/index.js":
/*!****************************************!*\
  !*** ./node_modules/kbar/lib/index.js ***!
  \****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Priority = exports.createAction = void 0;\nvar utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/kbar/lib/utils.js\");\nObject.defineProperty(exports, \"createAction\", ({ enumerable: true, get: function () { return utils_1.createAction; } }));\nObject.defineProperty(exports, \"Priority\", ({ enumerable: true, get: function () { return utils_1.Priority; } }));\n__exportStar(__webpack_require__(/*! ./useMatches */ \"(ssr)/./node_modules/kbar/lib/useMatches.js\"), exports);\n__exportStar(__webpack_require__(/*! ./KBarPortal */ \"(ssr)/./node_modules/kbar/lib/KBarPortal.js\"), exports);\n__exportStar(__webpack_require__(/*! ./KBarPositioner */ \"(ssr)/./node_modules/kbar/lib/KBarPositioner.js\"), exports);\n__exportStar(__webpack_require__(/*! ./KBarSearch */ \"(ssr)/./node_modules/kbar/lib/KBarSearch.js\"), exports);\n__exportStar(__webpack_require__(/*! ./KBarResults */ \"(ssr)/./node_modules/kbar/lib/KBarResults.js\"), exports);\n__exportStar(__webpack_require__(/*! ./useKBar */ \"(ssr)/./node_modules/kbar/lib/useKBar.js\"), exports);\n__exportStar(__webpack_require__(/*! ./useRegisterActions */ \"(ssr)/./node_modules/kbar/lib/useRegisterActions.js\"), exports);\n__exportStar(__webpack_require__(/*! ./KBarContextProvider */ \"(ssr)/./node_modules/kbar/lib/KBarContextProvider.js\"), exports);\n__exportStar(__webpack_require__(/*! ./KBarAnimator */ \"(ssr)/./node_modules/kbar/lib/KBarAnimator.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/./node_modules/kbar/lib/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./action */ \"(ssr)/./node_modules/kbar/lib/action/index.js\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/tinykeys.js":
/*!*******************************************!*\
  !*** ./node_modules/kbar/lib/tinykeys.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Fixes special character issues; `?` -> `shift+/` + build issue\n// https://github.com/jamiebuilds/tinykeys\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * These are the modifier keys that change the meaning of keybindings.\n *\n * Note: Ignoring \"AltGraph\" because it is covered by the others.\n */\nvar KEYBINDING_MODIFIER_KEYS = [\"Shift\", \"Meta\", \"Alt\", \"Control\"];\n/**\n * Keybinding sequences should timeout if individual key presses are more than\n * 1s apart by default.\n */\nvar DEFAULT_TIMEOUT = 1000;\n/**\n * Keybinding sequences should bind to this event by default.\n */\nvar DEFAULT_EVENT = \"keydown\";\n/**\n * An alias for creating platform-specific keybinding aliases.\n */\nvar MOD = typeof navigator === \"object\" &&\n    /Mac|iPod|iPhone|iPad/.test(navigator.platform)\n    ? \"Meta\"\n    : \"Control\";\n/**\n * There's a bug in Chrome that causes event.getModifierState not to exist on\n * KeyboardEvent's for F1/F2/etc keys.\n */\nfunction getModifierState(event, mod) {\n    return typeof event.getModifierState === \"function\"\n        ? event.getModifierState(mod)\n        : false;\n}\n/**\n * Parses a \"Key Binding String\" into its parts\n *\n * grammar    = `<sequence>`\n * <sequence> = `<press> <press> <press> ...`\n * <press>    = `<key>` or `<mods>+<key>`\n * <mods>     = `<mod>+<mod>+...`\n */\nfunction parse(str) {\n    return str\n        .trim()\n        .split(\" \")\n        .map(function (press) {\n        var mods = press.split(/\\b\\+/);\n        var key = mods.pop();\n        mods = mods.map(function (mod) { return (mod === \"$mod\" ? MOD : mod); });\n        return [mods, key];\n    });\n}\n/**\n * This tells us if a series of events matches a key binding sequence either\n * partially or exactly.\n */\nfunction match(event, press) {\n    // Special characters; `?` `!`\n    if (/^[^A-Za-z0-9]$/.test(event.key) && press[1] === event.key) {\n        return true;\n    }\n    // prettier-ignore\n    return !(\n    // Allow either the `event.key` or the `event.code`\n    // MDN event.key: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key\n    // MDN event.code: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/code\n    (press[1].toUpperCase() !== event.key.toUpperCase() &&\n        press[1] !== event.code) ||\n        // Ensure all the modifiers in the keybinding are pressed.\n        press[0].find(function (mod) {\n            return !getModifierState(event, mod);\n        }) ||\n        // KEYBINDING_MODIFIER_KEYS (Shift/Control/etc) change the meaning of a\n        // keybinding. So if they are pressed but aren't part of the current\n        // keybinding press, then we don't have a match.\n        KEYBINDING_MODIFIER_KEYS.find(function (mod) {\n            return !press[0].includes(mod) && press[1] !== mod && getModifierState(event, mod);\n        }));\n}\n/**\n * Subscribes to keybindings.\n *\n * Returns an unsubscribe method.\n *\n * @example\n * ```js\n * import keybindings from \"../src/keybindings\"\n *\n * keybindings(window, {\n * \t\"Shift+d\": () => {\n * \t\talert(\"The 'Shift' and 'd' keys were pressed at the same time\")\n * \t},\n * \t\"y e e t\": () => {\n * \t\talert(\"The keys 'y', 'e', 'e', and 't' were pressed in order\")\n * \t},\n * \t\"$mod+d\": () => {\n * \t\talert(\"Either 'Control+d' or 'Meta+d' were pressed\")\n * \t},\n * })\n * ```\n */\nfunction keybindings(target, keyBindingMap, options) {\n    var _a, _b;\n    if (options === void 0) { options = {}; }\n    var timeout = (_a = options.timeout) !== null && _a !== void 0 ? _a : DEFAULT_TIMEOUT;\n    var event = (_b = options.event) !== null && _b !== void 0 ? _b : DEFAULT_EVENT;\n    var keyBindings = Object.keys(keyBindingMap).map(function (key) {\n        return [parse(key), keyBindingMap[key]];\n    });\n    var possibleMatches = new Map();\n    var timer = null;\n    var onKeyEvent = function (event) {\n        // Ensure and stop any event that isn't a full keyboard event.\n        // Autocomplete option navigation and selection would fire a instanceof Event,\n        // instead of the expected KeyboardEvent\n        if (!(event instanceof KeyboardEvent)) {\n            return;\n        }\n        keyBindings.forEach(function (keyBinding) {\n            var sequence = keyBinding[0];\n            var callback = keyBinding[1];\n            var prev = possibleMatches.get(sequence);\n            var remainingExpectedPresses = prev ? prev : sequence;\n            var currentExpectedPress = remainingExpectedPresses[0];\n            var matches = match(event, currentExpectedPress);\n            if (!matches) {\n                // Modifier keydown events shouldn't break sequences\n                // Note: This works because:\n                // - non-modifiers will always return false\n                // - if the current keypress is a modifier then it will return true when we check its state\n                // MDN: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/getModifierState\n                if (!getModifierState(event, event.key)) {\n                    possibleMatches.delete(sequence);\n                }\n            }\n            else if (remainingExpectedPresses.length > 1) {\n                possibleMatches.set(sequence, remainingExpectedPresses.slice(1));\n            }\n            else {\n                possibleMatches.delete(sequence);\n                callback(event);\n            }\n        });\n        if (timer) {\n            clearTimeout(timer);\n        }\n        // @ts-ignore\n        timer = setTimeout(possibleMatches.clear.bind(possibleMatches), timeout);\n    };\n    target.addEventListener(event, onKeyEvent);\n    return function () {\n        target.removeEventListener(event, onKeyEvent);\n    };\n}\nexports[\"default\"] = keybindings;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/tinykeys.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/types.js":
/*!****************************************!*\
  !*** ./node_modules/kbar/lib/types.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.VisualState = void 0;\nvar VisualState;\n(function (VisualState) {\n    VisualState[\"animatingIn\"] = \"animating-in\";\n    VisualState[\"showing\"] = \"showing\";\n    VisualState[\"animatingOut\"] = \"animating-out\";\n    VisualState[\"hidden\"] = \"hidden\";\n})(VisualState = exports.VisualState || (exports.VisualState = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMva2Jhci9saWIvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsd0NBQXdDLG1CQUFtQixLQUFLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9rYmFyL2xpYi90eXBlcy5qcz8yM2RmIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5WaXN1YWxTdGF0ZSA9IHZvaWQgMDtcbnZhciBWaXN1YWxTdGF0ZTtcbihmdW5jdGlvbiAoVmlzdWFsU3RhdGUpIHtcbiAgICBWaXN1YWxTdGF0ZVtcImFuaW1hdGluZ0luXCJdID0gXCJhbmltYXRpbmctaW5cIjtcbiAgICBWaXN1YWxTdGF0ZVtcInNob3dpbmdcIl0gPSBcInNob3dpbmdcIjtcbiAgICBWaXN1YWxTdGF0ZVtcImFuaW1hdGluZ091dFwiXSA9IFwiYW5pbWF0aW5nLW91dFwiO1xuICAgIFZpc3VhbFN0YXRlW1wiaGlkZGVuXCJdID0gXCJoaWRkZW5cIjtcbn0pKFZpc3VhbFN0YXRlID0gZXhwb3J0cy5WaXN1YWxTdGF0ZSB8fCAoZXhwb3J0cy5WaXN1YWxTdGF0ZSA9IHt9KSk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/useKBar.js":
/*!******************************************!*\
  !*** ./node_modules/kbar/lib/useKBar.js ***!
  \******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useKBar = void 0;\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar KBarContextProvider_1 = __webpack_require__(/*! ./KBarContextProvider */ \"(ssr)/./node_modules/kbar/lib/KBarContextProvider.js\");\nfunction useKBar(collector) {\n    var _a = React.useContext(KBarContextProvider_1.KBarContext), query = _a.query, getState = _a.getState, subscribe = _a.subscribe, options = _a.options;\n    var collected = React.useRef(collector === null || collector === void 0 ? void 0 : collector(getState()));\n    var collectorRef = React.useRef(collector);\n    var onCollect = React.useCallback(function (collected) { return (__assign(__assign({}, collected), { query: query, options: options })); }, [query, options]);\n    var _b = React.useState(onCollect(collected.current)), render = _b[0], setRender = _b[1];\n    React.useEffect(function () {\n        var unsubscribe;\n        if (collectorRef.current) {\n            unsubscribe = subscribe(function (current) { return collectorRef.current(current); }, function (collected) { return setRender(onCollect(collected)); });\n        }\n        return function () {\n            if (unsubscribe) {\n                unsubscribe();\n            }\n        };\n    }, [onCollect, subscribe]);\n    return render;\n}\nexports.useKBar = useKBar;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/useKBar.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/useMatches.js":
/*!*********************************************!*\
  !*** ./node_modules/kbar/lib/useMatches.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useDeepMatches = exports.useMatches = exports.NO_GROUP = void 0;\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar useKBar_1 = __webpack_require__(/*! ./useKBar */ \"(ssr)/./node_modules/kbar/lib/useKBar.js\");\nvar utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/kbar/lib/utils.js\");\nvar fuse_js_1 = __importDefault(__webpack_require__(/*! fuse.js */ \"(ssr)/./node_modules/fuse.js/dist/fuse.esm.js\"));\nexports.NO_GROUP = {\n    name: \"none\",\n    priority: utils_1.Priority.NORMAL,\n};\nvar fuseOptions = {\n    keys: [\n        {\n            name: \"name\",\n            weight: 0.5,\n        },\n        {\n            name: \"keywords\",\n            getFn: function (item) { var _a; return ((_a = item.keywords) !== null && _a !== void 0 ? _a : \"\").split(\",\"); },\n            weight: 0.5,\n        },\n        \"subtitle\",\n    ],\n    ignoreLocation: true,\n    includeScore: true,\n    includeMatches: true,\n    threshold: 0.2,\n    minMatchCharLength: 1,\n};\nfunction order(a, b) {\n    /**\n     * Larger the priority = higher up the list\n     */\n    return b.priority - a.priority;\n}\n/**\n * returns deep matches only when a search query is present\n */\nfunction useMatches() {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        search: state.searchQuery,\n        actions: state.actions,\n        rootActionId: state.currentRootActionId,\n    }); }), search = _a.search, actions = _a.actions, rootActionId = _a.rootActionId;\n    var rootResults = React.useMemo(function () {\n        return Object.keys(actions)\n            .reduce(function (acc, actionId) {\n            var action = actions[actionId];\n            if (!action.parent && !rootActionId) {\n                acc.push(action);\n            }\n            if (action.id === rootActionId) {\n                for (var i = 0; i < action.children.length; i++) {\n                    acc.push(action.children[i]);\n                }\n            }\n            return acc;\n        }, [])\n            .sort(order);\n    }, [actions, rootActionId]);\n    var getDeepResults = React.useCallback(function (actions) {\n        var actionsClone = [];\n        for (var i = 0; i < actions.length; i++) {\n            actionsClone.push(actions[i]);\n        }\n        return (function collectChildren(actions, all) {\n            if (all === void 0) { all = actionsClone; }\n            for (var i = 0; i < actions.length; i++) {\n                if (actions[i].children.length > 0) {\n                    var childsChildren = actions[i].children;\n                    for (var i_1 = 0; i_1 < childsChildren.length; i_1++) {\n                        all.push(childsChildren[i_1]);\n                    }\n                    collectChildren(actions[i].children, all);\n                }\n            }\n            return all;\n        })(actions);\n    }, []);\n    var emptySearch = !search;\n    var filtered = React.useMemo(function () {\n        if (emptySearch)\n            return rootResults;\n        return getDeepResults(rootResults);\n    }, [getDeepResults, rootResults, emptySearch]);\n    var fuse = React.useMemo(function () { return new fuse_js_1.default(filtered, fuseOptions); }, [filtered]);\n    var matches = useInternalMatches(filtered, search, fuse);\n    var results = React.useMemo(function () {\n        var _a, _b;\n        /**\n         * Store a reference to a section and it's list of actions.\n         * Alongside these actions, we'll keep a temporary record of the\n         * final priority calculated by taking the commandScore + the\n         * explicitly set `action.priority` value.\n         */\n        var map = {};\n        /**\n         * Store another reference to a list of sections alongside\n         * the section's final priority, calculated the same as above.\n         */\n        var list = [];\n        /**\n         * We'll take the list above and sort by its priority. Then we'll\n         * collect all actions from the map above for this specific name and\n         * sort by its priority as well.\n         */\n        var ordered = [];\n        for (var i = 0; i < matches.length; i++) {\n            var match = matches[i];\n            var action = match.action;\n            var score = match.score || utils_1.Priority.NORMAL;\n            var section = {\n                name: typeof action.section === \"string\"\n                    ? action.section\n                    : ((_a = action.section) === null || _a === void 0 ? void 0 : _a.name) || exports.NO_GROUP.name,\n                priority: typeof action.section === \"string\"\n                    ? score\n                    : ((_b = action.section) === null || _b === void 0 ? void 0 : _b.priority) || 0 + score,\n            };\n            if (!map[section.name]) {\n                map[section.name] = [];\n                list.push(section);\n            }\n            map[section.name].push({\n                priority: action.priority + score,\n                action: action,\n            });\n        }\n        ordered = list.sort(order).map(function (group) { return ({\n            name: group.name,\n            actions: map[group.name].sort(order).map(function (item) { return item.action; }),\n        }); });\n        /**\n         * Our final result is simply flattening the ordered list into\n         * our familiar (ActionImpl | string)[] shape.\n         */\n        var results = [];\n        for (var i = 0; i < ordered.length; i++) {\n            var group = ordered[i];\n            if (group.name !== exports.NO_GROUP.name)\n                results.push(group.name);\n            for (var i_2 = 0; i_2 < group.actions.length; i_2++) {\n                results.push(group.actions[i_2]);\n            }\n        }\n        return results;\n    }, [matches]);\n    // ensure that users have an accurate `currentRootActionId`\n    // that syncs with the throttled return value.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    var memoRootActionId = React.useMemo(function () { return rootActionId; }, [results]);\n    return React.useMemo(function () { return ({\n        results: results,\n        rootActionId: memoRootActionId,\n    }); }, [memoRootActionId, results]);\n}\nexports.useMatches = useMatches;\nfunction useInternalMatches(filtered, search, fuse) {\n    var value = React.useMemo(function () { return ({\n        filtered: filtered,\n        search: search,\n    }); }, [filtered, search]);\n    var _a = (0, utils_1.useThrottledValue)(value), throttledFiltered = _a.filtered, throttledSearch = _a.search;\n    return React.useMemo(function () {\n        if (throttledSearch.trim() === \"\") {\n            return throttledFiltered.map(function (action) { return ({ score: 0, action: action }); });\n        }\n        var matches = [];\n        // Use Fuse's `search` method to perform the search efficiently\n        var searchResults = fuse.search(throttledSearch);\n        // Format the search results to match the existing structure\n        matches = searchResults.map(function (_a) {\n            var action = _a.item, score = _a.score;\n            return ({\n                score: 1 / ((score !== null && score !== void 0 ? score : 0) + 1),\n                action: action,\n            });\n        });\n        return matches;\n    }, [throttledFiltered, throttledSearch, fuse]);\n}\n/**\n * @deprecated use useMatches\n */\nexports.useDeepMatches = useMatches;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/useMatches.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/useRegisterActions.js":
/*!*****************************************************!*\
  !*** ./node_modules/kbar/lib/useRegisterActions.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useRegisterActions = void 0;\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar useKBar_1 = __webpack_require__(/*! ./useKBar */ \"(ssr)/./node_modules/kbar/lib/useKBar.js\");\nfunction useRegisterActions(actions, dependencies) {\n    if (dependencies === void 0) { dependencies = []; }\n    var query = (0, useKBar_1.useKBar)().query;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    var actionsCache = React.useMemo(function () { return actions; }, dependencies);\n    React.useEffect(function () {\n        if (!actionsCache.length) {\n            return;\n        }\n        var unregister = query.registerActions(actionsCache);\n        return function () {\n            unregister();\n        };\n    }, [query, actionsCache]);\n}\nexports.useRegisterActions = useRegisterActions;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMva2Jhci9saWIvdXNlUmVnaXN0ZXJBY3Rpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBLG1DQUFtQyxvQ0FBb0MsZ0JBQWdCO0FBQ3ZGLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0EsMENBQTBDLDRCQUE0QjtBQUN0RSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsMEJBQTBCO0FBQzFCLHlCQUF5QixtQkFBTyxDQUFDLHdHQUFPO0FBQ3hDLGdCQUFnQixtQkFBTyxDQUFDLDJEQUFXO0FBQ25DO0FBQ0EsbUNBQW1DO0FBQ25DO0FBQ0E7QUFDQSxtREFBbUQsaUJBQWlCO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSwwQkFBMEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL2tiYXIvbGliL3VzZVJlZ2lzdGVyQWN0aW9ucy5qcz9mNzc5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9fY3JlYXRlQmluZGluZyA9ICh0aGlzICYmIHRoaXMuX19jcmVhdGVCaW5kaW5nKSB8fCAoT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24oKSB7IHJldHVybiBtW2tdOyB9IH0pO1xufSkgOiAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIG9bazJdID0gbVtrXTtcbn0pKTtcbnZhciBfX3NldE1vZHVsZURlZmF1bHQgPSAodGhpcyAmJiB0aGlzLl9fc2V0TW9kdWxlRGVmYXVsdCkgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgdikge1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBcImRlZmF1bHRcIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCB2YWx1ZTogdiB9KTtcbn0pIDogZnVuY3Rpb24obywgdikge1xuICAgIG9bXCJkZWZhdWx0XCJdID0gdjtcbn0pO1xudmFyIF9faW1wb3J0U3RhciA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnRTdGFyKSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgaWYgKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgcmV0dXJuIG1vZDtcbiAgICB2YXIgcmVzdWx0ID0ge307XG4gICAgaWYgKG1vZCAhPSBudWxsKSBmb3IgKHZhciBrIGluIG1vZCkgaWYgKGsgIT09IFwiZGVmYXVsdFwiICYmIE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChtb2QsIGspKSBfX2NyZWF0ZUJpbmRpbmcocmVzdWx0LCBtb2QsIGspO1xuICAgIF9fc2V0TW9kdWxlRGVmYXVsdChyZXN1bHQsIG1vZCk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnVzZVJlZ2lzdGVyQWN0aW9ucyA9IHZvaWQgMDtcbnZhciBSZWFjdCA9IF9faW1wb3J0U3RhcihyZXF1aXJlKFwicmVhY3RcIikpO1xudmFyIHVzZUtCYXJfMSA9IHJlcXVpcmUoXCIuL3VzZUtCYXJcIik7XG5mdW5jdGlvbiB1c2VSZWdpc3RlckFjdGlvbnMoYWN0aW9ucywgZGVwZW5kZW5jaWVzKSB7XG4gICAgaWYgKGRlcGVuZGVuY2llcyA9PT0gdm9pZCAwKSB7IGRlcGVuZGVuY2llcyA9IFtdOyB9XG4gICAgdmFyIHF1ZXJ5ID0gKDAsIHVzZUtCYXJfMS51c2VLQmFyKSgpLnF1ZXJ5O1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHNcbiAgICB2YXIgYWN0aW9uc0NhY2hlID0gUmVhY3QudXNlTWVtbyhmdW5jdGlvbiAoKSB7IHJldHVybiBhY3Rpb25zOyB9LCBkZXBlbmRlbmNpZXMpO1xuICAgIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgICAgIGlmICghYWN0aW9uc0NhY2hlLmxlbmd0aCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHZhciB1bnJlZ2lzdGVyID0gcXVlcnkucmVnaXN0ZXJBY3Rpb25zKGFjdGlvbnNDYWNoZSk7XG4gICAgICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICB1bnJlZ2lzdGVyKCk7XG4gICAgICAgIH07XG4gICAgfSwgW3F1ZXJ5LCBhY3Rpb25zQ2FjaGVdKTtcbn1cbmV4cG9ydHMudXNlUmVnaXN0ZXJBY3Rpb25zID0gdXNlUmVnaXN0ZXJBY3Rpb25zO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/useRegisterActions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/useStore.js":
/*!*******************************************!*\
  !*** ./node_modules/kbar/lib/useStore.js ***!
  \*******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useStore = void 0;\nvar fast_equals_1 = __webpack_require__(/*! fast-equals */ \"(ssr)/./node_modules/fast-equals/dist/fast-equals.esm.js\");\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar tiny_invariant_1 = __importDefault(__webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/tiny-invariant.cjs.js\"));\nvar ActionInterface_1 = __webpack_require__(/*! ./action/ActionInterface */ \"(ssr)/./node_modules/kbar/lib/action/ActionInterface.js\");\nvar HistoryImpl_1 = __webpack_require__(/*! ./action/HistoryImpl */ \"(ssr)/./node_modules/kbar/lib/action/HistoryImpl.js\");\nvar types_1 = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/kbar/lib/types.js\");\nfunction useStore(props) {\n    var optionsRef = React.useRef(__assign({ animations: {\n            enterMs: 200,\n            exitMs: 100,\n        } }, props.options));\n    var actionsInterface = React.useMemo(function () {\n        return new ActionInterface_1.ActionInterface(props.actions || [], {\n            historyManager: optionsRef.current.enableHistory ? HistoryImpl_1.history : undefined,\n        });\n    }, \n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // TODO: at this point useReducer might be a better approach to managing state.\n    var _a = React.useState({\n        searchQuery: \"\",\n        currentRootActionId: null,\n        visualState: types_1.VisualState.hidden,\n        actions: __assign({}, actionsInterface.actions),\n        activeIndex: 0,\n        disabled: false,\n    }), state = _a[0], setState = _a[1];\n    var currState = React.useRef(state);\n    currState.current = state;\n    var getState = React.useCallback(function () { return currState.current; }, []);\n    var publisher = React.useMemo(function () { return new Publisher(getState); }, [getState]);\n    React.useEffect(function () {\n        currState.current = state;\n        publisher.notify();\n    }, [state, publisher]);\n    var registerActions = React.useCallback(function (actions) {\n        setState(function (state) {\n            return __assign(__assign({}, state), { actions: actionsInterface.add(actions) });\n        });\n        return function unregister() {\n            setState(function (state) {\n                return __assign(__assign({}, state), { actions: actionsInterface.remove(actions) });\n            });\n        };\n    }, [actionsInterface]);\n    var inputRef = React.useRef(null);\n    return React.useMemo(function () {\n        var query = {\n            setCurrentRootAction: function (actionId) {\n                setState(function (state) { return (__assign(__assign({}, state), { currentRootActionId: actionId })); });\n            },\n            setVisualState: function (cb) {\n                setState(function (state) { return (__assign(__assign({}, state), { visualState: typeof cb === \"function\" ? cb(state.visualState) : cb })); });\n            },\n            setSearch: function (searchQuery) {\n                return setState(function (state) { return (__assign(__assign({}, state), { searchQuery: searchQuery })); });\n            },\n            registerActions: registerActions,\n            toggle: function () {\n                return setState(function (state) { return (__assign(__assign({}, state), { visualState: [types_1.VisualState.animatingOut, types_1.VisualState.hidden].includes(state.visualState)\n                        ? types_1.VisualState.animatingIn\n                        : types_1.VisualState.animatingOut })); });\n            },\n            setActiveIndex: function (cb) {\n                return setState(function (state) { return (__assign(__assign({}, state), { activeIndex: typeof cb === \"number\" ? cb : cb(state.activeIndex) })); });\n            },\n            inputRefSetter: function (el) {\n                inputRef.current = el;\n            },\n            getInput: function () {\n                (0, tiny_invariant_1.default)(inputRef.current, \"Input ref is undefined, make sure you attach `query.inputRefSetter` to your search input.\");\n                return inputRef.current;\n            },\n            disable: function (disable) {\n                setState(function (state) { return (__assign(__assign({}, state), { disabled: disable })); });\n            },\n        };\n        return {\n            getState: getState,\n            query: query,\n            options: optionsRef.current,\n            subscribe: function (collector, cb) { return publisher.subscribe(collector, cb); },\n        };\n    }, [getState, publisher, registerActions]);\n}\nexports.useStore = useStore;\nvar Publisher = /** @class */ (function () {\n    function Publisher(getState) {\n        this.subscribers = [];\n        this.getState = getState;\n    }\n    Publisher.prototype.subscribe = function (collector, onChange) {\n        var _this = this;\n        var subscriber = new Subscriber(function () { return collector(_this.getState()); }, onChange);\n        this.subscribers.push(subscriber);\n        return this.unsubscribe.bind(this, subscriber);\n    };\n    Publisher.prototype.unsubscribe = function (subscriber) {\n        if (this.subscribers.length) {\n            var index = this.subscribers.indexOf(subscriber);\n            if (index > -1) {\n                return this.subscribers.splice(index, 1);\n            }\n        }\n    };\n    Publisher.prototype.notify = function () {\n        this.subscribers.forEach(function (subscriber) { return subscriber.collect(); });\n    };\n    return Publisher;\n}());\nvar Subscriber = /** @class */ (function () {\n    function Subscriber(collector, onChange) {\n        this.collector = collector;\n        this.onChange = onChange;\n    }\n    Subscriber.prototype.collect = function () {\n        try {\n            // grab latest state\n            var recollect = this.collector();\n            if (!(0, fast_equals_1.deepEqual)(recollect, this.collected)) {\n                this.collected = recollect;\n                if (this.onChange) {\n                    this.onChange(this.collected);\n                }\n            }\n        }\n        catch (error) {\n            console.warn(error);\n        }\n    };\n    return Subscriber;\n}());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/useStore.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/kbar/lib/utils.js":
/*!****************************************!*\
  !*** ./node_modules/kbar/lib/utils.js ***!
  \****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Priority = exports.isModKey = exports.shouldRejectKeystrokes = exports.useThrottledValue = exports.getScrollbarWidth = exports.useIsomorphicLayout = exports.noop = exports.createAction = exports.randomId = exports.usePointerMovedSinceMount = exports.useOuterClick = exports.swallowEvent = void 0;\nvar React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nfunction swallowEvent(event) {\n    event.stopPropagation();\n    event.preventDefault();\n}\nexports.swallowEvent = swallowEvent;\nfunction useOuterClick(dom, cb) {\n    var cbRef = React.useRef(cb);\n    cbRef.current = cb;\n    React.useEffect(function () {\n        function handler(event) {\n            var _a, _b;\n            if (((_a = dom.current) === null || _a === void 0 ? void 0 : _a.contains(event.target)) ||\n                // Add support for ReactShadowRoot\n                // @ts-expect-error wrong types, the `host` property exists https://stackoverflow.com/a/25340456\n                event.target === ((_b = dom.current) === null || _b === void 0 ? void 0 : _b.getRootNode().host)) {\n                return;\n            }\n            event.preventDefault();\n            event.stopPropagation();\n            cbRef.current();\n        }\n        window.addEventListener(\"pointerdown\", handler, true);\n        return function () { return window.removeEventListener(\"pointerdown\", handler, true); };\n    }, [dom]);\n}\nexports.useOuterClick = useOuterClick;\nfunction usePointerMovedSinceMount() {\n    var _a = React.useState(false), moved = _a[0], setMoved = _a[1];\n    React.useEffect(function () {\n        function handler() {\n            setMoved(true);\n        }\n        if (!moved) {\n            window.addEventListener(\"pointermove\", handler);\n            return function () { return window.removeEventListener(\"pointermove\", handler); };\n        }\n    }, [moved]);\n    return moved;\n}\nexports.usePointerMovedSinceMount = usePointerMovedSinceMount;\nfunction randomId() {\n    return Math.random().toString(36).substring(2, 9);\n}\nexports.randomId = randomId;\nfunction createAction(params) {\n    return __assign({ id: randomId() }, params);\n}\nexports.createAction = createAction;\nfunction noop() { }\nexports.noop = noop;\nexports.useIsomorphicLayout = typeof window === \"undefined\" ? noop : React.useLayoutEffect;\n// https://stackoverflow.com/questions/13382516/getting-scroll-bar-width-using-javascript\nfunction getScrollbarWidth() {\n    var outer = document.createElement(\"div\");\n    outer.style.visibility = \"hidden\";\n    outer.style.overflow = \"scroll\";\n    document.body.appendChild(outer);\n    var inner = document.createElement(\"div\");\n    outer.appendChild(inner);\n    var scrollbarWidth = outer.offsetWidth - inner.offsetWidth;\n    outer.parentNode.removeChild(outer);\n    return scrollbarWidth;\n}\nexports.getScrollbarWidth = getScrollbarWidth;\nfunction useThrottledValue(value, ms) {\n    if (ms === void 0) { ms = 100; }\n    var _a = React.useState(value), throttledValue = _a[0], setThrottledValue = _a[1];\n    var lastRan = React.useRef(Date.now());\n    React.useEffect(function () {\n        if (ms === 0)\n            return;\n        var timeout = setTimeout(function () {\n            setThrottledValue(value);\n            lastRan.current = Date.now();\n        }, lastRan.current - (Date.now() - ms));\n        return function () {\n            clearTimeout(timeout);\n        };\n    }, [ms, value]);\n    return ms === 0 ? value : throttledValue;\n}\nexports.useThrottledValue = useThrottledValue;\nfunction shouldRejectKeystrokes(_a) {\n    var _b, _c, _d;\n    var _e = _a === void 0 ? { ignoreWhenFocused: [] } : _a, ignoreWhenFocused = _e.ignoreWhenFocused;\n    var inputs = __spreadArray([\"input\", \"textarea\"], ignoreWhenFocused, true).map(function (el) {\n        return el.toLowerCase();\n    });\n    var activeElement = document.activeElement;\n    var ignoreStrokes = activeElement &&\n        (inputs.indexOf(activeElement.tagName.toLowerCase()) !== -1 ||\n            ((_b = activeElement.attributes.getNamedItem(\"role\")) === null || _b === void 0 ? void 0 : _b.value) === \"textbox\" ||\n            ((_c = activeElement.attributes.getNamedItem(\"contenteditable\")) === null || _c === void 0 ? void 0 : _c.value) ===\n                \"true\" ||\n            ((_d = activeElement.attributes.getNamedItem(\"contenteditable\")) === null || _d === void 0 ? void 0 : _d.value) ===\n                \"plaintext-only\");\n    return ignoreStrokes;\n}\nexports.shouldRejectKeystrokes = shouldRejectKeystrokes;\nvar SSR = typeof window === \"undefined\";\nvar isMac = !SSR && window.navigator.platform === \"MacIntel\";\nfunction isModKey(event) {\n    return isMac ? event.metaKey : event.ctrlKey;\n}\nexports.isModKey = isModKey;\nexports.Priority = {\n    HIGH: 1,\n    NORMAL: 0,\n    LOW: -1,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMva2Jhci9saWIvdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0EsaURBQWlELE9BQU87QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsb0NBQW9DLGdCQUFnQjtBQUN2RixDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLDBDQUEwQyw0QkFBNEI7QUFDdEUsQ0FBQztBQUNEO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2RUFBNkUsT0FBTztBQUNwRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQkFBZ0IsR0FBRyxnQkFBZ0IsR0FBRyw4QkFBOEIsR0FBRyx5QkFBeUIsR0FBRyx5QkFBeUIsR0FBRywyQkFBMkIsR0FBRyxZQUFZLEdBQUcsb0JBQW9CLEdBQUcsZ0JBQWdCLEdBQUcsaUNBQWlDLEdBQUcscUJBQXFCLEdBQUcsb0JBQW9CO0FBQ3RTLHlCQUF5QixtQkFBTyxDQUFDLHdHQUFPO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0IsS0FBSztBQUNMO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQSxzQkFBc0IsZ0JBQWdCO0FBQ3RDO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0EsWUFBWTtBQUNaLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0EsK0JBQStCLHdCQUF3QjtBQUN2RDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9rYmFyL2xpYi91dGlscy5qcz84OGI3Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9fYXNzaWduID0gKHRoaXMgJiYgdGhpcy5fX2Fzc2lnbikgfHwgZnVuY3Rpb24gKCkge1xuICAgIF9fYXNzaWduID0gT2JqZWN0LmFzc2lnbiB8fCBmdW5jdGlvbih0KSB7XG4gICAgICAgIGZvciAodmFyIHMsIGkgPSAxLCBuID0gYXJndW1lbnRzLmxlbmd0aDsgaSA8IG47IGkrKykge1xuICAgICAgICAgICAgcyA9IGFyZ3VtZW50c1tpXTtcbiAgICAgICAgICAgIGZvciAodmFyIHAgaW4gcykgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChzLCBwKSlcbiAgICAgICAgICAgICAgICB0W3BdID0gc1twXTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdDtcbiAgICB9O1xuICAgIHJldHVybiBfX2Fzc2lnbi5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xufTtcbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9KTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19zZXRNb2R1bGVEZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX3NldE1vZHVsZURlZmF1bHQpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIHYpIHtcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgXCJkZWZhdWx0XCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgdmFsdWU6IHYgfSk7XG59KSA6IGZ1bmN0aW9uKG8sIHYpIHtcbiAgICBvW1wiZGVmYXVsdFwiXSA9IHY7XG59KTtcbnZhciBfX2ltcG9ydFN0YXIgPSAodGhpcyAmJiB0aGlzLl9faW1wb3J0U3RhcikgfHwgZnVuY3Rpb24gKG1vZCkge1xuICAgIGlmIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpIHJldHVybiBtb2Q7XG4gICAgdmFyIHJlc3VsdCA9IHt9O1xuICAgIGlmIChtb2QgIT0gbnVsbCkgZm9yICh2YXIgayBpbiBtb2QpIGlmIChrICE9PSBcImRlZmF1bHRcIiAmJiBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwobW9kLCBrKSkgX19jcmVhdGVCaW5kaW5nKHJlc3VsdCwgbW9kLCBrKTtcbiAgICBfX3NldE1vZHVsZURlZmF1bHQocmVzdWx0LCBtb2QpO1xuICAgIHJldHVybiByZXN1bHQ7XG59O1xudmFyIF9fc3ByZWFkQXJyYXkgPSAodGhpcyAmJiB0aGlzLl9fc3ByZWFkQXJyYXkpIHx8IGZ1bmN0aW9uICh0bywgZnJvbSwgcGFjaykge1xuICAgIGlmIChwYWNrIHx8IGFyZ3VtZW50cy5sZW5ndGggPT09IDIpIGZvciAodmFyIGkgPSAwLCBsID0gZnJvbS5sZW5ndGgsIGFyOyBpIDwgbDsgaSsrKSB7XG4gICAgICAgIGlmIChhciB8fCAhKGkgaW4gZnJvbSkpIHtcbiAgICAgICAgICAgIGlmICghYXIpIGFyID0gQXJyYXkucHJvdG90eXBlLnNsaWNlLmNhbGwoZnJvbSwgMCwgaSk7XG4gICAgICAgICAgICBhcltpXSA9IGZyb21baV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRvLmNvbmNhdChhciB8fCBBcnJheS5wcm90b3R5cGUuc2xpY2UuY2FsbChmcm9tKSk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Qcmlvcml0eSA9IGV4cG9ydHMuaXNNb2RLZXkgPSBleHBvcnRzLnNob3VsZFJlamVjdEtleXN0cm9rZXMgPSBleHBvcnRzLnVzZVRocm90dGxlZFZhbHVlID0gZXhwb3J0cy5nZXRTY3JvbGxiYXJXaWR0aCA9IGV4cG9ydHMudXNlSXNvbW9ycGhpY0xheW91dCA9IGV4cG9ydHMubm9vcCA9IGV4cG9ydHMuY3JlYXRlQWN0aW9uID0gZXhwb3J0cy5yYW5kb21JZCA9IGV4cG9ydHMudXNlUG9pbnRlck1vdmVkU2luY2VNb3VudCA9IGV4cG9ydHMudXNlT3V0ZXJDbGljayA9IGV4cG9ydHMuc3dhbGxvd0V2ZW50ID0gdm9pZCAwO1xudmFyIFJlYWN0ID0gX19pbXBvcnRTdGFyKHJlcXVpcmUoXCJyZWFjdFwiKSk7XG5mdW5jdGlvbiBzd2FsbG93RXZlbnQoZXZlbnQpIHtcbiAgICBldmVudC5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xufVxuZXhwb3J0cy5zd2FsbG93RXZlbnQgPSBzd2FsbG93RXZlbnQ7XG5mdW5jdGlvbiB1c2VPdXRlckNsaWNrKGRvbSwgY2IpIHtcbiAgICB2YXIgY2JSZWYgPSBSZWFjdC51c2VSZWYoY2IpO1xuICAgIGNiUmVmLmN1cnJlbnQgPSBjYjtcbiAgICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgICAgICBmdW5jdGlvbiBoYW5kbGVyKGV2ZW50KSB7XG4gICAgICAgICAgICB2YXIgX2EsIF9iO1xuICAgICAgICAgICAgaWYgKCgoX2EgPSBkb20uY3VycmVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNvbnRhaW5zKGV2ZW50LnRhcmdldCkpIHx8XG4gICAgICAgICAgICAgICAgLy8gQWRkIHN1cHBvcnQgZm9yIFJlYWN0U2hhZG93Um9vdFxuICAgICAgICAgICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3Igd3JvbmcgdHlwZXMsIHRoZSBgaG9zdGAgcHJvcGVydHkgZXhpc3RzIGh0dHBzOi8vc3RhY2tvdmVyZmxvdy5jb20vYS8yNTM0MDQ1NlxuICAgICAgICAgICAgICAgIGV2ZW50LnRhcmdldCA9PT0gKChfYiA9IGRvbS5jdXJyZW50KSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IuZ2V0Um9vdE5vZGUoKS5ob3N0KSkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICBldmVudC5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgIGNiUmVmLmN1cnJlbnQoKTtcbiAgICAgICAgfVxuICAgICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInBvaW50ZXJkb3duXCIsIGhhbmRsZXIsIHRydWUpO1xuICAgICAgICByZXR1cm4gZnVuY3Rpb24gKCkgeyByZXR1cm4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJwb2ludGVyZG93blwiLCBoYW5kbGVyLCB0cnVlKTsgfTtcbiAgICB9LCBbZG9tXSk7XG59XG5leHBvcnRzLnVzZU91dGVyQ2xpY2sgPSB1c2VPdXRlckNsaWNrO1xuZnVuY3Rpb24gdXNlUG9pbnRlck1vdmVkU2luY2VNb3VudCgpIHtcbiAgICB2YXIgX2EgPSBSZWFjdC51c2VTdGF0ZShmYWxzZSksIG1vdmVkID0gX2FbMF0sIHNldE1vdmVkID0gX2FbMV07XG4gICAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgZnVuY3Rpb24gaGFuZGxlcigpIHtcbiAgICAgICAgICAgIHNldE1vdmVkKHRydWUpO1xuICAgICAgICB9XG4gICAgICAgIGlmICghbW92ZWQpIHtcbiAgICAgICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwicG9pbnRlcm1vdmVcIiwgaGFuZGxlcik7XG4gICAgICAgICAgICByZXR1cm4gZnVuY3Rpb24gKCkgeyByZXR1cm4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJwb2ludGVybW92ZVwiLCBoYW5kbGVyKTsgfTtcbiAgICAgICAgfVxuICAgIH0sIFttb3ZlZF0pO1xuICAgIHJldHVybiBtb3ZlZDtcbn1cbmV4cG9ydHMudXNlUG9pbnRlck1vdmVkU2luY2VNb3VudCA9IHVzZVBvaW50ZXJNb3ZlZFNpbmNlTW91bnQ7XG5mdW5jdGlvbiByYW5kb21JZCgpIHtcbiAgICByZXR1cm4gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDIsIDkpO1xufVxuZXhwb3J0cy5yYW5kb21JZCA9IHJhbmRvbUlkO1xuZnVuY3Rpb24gY3JlYXRlQWN0aW9uKHBhcmFtcykge1xuICAgIHJldHVybiBfX2Fzc2lnbih7IGlkOiByYW5kb21JZCgpIH0sIHBhcmFtcyk7XG59XG5leHBvcnRzLmNyZWF0ZUFjdGlvbiA9IGNyZWF0ZUFjdGlvbjtcbmZ1bmN0aW9uIG5vb3AoKSB7IH1cbmV4cG9ydHMubm9vcCA9IG5vb3A7XG5leHBvcnRzLnVzZUlzb21vcnBoaWNMYXlvdXQgPSB0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiID8gbm9vcCA6IFJlYWN0LnVzZUxheW91dEVmZmVjdDtcbi8vIGh0dHBzOi8vc3RhY2tvdmVyZmxvdy5jb20vcXVlc3Rpb25zLzEzMzgyNTE2L2dldHRpbmctc2Nyb2xsLWJhci13aWR0aC11c2luZy1qYXZhc2NyaXB0XG5mdW5jdGlvbiBnZXRTY3JvbGxiYXJXaWR0aCgpIHtcbiAgICB2YXIgb3V0ZXIgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KFwiZGl2XCIpO1xuICAgIG91dGVyLnN0eWxlLnZpc2liaWxpdHkgPSBcImhpZGRlblwiO1xuICAgIG91dGVyLnN0eWxlLm92ZXJmbG93ID0gXCJzY3JvbGxcIjtcbiAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKG91dGVyKTtcbiAgICB2YXIgaW5uZXIgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KFwiZGl2XCIpO1xuICAgIG91dGVyLmFwcGVuZENoaWxkKGlubmVyKTtcbiAgICB2YXIgc2Nyb2xsYmFyV2lkdGggPSBvdXRlci5vZmZzZXRXaWR0aCAtIGlubmVyLm9mZnNldFdpZHRoO1xuICAgIG91dGVyLnBhcmVudE5vZGUucmVtb3ZlQ2hpbGQob3V0ZXIpO1xuICAgIHJldHVybiBzY3JvbGxiYXJXaWR0aDtcbn1cbmV4cG9ydHMuZ2V0U2Nyb2xsYmFyV2lkdGggPSBnZXRTY3JvbGxiYXJXaWR0aDtcbmZ1bmN0aW9uIHVzZVRocm90dGxlZFZhbHVlKHZhbHVlLCBtcykge1xuICAgIGlmIChtcyA9PT0gdm9pZCAwKSB7IG1zID0gMTAwOyB9XG4gICAgdmFyIF9hID0gUmVhY3QudXNlU3RhdGUodmFsdWUpLCB0aHJvdHRsZWRWYWx1ZSA9IF9hWzBdLCBzZXRUaHJvdHRsZWRWYWx1ZSA9IF9hWzFdO1xuICAgIHZhciBsYXN0UmFuID0gUmVhY3QudXNlUmVmKERhdGUubm93KCkpO1xuICAgIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgICAgIGlmIChtcyA9PT0gMClcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgdmFyIHRpbWVvdXQgPSBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHNldFRocm90dGxlZFZhbHVlKHZhbHVlKTtcbiAgICAgICAgICAgIGxhc3RSYW4uY3VycmVudCA9IERhdGUubm93KCk7XG4gICAgICAgIH0sIGxhc3RSYW4uY3VycmVudCAtIChEYXRlLm5vdygpIC0gbXMpKTtcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0KTtcbiAgICAgICAgfTtcbiAgICB9LCBbbXMsIHZhbHVlXSk7XG4gICAgcmV0dXJuIG1zID09PSAwID8gdmFsdWUgOiB0aHJvdHRsZWRWYWx1ZTtcbn1cbmV4cG9ydHMudXNlVGhyb3R0bGVkVmFsdWUgPSB1c2VUaHJvdHRsZWRWYWx1ZTtcbmZ1bmN0aW9uIHNob3VsZFJlamVjdEtleXN0cm9rZXMoX2EpIHtcbiAgICB2YXIgX2IsIF9jLCBfZDtcbiAgICB2YXIgX2UgPSBfYSA9PT0gdm9pZCAwID8geyBpZ25vcmVXaGVuRm9jdXNlZDogW10gfSA6IF9hLCBpZ25vcmVXaGVuRm9jdXNlZCA9IF9lLmlnbm9yZVdoZW5Gb2N1c2VkO1xuICAgIHZhciBpbnB1dHMgPSBfX3NwcmVhZEFycmF5KFtcImlucHV0XCIsIFwidGV4dGFyZWFcIl0sIGlnbm9yZVdoZW5Gb2N1c2VkLCB0cnVlKS5tYXAoZnVuY3Rpb24gKGVsKSB7XG4gICAgICAgIHJldHVybiBlbC50b0xvd2VyQ2FzZSgpO1xuICAgIH0pO1xuICAgIHZhciBhY3RpdmVFbGVtZW50ID0gZG9jdW1lbnQuYWN0aXZlRWxlbWVudDtcbiAgICB2YXIgaWdub3JlU3Ryb2tlcyA9IGFjdGl2ZUVsZW1lbnQgJiZcbiAgICAgICAgKGlucHV0cy5pbmRleE9mKGFjdGl2ZUVsZW1lbnQudGFnTmFtZS50b0xvd2VyQ2FzZSgpKSAhPT0gLTEgfHxcbiAgICAgICAgICAgICgoX2IgPSBhY3RpdmVFbGVtZW50LmF0dHJpYnV0ZXMuZ2V0TmFtZWRJdGVtKFwicm9sZVwiKSkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLnZhbHVlKSA9PT0gXCJ0ZXh0Ym94XCIgfHxcbiAgICAgICAgICAgICgoX2MgPSBhY3RpdmVFbGVtZW50LmF0dHJpYnV0ZXMuZ2V0TmFtZWRJdGVtKFwiY29udGVudGVkaXRhYmxlXCIpKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MudmFsdWUpID09PVxuICAgICAgICAgICAgICAgIFwidHJ1ZVwiIHx8XG4gICAgICAgICAgICAoKF9kID0gYWN0aXZlRWxlbWVudC5hdHRyaWJ1dGVzLmdldE5hbWVkSXRlbShcImNvbnRlbnRlZGl0YWJsZVwiKSkgPT09IG51bGwgfHwgX2QgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9kLnZhbHVlKSA9PT1cbiAgICAgICAgICAgICAgICBcInBsYWludGV4dC1vbmx5XCIpO1xuICAgIHJldHVybiBpZ25vcmVTdHJva2VzO1xufVxuZXhwb3J0cy5zaG91bGRSZWplY3RLZXlzdHJva2VzID0gc2hvdWxkUmVqZWN0S2V5c3Ryb2tlcztcbnZhciBTU1IgPSB0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiO1xudmFyIGlzTWFjID0gIVNTUiAmJiB3aW5kb3cubmF2aWdhdG9yLnBsYXRmb3JtID09PSBcIk1hY0ludGVsXCI7XG5mdW5jdGlvbiBpc01vZEtleShldmVudCkge1xuICAgIHJldHVybiBpc01hYyA/IGV2ZW50Lm1ldGFLZXkgOiBldmVudC5jdHJsS2V5O1xufVxuZXhwb3J0cy5pc01vZEtleSA9IGlzTW9kS2V5O1xuZXhwb3J0cy5Qcmlvcml0eSA9IHtcbiAgICBISUdIOiAxLFxuICAgIE5PUk1BTDogMCxcbiAgICBMT1c6IC0xLFxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kbar/lib/utils.js\n");

/***/ })

};
;