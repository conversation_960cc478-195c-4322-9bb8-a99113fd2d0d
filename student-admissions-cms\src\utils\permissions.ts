// Role-based access control utilities
export type UserRole = 'admin' | 'admission_officer' | 'staff' | 'applicant';

export interface Permission {
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'list';
}

// Define permissions for each role
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  admin: [
    // Full access to all resources
    { resource: '*', action: 'create' },
    { resource: '*', action: 'read' },
    { resource: '*', action: 'update' },
    { resource: '*', action: 'delete' },
    { resource: '*', action: 'list' },
  ],
  admission_officer: [
    // Applications management
    { resource: 'applications', action: 'create' },
    { resource: 'applications', action: 'read' },
    { resource: 'applications', action: 'update' },
    { resource: 'applications', action: 'list' },
    
    // Inquiries management
    { resource: 'inquiries', action: 'create' },
    { resource: 'inquiries', action: 'read' },
    { resource: 'inquiries', action: 'update' },
    { resource: 'inquiries', action: 'list' },
    
    // Interviews management
    { resource: 'interviews', action: 'create' },
    { resource: 'interviews', action: 'read' },
    { resource: 'interviews', action: 'update' },
    { resource: 'interviews', action: 'list' },
    
    // Documents verification
    { resource: 'documents', action: 'read' },
    { resource: 'documents', action: 'update' },
    { resource: 'documents', action: 'list' },
    
    // Enrollments
    { resource: 'enrollments', action: 'create' },
    { resource: 'enrollments', action: 'read' },
    { resource: 'enrollments', action: 'update' },
    { resource: 'enrollments', action: 'list' },
    
    // Reports (read-only)
    { resource: 'reports', action: 'read' },
    { resource: 'reports', action: 'list' },
  ],
  staff: [
    // Limited access to applications
    { resource: 'applications', action: 'read' },
    { resource: 'applications', action: 'list' },
    
    // Inquiries management
    { resource: 'inquiries', action: 'create' },
    { resource: 'inquiries', action: 'read' },
    { resource: 'inquiries', action: 'update' },
    { resource: 'inquiries', action: 'list' },
    
    // Documents (read-only)
    { resource: 'documents', action: 'read' },
    { resource: 'documents', action: 'list' },
    
    // Basic reports
    { resource: 'reports', action: 'read' },
  ],
  applicant: [
    // Own applications only
    { resource: 'applications', action: 'create' },
    { resource: 'applications', action: 'read' },
    { resource: 'applications', action: 'update' },
    
    // Own documents
    { resource: 'documents', action: 'create' },
    { resource: 'documents', action: 'read' },
    { resource: 'documents', action: 'update' },
    
    // Own profile
    { resource: 'profile', action: 'read' },
    { resource: 'profile', action: 'update' },
  ],
};

// Check if user has permission for a specific action on a resource
export const hasPermission = (
  userRole: UserRole,
  resource: string,
  action: Permission['action']
): boolean => {
  const permissions = ROLE_PERMISSIONS[userRole];
  
  return permissions.some(permission => 
    (permission.resource === '*' || permission.resource === resource) &&
    permission.action === action
  );
};

// Check if user can access a specific route
export const canAccessRoute = (userRole: UserRole, route: string): boolean => {
  const routePermissions: Record<string, { resource: string; action: Permission['action'] }> = {
    '/dashboard': { resource: 'dashboard', action: 'read' },
    '/applications': { resource: 'applications', action: 'list' },
    '/applications/create': { resource: 'applications', action: 'create' },
    '/inquiries': { resource: 'inquiries', action: 'list' },
    '/inquiries/create': { resource: 'inquiries', action: 'create' },
    '/interviews': { resource: 'interviews', action: 'list' },
    '/enrollments': { resource: 'enrollments', action: 'list' },
    '/reports': { resource: 'reports', action: 'read' },
    '/settings': { resource: 'settings', action: 'read' },
    '/users': { resource: 'users', action: 'list' },
  };

  const routePermission = routePermissions[route];
  if (!routePermission) {
    return false;
  }

  return hasPermission(userRole, routePermission.resource, routePermission.action);
};

// Get allowed resources for a user role
export const getAllowedResources = (userRole: UserRole): string[] => {
  const permissions = ROLE_PERMISSIONS[userRole];
  const resources = new Set<string>();
  
  permissions.forEach(permission => {
    if (permission.resource !== '*') {
      resources.add(permission.resource);
    }
  });
  
  return Array.from(resources);
};

// Role hierarchy for access control
export const ROLE_HIERARCHY: Record<UserRole, number> = {
  applicant: 1,
  staff: 2,
  admission_officer: 3,
  admin: 4,
};

// Check if user role has higher or equal access level
export const hasRoleAccess = (userRole: UserRole, requiredRole: UserRole): boolean => {
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole];
};

// Navigation items based on user role
export const getNavigationItems = (userRole: UserRole) => {
  const baseItems = [
    { name: 'Dashboard', path: '/dashboard', icon: 'dashboard' },
  ];

  const roleSpecificItems: Record<UserRole, any[]> = {
    admin: [
      { name: 'Applications', path: '/applications', icon: 'assignment' },
      { name: 'Inquiries', path: '/inquiries', icon: 'contact_support' },
      { name: 'Interviews', path: '/interviews', icon: 'event' },
      { name: 'Enrollments', path: '/enrollments', icon: 'school' },
      { name: 'Programs', path: '/programs', icon: 'book' },
      { name: 'Users', path: '/users', icon: 'people' },
      { name: 'Reports', path: '/reports', icon: 'analytics' },
      { name: 'Settings', path: '/settings', icon: 'settings' },
    ],
    admission_officer: [
      { name: 'Applications', path: '/applications', icon: 'assignment' },
      { name: 'Inquiries', path: '/inquiries', icon: 'contact_support' },
      { name: 'Interviews', path: '/interviews', icon: 'event' },
      { name: 'Enrollments', path: '/enrollments', icon: 'school' },
      { name: 'Reports', path: '/reports', icon: 'analytics' },
    ],
    staff: [
      { name: 'Applications', path: '/applications', icon: 'assignment' },
      { name: 'Inquiries', path: '/inquiries', icon: 'contact_support' },
      { name: 'Reports', path: '/reports', icon: 'analytics' },
    ],
    applicant: [
      { name: 'My Applications', path: '/my-applications', icon: 'assignment' },
      { name: 'Apply Now', path: '/apply', icon: 'add' },
      { name: 'Profile', path: '/profile', icon: 'person' },
    ],
  };

  return [...baseItems, ...roleSpecificItems[userRole]];
};
