language: node_js
node_js:
  - "14"
install:
  <%_ if (pm === 'npm') { _%>
  - npm ci
  <%_ } _%>
  <%_ if (pm === 'yarn') { _%>
  - yarn
  <%_ } _%>
  <%_  if (linter.includes("eslint")) { _%>
  - <%= pmRun === 'yarn' ? 'yarn lint' : 'npm run lint' %>
  <%_ } _%>
  <%_  if (testing) { _%>
  - <%= pmRun === 'yarn' ? 'yarn test' : 'npm run test' %>
  <%_ } _%>
  <%_  if (e2etest.includes("cypress")) { _%>
  - <%= pmRun === 'yarn' ? 'yarn cypress:test' : 'npm run cypress:test' %>
  <%_ } _%>
  <%_  if (e2etest.includes("webdriverio")) { _%>
  - <%= pmRun === 'yarn' ? 'yarn webdriver:run' : 'npm run webdriver:run' %>
  <%_ } _%>