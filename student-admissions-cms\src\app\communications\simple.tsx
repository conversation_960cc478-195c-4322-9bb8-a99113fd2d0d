'use client';

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Email as EmailIcon,
  Sms as SmsIcon,
  WhatsApp as WhatsAppIcon,
  Add as AddIcon,
  Send as SendIcon,
  Template as TemplateIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';
import { mockData } from '@/components/DummyDataProvider';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const SimpleCommunicationsPage: React.FC = () => {
  const [tabValue, setTabValue] = React.useState(0);
  const templates = mockData.templates;

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <EmailIcon />;
      case 'sms':
        return <SmsIcon />;
      case 'whatsapp':
        return <WhatsAppIcon />;
      default:
        return <EmailIcon />;
    }
  };

  const getTypeColor = (type: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      email: 'primary',
      sms: 'success',
      whatsapp: 'info',
      in_app: 'secondary',
    };
    return colors[type] || 'default';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Communication Management
      </Typography>
      <Typography variant="body1" color="text.secondary" gutterBottom>
        Manage communication templates and send notifications to applicants and students.
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TemplateIcon color="primary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="primary">
                    {templates.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Templates
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <EmailIcon color="info" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="info.main">
                    {templates.filter(t => t.type === 'email').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Email Templates
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <NotificationsIcon color="success" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="success.main">
                    156
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Sent Today
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SendIcon color="warning" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="warning.main">
                    98.5%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Delivery Rate
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="Templates" />
          <Tab label="Send Notifications" />
          <Tab label="Notification History" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">Communication Templates</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => alert('Create new template functionality would be implemented here')}
          >
            New Template
          </Button>
        </Box>

        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Template Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Subject</TableCell>
                <TableCell>Variables</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {templates.map((template) => (
                <TableRow key={template.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {template.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      icon={getTypeIcon(template.type)}
                      label={template.type.toUpperCase()}
                      color={getTypeColor(template.type)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" noWrap>
                      {template.type === 'email' ? template.subject : 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {template.variables?.length || 0} variables
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={template.is_active ? 'Active' : 'Inactive'}
                      color={template.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {new Date(template.created_at).toLocaleDateString()}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={() => alert(`Preview template: ${template.name}`)}
                      >
                        Preview
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={() => alert(`Edit template: ${template.name}`)}
                      >
                        Edit
                      </Button>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Send Bulk Notifications
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Send notifications to multiple recipients at once
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<SendIcon />}
                  fullWidth
                  onClick={() => alert('Bulk notification functionality would be implemented here')}
                >
                  Send Bulk Notifications
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Send
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Send individual notifications using templates
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<EmailIcon />}
                  fullWidth
                  onClick={() => alert('Quick send functionality would be implemented here')}
                >
                  Quick Send
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Bulk Notification Features
          </Typography>
          <Typography variant="body2">
            The bulk notification system would include:
          </Typography>
          <ul>
            <li>Recipient group selection (all applicants, specific status, program, etc.)</li>
            <li>Template selection with variable substitution</li>
            <li>Preview before sending</li>
            <li>Scheduled sending</li>
            <li>Delivery tracking and analytics</li>
          </ul>
        </Alert>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6" gutterBottom>
          Notification History
        </Typography>
        
        <Alert severity="info">
          <Typography variant="h6" gutterBottom>
            📊 Notification Analytics
          </Typography>
          <Typography variant="body2">
            The notification history would display:
          </Typography>
          <ul>
            <li>Sent notifications with timestamps and recipients</li>
            <li>Delivery confirmations and failure reasons</li>
            <li>Open rates and engagement metrics for emails</li>
            <li>Response tracking and analytics</li>
            <li>Bounce and unsubscribe management</li>
            <li>Performance metrics by template and campaign</li>
          </ul>
        </Alert>
      </TabPanel>

      {/* Demo Notice */}
      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
        <Typography variant="body2">
          <strong>Demo Mode:</strong> This page displays dummy data for demonstration purposes. 
          In a real implementation, this would include:
        </Typography>
        <ul>
          <li>Rich text editor for template creation</li>
          <li>Variable insertion and preview functionality</li>
          <li>Integration with email/SMS/WhatsApp providers</li>
          <li>Automated trigger-based notifications</li>
          <li>A/B testing for templates</li>
          <li>Comprehensive delivery and engagement analytics</li>
        </ul>
      </Box>
    </Box>
  );
};

export default SimpleCommunicationsPage;
