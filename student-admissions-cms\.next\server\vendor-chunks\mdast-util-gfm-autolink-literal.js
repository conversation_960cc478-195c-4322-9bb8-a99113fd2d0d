/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-autolink-literal";
exports.ids = ["vendor-chunks/mdast-util-gfm-autolink-literal"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-autolink-literal/from-markdown.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-autolink-literal/from-markdown.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var ccount = __webpack_require__(/*! ccount */ \"(ssr)/./node_modules/ccount/index.js\")\nvar findAndReplace = __webpack_require__(/*! mdast-util-find-and-replace */ \"(ssr)/./node_modules/mdast-util-find-and-replace/index.js\")\nvar unicodePunctuation = __webpack_require__(/*! micromark/dist/character/unicode-punctuation */ \"(ssr)/./node_modules/micromark/dist/character/unicode-punctuation.js\")\nvar unicodeWhitespace = __webpack_require__(/*! micromark/dist/character/unicode-whitespace */ \"(ssr)/./node_modules/micromark/dist/character/unicode-whitespace.js\")\n\nexports.transforms = [transformGfmAutolinkLiterals]\nexports.enter = {\n  literalAutolink: enterLiteralAutolink,\n  literalAutolinkEmail: enterLiteralAutolinkValue,\n  literalAutolinkHttp: enterLiteralAutolinkValue,\n  literalAutolinkWww: enterLiteralAutolinkValue\n}\nexports.exit = {\n  literalAutolink: exitLiteralAutolink,\n  literalAutolinkEmail: exitLiteralAutolinkEmail,\n  literalAutolinkHttp: exitLiteralAutolinkHttp,\n  literalAutolinkWww: exitLiteralAutolinkWww\n}\n\nfunction enterLiteralAutolink(token) {\n  this.enter({type: 'link', title: null, url: '', children: []}, token)\n}\n\nfunction enterLiteralAutolinkValue(token) {\n  this.config.enter.autolinkProtocol.call(this, token)\n}\n\nfunction exitLiteralAutolinkHttp(token) {\n  this.config.exit.autolinkProtocol.call(this, token)\n}\n\nfunction exitLiteralAutolinkWww(token) {\n  this.config.exit.data.call(this, token)\n  this.stack[this.stack.length - 1].url = 'http://' + this.sliceSerialize(token)\n}\n\nfunction exitLiteralAutolinkEmail(token) {\n  this.config.exit.autolinkEmail.call(this, token)\n}\n\nfunction exitLiteralAutolink(token) {\n  this.exit(token)\n}\n\nfunction transformGfmAutolinkLiterals(tree) {\n  findAndReplace(\n    tree,\n    [\n      [/(https?:\\/\\/|www(?=\\.))([-.\\w]+)([^ \\t\\r\\n]*)/i, findUrl],\n      [/([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)/, findEmail]\n    ],\n    {ignore: ['link', 'linkReference']}\n  )\n}\n\nfunction findUrl($0, protocol, domain, path, match) {\n  var prefix = ''\n  var parts\n  var result\n\n  // Not an expected previous character.\n  if (!previous(match)) {\n    return false\n  }\n\n  // Treat `www` as part of the domain.\n  if (/^w/i.test(protocol)) {\n    domain = protocol + domain\n    protocol = ''\n    prefix = 'http://'\n  }\n\n  if (!isCorrectDomain(domain)) {\n    return false\n  }\n\n  parts = splitUrl(domain + path)\n\n  if (!parts[0]) return false\n\n  result = {\n    type: 'link',\n    title: null,\n    url: prefix + protocol + parts[0],\n    children: [{type: 'text', value: protocol + parts[0]}]\n  }\n\n  if (parts[1]) {\n    result = [result, {type: 'text', value: parts[1]}]\n  }\n\n  return result\n}\n\nfunction findEmail($0, atext, label, match) {\n  // Not an expected previous character.\n  if (!previous(match, true) || /[_-]$/.test(label)) {\n    return false\n  }\n\n  return {\n    type: 'link',\n    title: null,\n    url: 'mailto:' + atext + '@' + label,\n    children: [{type: 'text', value: atext + '@' + label}]\n  }\n}\n\nfunction isCorrectDomain(domain) {\n  var parts = domain.split('.')\n\n  if (\n    parts.length < 2 ||\n    (parts[parts.length - 1] &&\n      (/_/.test(parts[parts.length - 1]) ||\n        !/[a-zA-Z\\d]/.test(parts[parts.length - 1]))) ||\n    (parts[parts.length - 2] &&\n      (/_/.test(parts[parts.length - 2]) ||\n        !/[a-zA-Z\\d]/.test(parts[parts.length - 2])))\n  ) {\n    return false\n  }\n\n  return true\n}\n\nfunction splitUrl(url) {\n  var trail = /[!\"&'),.:;<>?\\]}]+$/.exec(url)\n  var closingParenIndex\n  var openingParens\n  var closingParens\n\n  if (trail) {\n    url = url.slice(0, trail.index)\n    trail = trail[0]\n    closingParenIndex = trail.indexOf(')')\n    openingParens = ccount(url, '(')\n    closingParens = ccount(url, ')')\n\n    while (closingParenIndex !== -1 && openingParens > closingParens) {\n      url += trail.slice(0, closingParenIndex + 1)\n      trail = trail.slice(closingParenIndex + 1)\n      closingParenIndex = trail.indexOf(')')\n      closingParens++\n    }\n  }\n\n  return [url, trail]\n}\n\nfunction previous(match, email) {\n  var code = match.input.charCodeAt(match.index - 1)\n  return (\n    (code !== code || unicodeWhitespace(code) || unicodePunctuation(code)) &&\n    (!email || code !== 47)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1nZm0tYXV0b2xpbmstbGl0ZXJhbC9mcm9tLW1hcmtkb3duLmpzIiwibWFwcGluZ3MiOiJBQUFBLGFBQWEsbUJBQU8sQ0FBQyxvREFBUTtBQUM3QixxQkFBcUIsbUJBQU8sQ0FBQyw4RkFBNkI7QUFDMUQseUJBQXlCLG1CQUFPLENBQUMsMEhBQThDO0FBQy9FLHdCQUF3QixtQkFBTyxDQUFDLHdIQUE2Qzs7QUFFN0Usa0JBQWtCO0FBQ2xCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxjQUFjLGlEQUFpRDtBQUMvRDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLHlDQUF5QztBQUN6RDs7QUFFQTtBQUNBLHVCQUF1Qiw4QkFBOEI7QUFDckQ7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLHlDQUF5QztBQUN6RDtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EseUJBQXlCLE1BQU07QUFDL0I7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLWdmbS1hdXRvbGluay1saXRlcmFsL2Zyb20tbWFya2Rvd24uanM/YjVhYiJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgY2NvdW50ID0gcmVxdWlyZSgnY2NvdW50JylcbnZhciBmaW5kQW5kUmVwbGFjZSA9IHJlcXVpcmUoJ21kYXN0LXV0aWwtZmluZC1hbmQtcmVwbGFjZScpXG52YXIgdW5pY29kZVB1bmN0dWF0aW9uID0gcmVxdWlyZSgnbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL3VuaWNvZGUtcHVuY3R1YXRpb24nKVxudmFyIHVuaWNvZGVXaGl0ZXNwYWNlID0gcmVxdWlyZSgnbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL3VuaWNvZGUtd2hpdGVzcGFjZScpXG5cbmV4cG9ydHMudHJhbnNmb3JtcyA9IFt0cmFuc2Zvcm1HZm1BdXRvbGlua0xpdGVyYWxzXVxuZXhwb3J0cy5lbnRlciA9IHtcbiAgbGl0ZXJhbEF1dG9saW5rOiBlbnRlckxpdGVyYWxBdXRvbGluayxcbiAgbGl0ZXJhbEF1dG9saW5rRW1haWw6IGVudGVyTGl0ZXJhbEF1dG9saW5rVmFsdWUsXG4gIGxpdGVyYWxBdXRvbGlua0h0dHA6IGVudGVyTGl0ZXJhbEF1dG9saW5rVmFsdWUsXG4gIGxpdGVyYWxBdXRvbGlua1d3dzogZW50ZXJMaXRlcmFsQXV0b2xpbmtWYWx1ZVxufVxuZXhwb3J0cy5leGl0ID0ge1xuICBsaXRlcmFsQXV0b2xpbms6IGV4aXRMaXRlcmFsQXV0b2xpbmssXG4gIGxpdGVyYWxBdXRvbGlua0VtYWlsOiBleGl0TGl0ZXJhbEF1dG9saW5rRW1haWwsXG4gIGxpdGVyYWxBdXRvbGlua0h0dHA6IGV4aXRMaXRlcmFsQXV0b2xpbmtIdHRwLFxuICBsaXRlcmFsQXV0b2xpbmtXd3c6IGV4aXRMaXRlcmFsQXV0b2xpbmtXd3dcbn1cblxuZnVuY3Rpb24gZW50ZXJMaXRlcmFsQXV0b2xpbmsodG9rZW4pIHtcbiAgdGhpcy5lbnRlcih7dHlwZTogJ2xpbmsnLCB0aXRsZTogbnVsbCwgdXJsOiAnJywgY2hpbGRyZW46IFtdfSwgdG9rZW4pXG59XG5cbmZ1bmN0aW9uIGVudGVyTGl0ZXJhbEF1dG9saW5rVmFsdWUodG9rZW4pIHtcbiAgdGhpcy5jb25maWcuZW50ZXIuYXV0b2xpbmtQcm90b2NvbC5jYWxsKHRoaXMsIHRva2VuKVxufVxuXG5mdW5jdGlvbiBleGl0TGl0ZXJhbEF1dG9saW5rSHR0cCh0b2tlbikge1xuICB0aGlzLmNvbmZpZy5leGl0LmF1dG9saW5rUHJvdG9jb2wuY2FsbCh0aGlzLCB0b2tlbilcbn1cblxuZnVuY3Rpb24gZXhpdExpdGVyYWxBdXRvbGlua1d3dyh0b2tlbikge1xuICB0aGlzLmNvbmZpZy5leGl0LmRhdGEuY2FsbCh0aGlzLCB0b2tlbilcbiAgdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDFdLnVybCA9ICdodHRwOi8vJyArIHRoaXMuc2xpY2VTZXJpYWxpemUodG9rZW4pXG59XG5cbmZ1bmN0aW9uIGV4aXRMaXRlcmFsQXV0b2xpbmtFbWFpbCh0b2tlbikge1xuICB0aGlzLmNvbmZpZy5leGl0LmF1dG9saW5rRW1haWwuY2FsbCh0aGlzLCB0b2tlbilcbn1cblxuZnVuY3Rpb24gZXhpdExpdGVyYWxBdXRvbGluayh0b2tlbikge1xuICB0aGlzLmV4aXQodG9rZW4pXG59XG5cbmZ1bmN0aW9uIHRyYW5zZm9ybUdmbUF1dG9saW5rTGl0ZXJhbHModHJlZSkge1xuICBmaW5kQW5kUmVwbGFjZShcbiAgICB0cmVlLFxuICAgIFtcbiAgICAgIFsvKGh0dHBzPzpcXC9cXC98d3d3KD89XFwuKSkoWy0uXFx3XSspKFteIFxcdFxcclxcbl0qKS9pLCBmaW5kVXJsXSxcbiAgICAgIFsvKFstLlxcdytdKylAKFstXFx3XSsoPzpcXC5bLVxcd10rKSspLywgZmluZEVtYWlsXVxuICAgIF0sXG4gICAge2lnbm9yZTogWydsaW5rJywgJ2xpbmtSZWZlcmVuY2UnXX1cbiAgKVxufVxuXG5mdW5jdGlvbiBmaW5kVXJsKCQwLCBwcm90b2NvbCwgZG9tYWluLCBwYXRoLCBtYXRjaCkge1xuICB2YXIgcHJlZml4ID0gJydcbiAgdmFyIHBhcnRzXG4gIHZhciByZXN1bHRcblxuICAvLyBOb3QgYW4gZXhwZWN0ZWQgcHJldmlvdXMgY2hhcmFjdGVyLlxuICBpZiAoIXByZXZpb3VzKG1hdGNoKSkge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG5cbiAgLy8gVHJlYXQgYHd3d2AgYXMgcGFydCBvZiB0aGUgZG9tYWluLlxuICBpZiAoL153L2kudGVzdChwcm90b2NvbCkpIHtcbiAgICBkb21haW4gPSBwcm90b2NvbCArIGRvbWFpblxuICAgIHByb3RvY29sID0gJydcbiAgICBwcmVmaXggPSAnaHR0cDovLydcbiAgfVxuXG4gIGlmICghaXNDb3JyZWN0RG9tYWluKGRvbWFpbikpIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIHBhcnRzID0gc3BsaXRVcmwoZG9tYWluICsgcGF0aClcblxuICBpZiAoIXBhcnRzWzBdKSByZXR1cm4gZmFsc2VcblxuICByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2xpbmsnLFxuICAgIHRpdGxlOiBudWxsLFxuICAgIHVybDogcHJlZml4ICsgcHJvdG9jb2wgKyBwYXJ0c1swXSxcbiAgICBjaGlsZHJlbjogW3t0eXBlOiAndGV4dCcsIHZhbHVlOiBwcm90b2NvbCArIHBhcnRzWzBdfV1cbiAgfVxuXG4gIGlmIChwYXJ0c1sxXSkge1xuICAgIHJlc3VsdCA9IFtyZXN1bHQsIHt0eXBlOiAndGV4dCcsIHZhbHVlOiBwYXJ0c1sxXX1dXG4gIH1cblxuICByZXR1cm4gcmVzdWx0XG59XG5cbmZ1bmN0aW9uIGZpbmRFbWFpbCgkMCwgYXRleHQsIGxhYmVsLCBtYXRjaCkge1xuICAvLyBOb3QgYW4gZXhwZWN0ZWQgcHJldmlvdXMgY2hhcmFjdGVyLlxuICBpZiAoIXByZXZpb3VzKG1hdGNoLCB0cnVlKSB8fCAvW18tXSQvLnRlc3QobGFiZWwpKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICByZXR1cm4ge1xuICAgIHR5cGU6ICdsaW5rJyxcbiAgICB0aXRsZTogbnVsbCxcbiAgICB1cmw6ICdtYWlsdG86JyArIGF0ZXh0ICsgJ0AnICsgbGFiZWwsXG4gICAgY2hpbGRyZW46IFt7dHlwZTogJ3RleHQnLCB2YWx1ZTogYXRleHQgKyAnQCcgKyBsYWJlbH1dXG4gIH1cbn1cblxuZnVuY3Rpb24gaXNDb3JyZWN0RG9tYWluKGRvbWFpbikge1xuICB2YXIgcGFydHMgPSBkb21haW4uc3BsaXQoJy4nKVxuXG4gIGlmIChcbiAgICBwYXJ0cy5sZW5ndGggPCAyIHx8XG4gICAgKHBhcnRzW3BhcnRzLmxlbmd0aCAtIDFdICYmXG4gICAgICAoL18vLnRlc3QocGFydHNbcGFydHMubGVuZ3RoIC0gMV0pIHx8XG4gICAgICAgICEvW2EtekEtWlxcZF0vLnRlc3QocGFydHNbcGFydHMubGVuZ3RoIC0gMV0pKSkgfHxcbiAgICAocGFydHNbcGFydHMubGVuZ3RoIC0gMl0gJiZcbiAgICAgICgvXy8udGVzdChwYXJ0c1twYXJ0cy5sZW5ndGggLSAyXSkgfHxcbiAgICAgICAgIS9bYS16QS1aXFxkXS8udGVzdChwYXJ0c1twYXJ0cy5sZW5ndGggLSAyXSkpKVxuICApIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIHJldHVybiB0cnVlXG59XG5cbmZ1bmN0aW9uIHNwbGl0VXJsKHVybCkge1xuICB2YXIgdHJhaWwgPSAvWyFcIiYnKSwuOjs8Pj9cXF19XSskLy5leGVjKHVybClcbiAgdmFyIGNsb3NpbmdQYXJlbkluZGV4XG4gIHZhciBvcGVuaW5nUGFyZW5zXG4gIHZhciBjbG9zaW5nUGFyZW5zXG5cbiAgaWYgKHRyYWlsKSB7XG4gICAgdXJsID0gdXJsLnNsaWNlKDAsIHRyYWlsLmluZGV4KVxuICAgIHRyYWlsID0gdHJhaWxbMF1cbiAgICBjbG9zaW5nUGFyZW5JbmRleCA9IHRyYWlsLmluZGV4T2YoJyknKVxuICAgIG9wZW5pbmdQYXJlbnMgPSBjY291bnQodXJsLCAnKCcpXG4gICAgY2xvc2luZ1BhcmVucyA9IGNjb3VudCh1cmwsICcpJylcblxuICAgIHdoaWxlIChjbG9zaW5nUGFyZW5JbmRleCAhPT0gLTEgJiYgb3BlbmluZ1BhcmVucyA+IGNsb3NpbmdQYXJlbnMpIHtcbiAgICAgIHVybCArPSB0cmFpbC5zbGljZSgwLCBjbG9zaW5nUGFyZW5JbmRleCArIDEpXG4gICAgICB0cmFpbCA9IHRyYWlsLnNsaWNlKGNsb3NpbmdQYXJlbkluZGV4ICsgMSlcbiAgICAgIGNsb3NpbmdQYXJlbkluZGV4ID0gdHJhaWwuaW5kZXhPZignKScpXG4gICAgICBjbG9zaW5nUGFyZW5zKytcbiAgICB9XG4gIH1cblxuICByZXR1cm4gW3VybCwgdHJhaWxdXG59XG5cbmZ1bmN0aW9uIHByZXZpb3VzKG1hdGNoLCBlbWFpbCkge1xuICB2YXIgY29kZSA9IG1hdGNoLmlucHV0LmNoYXJDb2RlQXQobWF0Y2guaW5kZXggLSAxKVxuICByZXR1cm4gKFxuICAgIChjb2RlICE9PSBjb2RlIHx8IHVuaWNvZGVXaGl0ZXNwYWNlKGNvZGUpIHx8IHVuaWNvZGVQdW5jdHVhdGlvbihjb2RlKSkgJiZcbiAgICAoIWVtYWlsIHx8IGNvZGUgIT09IDQ3KVxuICApXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-autolink-literal/from-markdown.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm-autolink-literal/to-markdown.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-autolink-literal/to-markdown.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("var inConstruct = 'phrasing'\nvar notInConstruct = ['autolink', 'link', 'image', 'label']\n\nexports.unsafe = [\n  {\n    character: '@',\n    before: '[+\\\\-.\\\\w]',\n    after: '[\\\\-.\\\\w]',\n    inConstruct: inConstruct,\n    notInConstruct: notInConstruct\n  },\n  {\n    character: '.',\n    before: '[Ww]',\n    after: '[\\\\-.\\\\w]',\n    inConstruct: inConstruct,\n    notInConstruct: notInConstruct\n  },\n  {\n    character: ':',\n    before: '[ps]',\n    after: '\\\\/',\n    inConstruct: inConstruct,\n    notInConstruct: notInConstruct\n  }\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1nZm0tYXV0b2xpbmstbGl0ZXJhbC90by1tYXJrZG93bi5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBOztBQUVBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtZ2ZtLWF1dG9saW5rLWxpdGVyYWwvdG8tbWFya2Rvd24uanM/OWUwZiJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgaW5Db25zdHJ1Y3QgPSAncGhyYXNpbmcnXG52YXIgbm90SW5Db25zdHJ1Y3QgPSBbJ2F1dG9saW5rJywgJ2xpbmsnLCAnaW1hZ2UnLCAnbGFiZWwnXVxuXG5leHBvcnRzLnVuc2FmZSA9IFtcbiAge1xuICAgIGNoYXJhY3RlcjogJ0AnLFxuICAgIGJlZm9yZTogJ1srXFxcXC0uXFxcXHddJyxcbiAgICBhZnRlcjogJ1tcXFxcLS5cXFxcd10nLFxuICAgIGluQ29uc3RydWN0OiBpbkNvbnN0cnVjdCxcbiAgICBub3RJbkNvbnN0cnVjdDogbm90SW5Db25zdHJ1Y3RcbiAgfSxcbiAge1xuICAgIGNoYXJhY3RlcjogJy4nLFxuICAgIGJlZm9yZTogJ1tXd10nLFxuICAgIGFmdGVyOiAnW1xcXFwtLlxcXFx3XScsXG4gICAgaW5Db25zdHJ1Y3Q6IGluQ29uc3RydWN0LFxuICAgIG5vdEluQ29uc3RydWN0OiBub3RJbkNvbnN0cnVjdFxuICB9LFxuICB7XG4gICAgY2hhcmFjdGVyOiAnOicsXG4gICAgYmVmb3JlOiAnW3BzXScsXG4gICAgYWZ0ZXI6ICdcXFxcLycsXG4gICAgaW5Db25zdHJ1Y3Q6IGluQ29uc3RydWN0LFxuICAgIG5vdEluQ29uc3RydWN0OiBub3RJbkNvbnN0cnVjdFxuICB9XG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-autolink-literal/to-markdown.js\n");

/***/ })

};
;