/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-markdown";
exports.ids = ["vendor-chunks/mdast-util-to-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/configure.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/configure.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports = configure\n\nfunction configure(base, extension) {\n  var index = -1\n  var key\n\n  // First do subextensions.\n  if (extension.extensions) {\n    while (++index < extension.extensions.length) {\n      configure(base, extension.extensions[index])\n    }\n  }\n\n  for (key in extension) {\n    if (key === 'extensions') {\n      // Empty.\n    } else if (key === 'unsafe' || key === 'join') {\n      base[key] = base[key].concat(extension[key] || [])\n    } else if (key === 'handlers') {\n      base[key] = Object.assign(base[key], extension[key] || {})\n    } else {\n      base.options[key] = extension[key]\n    }\n  }\n\n  return base\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvY29uZmlndXJlLmpzIiwibWFwcGluZ3MiOiJBQUFBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOLCtEQUErRDtBQUMvRCxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2NvbmZpZ3VyZS5qcz80ZDFhIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gY29uZmlndXJlXG5cbmZ1bmN0aW9uIGNvbmZpZ3VyZShiYXNlLCBleHRlbnNpb24pIHtcbiAgdmFyIGluZGV4ID0gLTFcbiAgdmFyIGtleVxuXG4gIC8vIEZpcnN0IGRvIHN1YmV4dGVuc2lvbnMuXG4gIGlmIChleHRlbnNpb24uZXh0ZW5zaW9ucykge1xuICAgIHdoaWxlICgrK2luZGV4IDwgZXh0ZW5zaW9uLmV4dGVuc2lvbnMubGVuZ3RoKSB7XG4gICAgICBjb25maWd1cmUoYmFzZSwgZXh0ZW5zaW9uLmV4dGVuc2lvbnNbaW5kZXhdKVxuICAgIH1cbiAgfVxuXG4gIGZvciAoa2V5IGluIGV4dGVuc2lvbikge1xuICAgIGlmIChrZXkgPT09ICdleHRlbnNpb25zJykge1xuICAgICAgLy8gRW1wdHkuXG4gICAgfSBlbHNlIGlmIChrZXkgPT09ICd1bnNhZmUnIHx8IGtleSA9PT0gJ2pvaW4nKSB7XG4gICAgICBiYXNlW2tleV0gPSBiYXNlW2tleV0uY29uY2F0KGV4dGVuc2lvbltrZXldIHx8IFtdKVxuICAgIH0gZWxzZSBpZiAoa2V5ID09PSAnaGFuZGxlcnMnKSB7XG4gICAgICBiYXNlW2tleV0gPSBPYmplY3QuYXNzaWduKGJhc2Vba2V5XSwgZXh0ZW5zaW9uW2tleV0gfHwge30pXG4gICAgfSBlbHNlIHtcbiAgICAgIGJhc2Uub3B0aW9uc1trZXldID0gZXh0ZW5zaW9uW2tleV1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gYmFzZVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/configure.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = inlineCode\ninlineCode.peek = inlineCodePeek\n\nvar patternCompile = __webpack_require__(/*! ../util/pattern-compile */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js\")\n\nfunction inlineCode(node, parent, context) {\n  var value = node.value || ''\n  var sequence = '`'\n  var index = -1\n  var pattern\n  var expression\n  var match\n  var position\n\n  // If there is a single grave accent on its own in the code, use a fence of\n  // two.\n  // If there are two in a row, use one.\n  while (new RegExp('(^|[^`])' + sequence + '([^`]|$)').test(value)) {\n    sequence += '`'\n  }\n\n  // If this is not just spaces or eols (tabs don’t count), and either the\n  // first or last character are a space, eol, or tick, then pad with spaces.\n  if (\n    /[^ \\r\\n]/.test(value) &&\n    (/[ \\r\\n`]/.test(value.charAt(0)) ||\n      /[ \\r\\n`]/.test(value.charAt(value.length - 1)))\n  ) {\n    value = ' ' + value + ' '\n  }\n\n  // We have a potential problem: certain characters after eols could result in\n  // blocks being seen.\n  // For example, if someone injected the string `'\\n# b'`, then that would\n  // result in an ATX heading.\n  // We can’t escape characters in `inlineCode`, but because eols are\n  // transformed to spaces when going from markdown to HTML anyway, we can swap\n  // them out.\n  while (++index < context.unsafe.length) {\n    pattern = context.unsafe[index]\n\n    // Only look for `atBreak`s.\n    // Btw: note that `atBreak` patterns will always start the regex at LF or\n    // CR.\n    if (!pattern.atBreak) continue\n\n    expression = patternCompile(pattern)\n\n    while ((match = expression.exec(value))) {\n      position = match.index\n\n      // Support CRLF (patterns only look for one of the characters).\n      if (\n        value.charCodeAt(position) === 10 /* `\\n` */ &&\n        value.charCodeAt(position - 1) === 13 /* `\\r` */\n      ) {\n        position--\n      }\n\n      value = value.slice(0, position) + ' ' + value.slice(match.index + 1)\n    }\n  }\n\n  return sequence + value + sequence\n}\n\nfunction inlineCodePeek() {\n  return '`'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list-item.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = listItem\n\nvar repeat = __webpack_require__(/*! repeat-string */ \"(ssr)/./node_modules/repeat-string/index.js\")\nvar checkBullet = __webpack_require__(/*! ../util/check-bullet */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\")\nvar checkListItemIndent = __webpack_require__(/*! ../util/check-list-item-indent */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\")\nvar flow = __webpack_require__(/*! ../util/container-flow */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js\")\nvar indentLines = __webpack_require__(/*! ../util/indent-lines */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js\")\n\nfunction listItem(node, parent, context) {\n  var bullet = checkBullet(context)\n  var listItemIndent = checkListItemIndent(context)\n  var size\n  var value\n  var exit\n\n  if (parent && parent.ordered) {\n    bullet =\n      (parent.start > -1 ? parent.start : 1) +\n      (context.options.incrementListMarker === false\n        ? 0\n        : parent.children.indexOf(node)) +\n      '.'\n  }\n\n  size = bullet.length + 1\n\n  if (\n    listItemIndent === 'tab' ||\n    (listItemIndent === 'mixed' && ((parent && parent.spread) || node.spread))\n  ) {\n    size = Math.ceil(size / 4) * 4\n  }\n\n  exit = context.enter('listItem')\n  value = indentLines(flow(node, context), map)\n  exit()\n\n  return value\n\n  function map(line, index, blank) {\n    if (index) {\n      return (blank ? '' : repeat(' ', size)) + line\n    }\n\n    return (blank ? bullet : bullet + repeat(' ', size - bullet.length)) + line\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js ***!
  \**********************************************************************/
/***/ ((module) => {

eval("module.exports = checkBullet\n\nfunction checkBullet(context) {\n  var marker = context.options.bullet || '*'\n\n  if (marker !== '*' && marker !== '+' && marker !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bullet`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWJ1bGxldC5qcz9kZTEwIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gY2hlY2tCdWxsZXRcblxuZnVuY3Rpb24gY2hlY2tCdWxsZXQoY29udGV4dCkge1xuICB2YXIgbWFya2VyID0gY29udGV4dC5vcHRpb25zLmJ1bGxldCB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnKycgJiYgbWFya2VyICE9PSAnLScpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmJ1bGxldGAsIGV4cGVjdGVkIGAqYCwgYCtgLCBvciBgLWAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js":
/*!********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js ***!
  \********************************************************************************/
/***/ ((module) => {

eval("module.exports = checkListItemIndent\n\nfunction checkListItemIndent(context) {\n  var style = context.options.listItemIndent || 'tab'\n\n  if (style === 1 || style === '1') {\n    return 'one'\n  }\n\n  if (style !== 'tab' && style !== 'one' && style !== 'mixed') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        style +\n        '` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`'\n    )\n  }\n\n  return style\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1saXN0LWl0ZW0taW5kZW50LmpzIiwibWFwcGluZ3MiOiJBQUFBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stbGlzdC1pdGVtLWluZGVudC5qcz8xYjQ1Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gY2hlY2tMaXN0SXRlbUluZGVudFxuXG5mdW5jdGlvbiBjaGVja0xpc3RJdGVtSW5kZW50KGNvbnRleHQpIHtcbiAgdmFyIHN0eWxlID0gY29udGV4dC5vcHRpb25zLmxpc3RJdGVtSW5kZW50IHx8ICd0YWInXG5cbiAgaWYgKHN0eWxlID09PSAxIHx8IHN0eWxlID09PSAnMScpIHtcbiAgICByZXR1cm4gJ29uZSdcbiAgfVxuXG4gIGlmIChzdHlsZSAhPT0gJ3RhYicgJiYgc3R5bGUgIT09ICdvbmUnICYmIHN0eWxlICE9PSAnbWl4ZWQnKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgaXRlbXMgd2l0aCBgJyArXG4gICAgICAgIHN0eWxlICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmxpc3RJdGVtSW5kZW50YCwgZXhwZWN0ZWQgYHRhYmAsIGBvbmVgLCBvciBgbWl4ZWRgJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBzdHlsZVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/container-flow.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = flow\n\nvar repeat = __webpack_require__(/*! repeat-string */ \"(ssr)/./node_modules/repeat-string/index.js\")\n\nfunction flow(parent, context) {\n  var children = parent.children || []\n  var results = []\n  var index = -1\n  var child\n\n  while (++index < children.length) {\n    child = children[index]\n\n    results.push(\n      context.handle(child, parent, context, {before: '\\n', after: '\\n'})\n    )\n\n    if (index + 1 < children.length) {\n      results.push(between(child, children[index + 1]))\n    }\n  }\n\n  return results.join('')\n\n  function between(left, right) {\n    var index = -1\n    var result\n\n    while (++index < context.join.length) {\n      result = context.join[index](left, right, parent, context)\n\n      if (result === true || result === 1) {\n        break\n      }\n\n      if (typeof result === 'number') {\n        return repeat('\\n', 1 + Number(result))\n      }\n\n      if (result === false) {\n        return '\\n\\n<!---->\\n\\n'\n      }\n    }\n\n    return '\\n\\n'\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jb250YWluZXItZmxvdy5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7QUFFQSxhQUFhLG1CQUFPLENBQUMsa0VBQWU7O0FBRXBDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLDhDQUE4QywwQkFBMEI7QUFDeEU7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY29udGFpbmVyLWZsb3cuanM/ZDU1ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGZsb3dcblxudmFyIHJlcGVhdCA9IHJlcXVpcmUoJ3JlcGVhdC1zdHJpbmcnKVxuXG5mdW5jdGlvbiBmbG93KHBhcmVudCwgY29udGV4dCkge1xuICB2YXIgY2hpbGRyZW4gPSBwYXJlbnQuY2hpbGRyZW4gfHwgW11cbiAgdmFyIHJlc3VsdHMgPSBbXVxuICB2YXIgaW5kZXggPSAtMVxuICB2YXIgY2hpbGRcblxuICB3aGlsZSAoKytpbmRleCA8IGNoaWxkcmVuLmxlbmd0aCkge1xuICAgIGNoaWxkID0gY2hpbGRyZW5baW5kZXhdXG5cbiAgICByZXN1bHRzLnB1c2goXG4gICAgICBjb250ZXh0LmhhbmRsZShjaGlsZCwgcGFyZW50LCBjb250ZXh0LCB7YmVmb3JlOiAnXFxuJywgYWZ0ZXI6ICdcXG4nfSlcbiAgICApXG5cbiAgICBpZiAoaW5kZXggKyAxIDwgY2hpbGRyZW4ubGVuZ3RoKSB7XG4gICAgICByZXN1bHRzLnB1c2goYmV0d2VlbihjaGlsZCwgY2hpbGRyZW5baW5kZXggKyAxXSkpXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHJlc3VsdHMuam9pbignJylcblxuICBmdW5jdGlvbiBiZXR3ZWVuKGxlZnQsIHJpZ2h0KSB7XG4gICAgdmFyIGluZGV4ID0gLTFcbiAgICB2YXIgcmVzdWx0XG5cbiAgICB3aGlsZSAoKytpbmRleCA8IGNvbnRleHQuam9pbi5sZW5ndGgpIHtcbiAgICAgIHJlc3VsdCA9IGNvbnRleHQuam9pbltpbmRleF0obGVmdCwgcmlnaHQsIHBhcmVudCwgY29udGV4dClcblxuICAgICAgaWYgKHJlc3VsdCA9PT0gdHJ1ZSB8fCByZXN1bHQgPT09IDEpIHtcbiAgICAgICAgYnJlYWtcbiAgICAgIH1cblxuICAgICAgaWYgKHR5cGVvZiByZXN1bHQgPT09ICdudW1iZXInKSB7XG4gICAgICAgIHJldHVybiByZXBlYXQoJ1xcbicsIDEgKyBOdW1iZXIocmVzdWx0KSlcbiAgICAgIH1cblxuICAgICAgaWYgKHJlc3VsdCA9PT0gZmFsc2UpIHtcbiAgICAgICAgcmV0dXJuICdcXG5cXG48IS0tLS0+XFxuXFxuJ1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiAnXFxuXFxuJ1xuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js ***!
  \****************************************************************************/
/***/ ((module) => {

eval("module.exports = phrasing\n\nfunction phrasing(parent, context, safeOptions) {\n  var children = parent.children || []\n  var results = []\n  var index = -1\n  var before = safeOptions.before\n  var after\n  var handle\n  var child\n\n  while (++index < children.length) {\n    child = children[index]\n\n    if (index + 1 < children.length) {\n      handle = context.handle.handlers[children[index + 1].type]\n      if (handle && handle.peek) handle = handle.peek\n      after = handle\n        ? handle(children[index + 1], parent, context, {\n            before: '',\n            after: ''\n          }).charAt(0)\n        : ''\n    } else {\n      after = safeOptions.after\n    }\n\n    // In some cases, html (text) can be found in phrasing right after an eol.\n    // When we’d serialize that, in most cases that would be seen as html\n    // (flow).\n    // As we can’t escape or so to prevent it from happening, we take a somewhat\n    // reasonable approach: replace that eol with a space.\n    // See: <https://github.com/syntax-tree/mdast-util-to-markdown/issues/15>\n    if (\n      results.length > 0 &&\n      (before === '\\r' || before === '\\n') &&\n      child.type === 'html'\n    ) {\n      results[results.length - 1] = results[results.length - 1].replace(\n        /(\\r?\\n|\\r)$/,\n        ' '\n      )\n      before = ' '\n    }\n\n    results.push(\n      context.handle(child, parent, context, {\n        before: before,\n        after: after\n      })\n    )\n\n    before = results[results.length - 1].slice(-1)\n  }\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js ***!
  \**********************************************************************/
/***/ ((module) => {

eval("module.exports = indentLines\n\nvar eol = /\\r?\\n|\\r/g\n\nfunction indentLines(value, map) {\n  var result = []\n  var start = 0\n  var line = 0\n  var match\n\n  while ((match = eol.exec(value))) {\n    one(value.slice(start, match.index))\n    result.push(match[0])\n    start = match.index + match[0].length\n    line++\n  }\n\n  one(value.slice(start))\n\n  return result.join('')\n\n  function one(value) {\n    result.push(map(value, line, !value))\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9pbmRlbnQtbGluZXMuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvaW5kZW50LWxpbmVzLmpzP2MzOGQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBpbmRlbnRMaW5lc1xuXG52YXIgZW9sID0gL1xccj9cXG58XFxyL2dcblxuZnVuY3Rpb24gaW5kZW50TGluZXModmFsdWUsIG1hcCkge1xuICB2YXIgcmVzdWx0ID0gW11cbiAgdmFyIHN0YXJ0ID0gMFxuICB2YXIgbGluZSA9IDBcbiAgdmFyIG1hdGNoXG5cbiAgd2hpbGUgKChtYXRjaCA9IGVvbC5leGVjKHZhbHVlKSkpIHtcbiAgICBvbmUodmFsdWUuc2xpY2Uoc3RhcnQsIG1hdGNoLmluZGV4KSlcbiAgICByZXN1bHQucHVzaChtYXRjaFswXSlcbiAgICBzdGFydCA9IG1hdGNoLmluZGV4ICsgbWF0Y2hbMF0ubGVuZ3RoXG4gICAgbGluZSsrXG4gIH1cblxuICBvbmUodmFsdWUuc2xpY2Uoc3RhcnQpKVxuXG4gIHJldHVybiByZXN1bHQuam9pbignJylcblxuICBmdW5jdGlvbiBvbmUodmFsdWUpIHtcbiAgICByZXN1bHQucHVzaChtYXAodmFsdWUsIGxpbmUsICF2YWx1ZSkpXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js":
/*!*************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js ***!
  \*************************************************************************/
/***/ ((module) => {

eval("module.exports = patternCompile\n\nfunction patternCompile(pattern) {\n  var before\n  var after\n\n  if (!pattern._compiled) {\n    before = pattern.before ? '(?:' + pattern.before + ')' : ''\n    after = pattern.after ? '(?:' + pattern.after + ')' : ''\n\n    if (pattern.atBreak) {\n      before = '[\\\\r\\\\n][\\\\t ]*' + before\n    }\n\n    pattern._compiled = new RegExp(\n      (before ? '(' + before + ')' : '') +\n        (/[|\\\\{}()[\\]^$+*?.-]/.test(pattern.character) ? '\\\\' : '') +\n        pattern.character +\n        (after || ''),\n      'g'\n    )\n  }\n\n  return pattern._compiled\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9wYXR0ZXJuLWNvbXBpbGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvcGF0dGVybi1jb21waWxlLmpzP2ZhNWIiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBwYXR0ZXJuQ29tcGlsZVxuXG5mdW5jdGlvbiBwYXR0ZXJuQ29tcGlsZShwYXR0ZXJuKSB7XG4gIHZhciBiZWZvcmVcbiAgdmFyIGFmdGVyXG5cbiAgaWYgKCFwYXR0ZXJuLl9jb21waWxlZCkge1xuICAgIGJlZm9yZSA9IHBhdHRlcm4uYmVmb3JlID8gJyg/OicgKyBwYXR0ZXJuLmJlZm9yZSArICcpJyA6ICcnXG4gICAgYWZ0ZXIgPSBwYXR0ZXJuLmFmdGVyID8gJyg/OicgKyBwYXR0ZXJuLmFmdGVyICsgJyknIDogJydcblxuICAgIGlmIChwYXR0ZXJuLmF0QnJlYWspIHtcbiAgICAgIGJlZm9yZSA9ICdbXFxcXHJcXFxcbl1bXFxcXHQgXSonICsgYmVmb3JlXG4gICAgfVxuXG4gICAgcGF0dGVybi5fY29tcGlsZWQgPSBuZXcgUmVnRXhwKFxuICAgICAgKGJlZm9yZSA/ICcoJyArIGJlZm9yZSArICcpJyA6ICcnKSArXG4gICAgICAgICgvW3xcXFxce30oKVtcXF1eJCsqPy4tXS8udGVzdChwYXR0ZXJuLmNoYXJhY3RlcikgPyAnXFxcXCcgOiAnJykgK1xuICAgICAgICBwYXR0ZXJuLmNoYXJhY3RlciArXG4gICAgICAgIChhZnRlciB8fCAnJyksXG4gICAgICAnZydcbiAgICApXG4gIH1cblxuICByZXR1cm4gcGF0dGVybi5fY29tcGlsZWRcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js\n");

/***/ })

};
;