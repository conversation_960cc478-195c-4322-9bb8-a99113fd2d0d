/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/stackframe";
exports.ids = ["vendor-chunks/stackframe"];
exports.modules = {

/***/ "(ssr)/./node_modules/stackframe/stackframe.js":
/*!***********************************************!*\
  !*** ./node_modules/stackframe/stackframe.js ***!
  \***********************************************/
/***/ (function(module, exports) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    } else {}\n}(this, function() {\n    'use strict';\n    function _isNumber(n) {\n        return !isNaN(parseFloat(n)) && isFinite(n);\n    }\n\n    function _capitalize(str) {\n        return str.charAt(0).toUpperCase() + str.substring(1);\n    }\n\n    function _getter(p) {\n        return function() {\n            return this[p];\n        };\n    }\n\n    var booleanProps = ['isConstructor', 'isEval', 'isNative', 'isToplevel'];\n    var numericProps = ['columnNumber', 'lineNumber'];\n    var stringProps = ['fileName', 'functionName', 'source'];\n    var arrayProps = ['args'];\n    var objectProps = ['evalOrigin'];\n\n    var props = booleanProps.concat(numericProps, stringProps, arrayProps, objectProps);\n\n    function StackFrame(obj) {\n        if (!obj) return;\n        for (var i = 0; i < props.length; i++) {\n            if (obj[props[i]] !== undefined) {\n                this['set' + _capitalize(props[i])](obj[props[i]]);\n            }\n        }\n    }\n\n    StackFrame.prototype = {\n        getArgs: function() {\n            return this.args;\n        },\n        setArgs: function(v) {\n            if (Object.prototype.toString.call(v) !== '[object Array]') {\n                throw new TypeError('Args must be an Array');\n            }\n            this.args = v;\n        },\n\n        getEvalOrigin: function() {\n            return this.evalOrigin;\n        },\n        setEvalOrigin: function(v) {\n            if (v instanceof StackFrame) {\n                this.evalOrigin = v;\n            } else if (v instanceof Object) {\n                this.evalOrigin = new StackFrame(v);\n            } else {\n                throw new TypeError('Eval Origin must be an Object or StackFrame');\n            }\n        },\n\n        toString: function() {\n            var fileName = this.getFileName() || '';\n            var lineNumber = this.getLineNumber() || '';\n            var columnNumber = this.getColumnNumber() || '';\n            var functionName = this.getFunctionName() || '';\n            if (this.getIsEval()) {\n                if (fileName) {\n                    return '[eval] (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n                }\n                return '[eval]:' + lineNumber + ':' + columnNumber;\n            }\n            if (functionName) {\n                return functionName + ' (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n            }\n            return fileName + ':' + lineNumber + ':' + columnNumber;\n        }\n    };\n\n    StackFrame.fromString = function StackFrame$$fromString(str) {\n        var argsStartIndex = str.indexOf('(');\n        var argsEndIndex = str.lastIndexOf(')');\n\n        var functionName = str.substring(0, argsStartIndex);\n        var args = str.substring(argsStartIndex + 1, argsEndIndex).split(',');\n        var locationString = str.substring(argsEndIndex + 1);\n\n        if (locationString.indexOf('@') === 0) {\n            var parts = /@(.+?)(?::(\\d+))?(?::(\\d+))?$/.exec(locationString, '');\n            var fileName = parts[1];\n            var lineNumber = parts[2];\n            var columnNumber = parts[3];\n        }\n\n        return new StackFrame({\n            functionName: functionName,\n            args: args || undefined,\n            fileName: fileName,\n            lineNumber: lineNumber || undefined,\n            columnNumber: columnNumber || undefined\n        });\n    };\n\n    for (var i = 0; i < booleanProps.length; i++) {\n        StackFrame.prototype['get' + _capitalize(booleanProps[i])] = _getter(booleanProps[i]);\n        StackFrame.prototype['set' + _capitalize(booleanProps[i])] = (function(p) {\n            return function(v) {\n                this[p] = Boolean(v);\n            };\n        })(booleanProps[i]);\n    }\n\n    for (var j = 0; j < numericProps.length; j++) {\n        StackFrame.prototype['get' + _capitalize(numericProps[j])] = _getter(numericProps[j]);\n        StackFrame.prototype['set' + _capitalize(numericProps[j])] = (function(p) {\n            return function(v) {\n                if (!_isNumber(v)) {\n                    throw new TypeError(p + ' must be a Number');\n                }\n                this[p] = Number(v);\n            };\n        })(numericProps[j]);\n    }\n\n    for (var k = 0; k < stringProps.length; k++) {\n        StackFrame.prototype['get' + _capitalize(stringProps[k])] = _getter(stringProps[k]);\n        StackFrame.prototype['set' + _capitalize(stringProps[k])] = (function(p) {\n            return function(v) {\n                this[p] = String(v);\n            };\n        })(stringProps[k]);\n    }\n\n    return StackFrame;\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stackframe/stackframe.js\n");

/***/ })

};
;