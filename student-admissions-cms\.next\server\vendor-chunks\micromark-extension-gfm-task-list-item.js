/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-task-list-item";
exports.ids = ["vendor-chunks/micromark-extension-gfm-task-list-item"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-task-list-item/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-task-list-item/index.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./syntax */ \"(ssr)/./node_modules/micromark-extension-gfm-task-list-item/syntax.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tdGFzay1saXN0LWl0ZW0vaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsNkhBQW9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmstZXh0ZW5zaW9uLWdmbS10YXNrLWxpc3QtaXRlbS9pbmRleC5qcz80MmYyIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9zeW50YXgnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-task-list-item/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-task-list-item/syntax.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-task-list-item/syntax.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var markdownLineEndingOrSpace = __webpack_require__(/*! micromark/dist/character/markdown-line-ending-or-space */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending-or-space.js\")\nvar spaceFactory = __webpack_require__(/*! micromark/dist/tokenize/factory-space */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\nvar prefixSize = __webpack_require__(/*! micromark/dist/util/prefix-size */ \"(ssr)/./node_modules/micromark/dist/util/prefix-size.js\")\n\nvar tasklistCheck = {tokenize: tokenizeTasklistCheck}\n\nexports.text = {91: tasklistCheck}\n\nfunction tokenizeTasklistCheck(effects, ok, nok) {\n  var self = this\n\n  return open\n\n  function open(code) {\n    if (\n      // Exit if not `[`.\n      code !== 91 ||\n      // Exit if there’s stuff before.\n      self.previous !== null ||\n      // Exit if not in the first content that is the first child of a list\n      // item.\n      !self._gfmTasklistFirstContentOfListItem\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('taskListCheck')\n    effects.enter('taskListCheckMarker')\n    effects.consume(code)\n    effects.exit('taskListCheckMarker')\n    return inside\n  }\n\n  function inside(code) {\n    // Tab or space.\n    if (code === -2 || code === 32) {\n      effects.enter('taskListCheckValueUnchecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueUnchecked')\n      return close\n    }\n\n    // Upper- and lower `x`.\n    if (code === 88 || code === 120) {\n      effects.enter('taskListCheckValueChecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueChecked')\n      return close\n    }\n\n    return nok(code)\n  }\n\n  function close(code) {\n    // `]`\n    if (code === 93) {\n      effects.enter('taskListCheckMarker')\n      effects.consume(code)\n      effects.exit('taskListCheckMarker')\n      effects.exit('taskListCheck')\n      return effects.check({tokenize: spaceThenNonSpace}, ok, nok)\n    }\n\n    return nok(code)\n  }\n}\n\nfunction spaceThenNonSpace(effects, ok, nok) {\n  var self = this\n\n  return spaceFactory(effects, after, 'whitespace')\n\n  function after(code) {\n    return prefixSize(self.events, 'whitespace') &&\n      code !== null &&\n      !markdownLineEndingOrSpace(code)\n      ? ok(code)\n      : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-task-list-item/syntax.js\n");

/***/ })

};
;