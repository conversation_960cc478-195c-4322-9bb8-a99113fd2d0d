{
  "name": "<%= name %>",
  "version": "0.1.0",
  "private": true,
  "type": "module",
  "dependencies": {
    "@refinedev/cli": "^2.16.21",
    "@refinedev/core": "^4.47.1",
    "@refinedev/devtools": "^1.1.32",
    "@refinedev/kbar": "^1.3.6",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "react-router": "^7.0.2"
  },
  "devDependencies": {
    "@types/node": "^18.16.2",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "@typescript-eslint/eslint-plugin": "^5.57.1",
    "@typescript-eslint/parser": "^5.57.1",
    "@vitejs/plugin-react": "^4.0.0",
    "eslint": "^8.38.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.3.4",
    "typescript": "^5.4.2",
    "vite": "^4.3.1"
  },
  "scripts": {
    "dev": "refine dev",
    "build": "tsc && refine build",
    "start": "refine start",
    "refine": "refine"
  },
  "browserslist": {
    "production": [">0.2%", "not dead", "not op_mini all"],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  <%_ if (typeof projectId !== 'undefined' && projectId !== '') { _%>
  },
  "refine": {
      "projectId": "<%= projectId %>"
  }
  <%_ } else { _%>
  }
  <%_ } _%>
}
