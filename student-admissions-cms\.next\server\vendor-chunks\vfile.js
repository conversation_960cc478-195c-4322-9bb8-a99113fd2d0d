"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vfile";
exports.ids = ["vendor-chunks/vfile"];
exports.modules = {

/***/ "(ssr)/./node_modules/vfile/index.js":
/*!*************************************!*\
  !*** ./node_modules/vfile/index.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = __webpack_require__(/*! ./lib */ \"(ssr)/./node_modules/vfile/lib/index.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmZpbGUvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosNEZBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy92ZmlsZS9pbmRleC5qcz81NzQ1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vbGliJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vfile/lib/core.js":
/*!****************************************!*\
  !*** ./node_modules/vfile/lib/core.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar p = __webpack_require__(/*! ./minpath */ \"(ssr)/./node_modules/vfile/lib/minpath.js\")\nvar proc = __webpack_require__(/*! ./minproc */ \"(ssr)/./node_modules/vfile/lib/minproc.js\")\nvar buffer = __webpack_require__(/*! is-buffer */ \"(ssr)/./node_modules/is-buffer/index.js\")\n\nmodule.exports = VFile\n\nvar own = {}.hasOwnProperty\n\n// Order of setting (least specific to most), we need this because otherwise\n// `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n// stem can be set.\nvar order = ['history', 'path', 'basename', 'stem', 'extname', 'dirname']\n\nVFile.prototype.toString = toString\n\n// Access full path (`~/index.min.js`).\nObject.defineProperty(VFile.prototype, 'path', {get: getPath, set: setPath})\n\n// Access parent path (`~`).\nObject.defineProperty(VFile.prototype, 'dirname', {\n  get: getDirname,\n  set: setDirname\n})\n\n// Access basename (`index.min.js`).\nObject.defineProperty(VFile.prototype, 'basename', {\n  get: getBasename,\n  set: setBasename\n})\n\n// Access extname (`.js`).\nObject.defineProperty(VFile.prototype, 'extname', {\n  get: getExtname,\n  set: setExtname\n})\n\n// Access stem (`index.min`).\nObject.defineProperty(VFile.prototype, 'stem', {get: getStem, set: setStem})\n\n// Construct a new file.\nfunction VFile(options) {\n  var prop\n  var index\n\n  if (!options) {\n    options = {}\n  } else if (typeof options === 'string' || buffer(options)) {\n    options = {contents: options}\n  } else if ('message' in options && 'messages' in options) {\n    return options\n  }\n\n  if (!(this instanceof VFile)) {\n    return new VFile(options)\n  }\n\n  this.data = {}\n  this.messages = []\n  this.history = []\n  this.cwd = proc.cwd()\n\n  // Set path related properties in the correct order.\n  index = -1\n\n  while (++index < order.length) {\n    prop = order[index]\n\n    if (own.call(options, prop)) {\n      this[prop] = options[prop]\n    }\n  }\n\n  // Set non-path related properties.\n  for (prop in options) {\n    if (order.indexOf(prop) < 0) {\n      this[prop] = options[prop]\n    }\n  }\n}\n\nfunction getPath() {\n  return this.history[this.history.length - 1]\n}\n\nfunction setPath(path) {\n  assertNonEmpty(path, 'path')\n\n  if (this.path !== path) {\n    this.history.push(path)\n  }\n}\n\nfunction getDirname() {\n  return typeof this.path === 'string' ? p.dirname(this.path) : undefined\n}\n\nfunction setDirname(dirname) {\n  assertPath(this.path, 'dirname')\n  this.path = p.join(dirname || '', this.basename)\n}\n\nfunction getBasename() {\n  return typeof this.path === 'string' ? p.basename(this.path) : undefined\n}\n\nfunction setBasename(basename) {\n  assertNonEmpty(basename, 'basename')\n  assertPart(basename, 'basename')\n  this.path = p.join(this.dirname || '', basename)\n}\n\nfunction getExtname() {\n  return typeof this.path === 'string' ? p.extname(this.path) : undefined\n}\n\nfunction setExtname(extname) {\n  assertPart(extname, 'extname')\n  assertPath(this.path, 'extname')\n\n  if (extname) {\n    if (extname.charCodeAt(0) !== 46 /* `.` */) {\n      throw new Error('`extname` must start with `.`')\n    }\n\n    if (extname.indexOf('.', 1) > -1) {\n      throw new Error('`extname` cannot contain multiple dots')\n    }\n  }\n\n  this.path = p.join(this.dirname, this.stem + (extname || ''))\n}\n\nfunction getStem() {\n  return typeof this.path === 'string'\n    ? p.basename(this.path, this.extname)\n    : undefined\n}\n\nfunction setStem(stem) {\n  assertNonEmpty(stem, 'stem')\n  assertPart(stem, 'stem')\n  this.path = p.join(this.dirname || '', stem + (this.extname || ''))\n}\n\n// Get the value of the file.\nfunction toString(encoding) {\n  return (this.contents || '').toString(encoding)\n}\n\n// Assert that `part` is not a path (i.e., does not contain `p.sep`).\nfunction assertPart(part, name) {\n  if (part && part.indexOf(p.sep) > -1) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + p.sep + '`'\n    )\n  }\n}\n\n// Assert that `part` is not empty.\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n// Assert `path` exists.\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vfile/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/vfile/lib/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar VMessage = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/index.js\")\nvar VFile = __webpack_require__(/*! ./core.js */ \"(ssr)/./node_modules/vfile/lib/core.js\")\n\nmodule.exports = VFile\n\nVFile.prototype.message = message\nVFile.prototype.info = info\nVFile.prototype.fail = fail\n\n// Create a message with `reason` at `position`.\n// When an error is passed in as `reason`, copies the stack.\nfunction message(reason, position, origin) {\n  var message = new VMessage(reason, position, origin)\n\n  if (this.path) {\n    message.name = this.path + ':' + message.name\n    message.file = this.path\n  }\n\n  message.fatal = false\n\n  this.messages.push(message)\n\n  return message\n}\n\n// Fail: creates a vmessage, associates it with the file, and throws it.\nfunction fail() {\n  var message = this.message.apply(this, arguments)\n\n  message.fatal = true\n\n  throw message\n}\n\n// Info: creates a vmessage, associates it with the file, and marks the fatality\n// as null.\nfunction info() {\n  var message = this.message.apply(this, arguments)\n\n  message.fatal = null\n\n  return message\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmZpbGUvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGVBQWUsbUJBQU8sQ0FBQyxrRUFBZTtBQUN0QyxZQUFZLG1CQUFPLENBQUMseURBQVc7O0FBRS9COztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL3ZmaWxlL2xpYi9pbmRleC5qcz82MmEzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgVk1lc3NhZ2UgPSByZXF1aXJlKCd2ZmlsZS1tZXNzYWdlJylcbnZhciBWRmlsZSA9IHJlcXVpcmUoJy4vY29yZS5qcycpXG5cbm1vZHVsZS5leHBvcnRzID0gVkZpbGVcblxuVkZpbGUucHJvdG90eXBlLm1lc3NhZ2UgPSBtZXNzYWdlXG5WRmlsZS5wcm90b3R5cGUuaW5mbyA9IGluZm9cblZGaWxlLnByb3RvdHlwZS5mYWlsID0gZmFpbFxuXG4vLyBDcmVhdGUgYSBtZXNzYWdlIHdpdGggYHJlYXNvbmAgYXQgYHBvc2l0aW9uYC5cbi8vIFdoZW4gYW4gZXJyb3IgaXMgcGFzc2VkIGluIGFzIGByZWFzb25gLCBjb3BpZXMgdGhlIHN0YWNrLlxuZnVuY3Rpb24gbWVzc2FnZShyZWFzb24sIHBvc2l0aW9uLCBvcmlnaW4pIHtcbiAgdmFyIG1lc3NhZ2UgPSBuZXcgVk1lc3NhZ2UocmVhc29uLCBwb3NpdGlvbiwgb3JpZ2luKVxuXG4gIGlmICh0aGlzLnBhdGgpIHtcbiAgICBtZXNzYWdlLm5hbWUgPSB0aGlzLnBhdGggKyAnOicgKyBtZXNzYWdlLm5hbWVcbiAgICBtZXNzYWdlLmZpbGUgPSB0aGlzLnBhdGhcbiAgfVxuXG4gIG1lc3NhZ2UuZmF0YWwgPSBmYWxzZVxuXG4gIHRoaXMubWVzc2FnZXMucHVzaChtZXNzYWdlKVxuXG4gIHJldHVybiBtZXNzYWdlXG59XG5cbi8vIEZhaWw6IGNyZWF0ZXMgYSB2bWVzc2FnZSwgYXNzb2NpYXRlcyBpdCB3aXRoIHRoZSBmaWxlLCBhbmQgdGhyb3dzIGl0LlxuZnVuY3Rpb24gZmFpbCgpIHtcbiAgdmFyIG1lc3NhZ2UgPSB0aGlzLm1lc3NhZ2UuYXBwbHkodGhpcywgYXJndW1lbnRzKVxuXG4gIG1lc3NhZ2UuZmF0YWwgPSB0cnVlXG5cbiAgdGhyb3cgbWVzc2FnZVxufVxuXG4vLyBJbmZvOiBjcmVhdGVzIGEgdm1lc3NhZ2UsIGFzc29jaWF0ZXMgaXQgd2l0aCB0aGUgZmlsZSwgYW5kIG1hcmtzIHRoZSBmYXRhbGl0eVxuLy8gYXMgbnVsbC5cbmZ1bmN0aW9uIGluZm8oKSB7XG4gIHZhciBtZXNzYWdlID0gdGhpcy5tZXNzYWdlLmFwcGx5KHRoaXMsIGFyZ3VtZW50cylcblxuICBtZXNzYWdlLmZhdGFsID0gbnVsbFxuXG4gIHJldHVybiBtZXNzYWdlXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vfile/lib/minpath.js":
/*!*******************************************!*\
  !*** ./node_modules/vfile/lib/minpath.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = __webpack_require__(/*! path */ \"path\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmZpbGUvbGliL21pbnBhdGguanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosd0RBQWdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy92ZmlsZS9saWIvbWlucGF0aC5qcz83NDk1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJ3BhdGgnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/minpath.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vfile/lib/minproc.js":
/*!*******************************************!*\
  !*** ./node_modules/vfile/lib/minproc.js ***!
  \*******************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = process\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmZpbGUvbGliL21pbnByb2MuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL3ZmaWxlL2xpYi9taW5wcm9jLmpzP2U4YzAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gcHJvY2Vzc1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/minproc.js\n");

/***/ })

};
;