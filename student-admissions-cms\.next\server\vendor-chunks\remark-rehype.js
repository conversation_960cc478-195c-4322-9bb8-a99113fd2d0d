"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark-rehype";
exports.ids = ["vendor-chunks/remark-rehype"];
exports.modules = {

/***/ "(ssr)/./node_modules/remark-rehype/index.js":
/*!*********************************************!*\
  !*** ./node_modules/remark-rehype/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar mdast2hast = __webpack_require__(/*! mdast-util-to-hast */ \"(ssr)/./node_modules/mdast-util-to-hast/index.js\")\n\nmodule.exports = remark2rehype\n\n// Attacher.\n// If a destination is given, runs the destination with the new hast tree\n// (bridge mode).\n// Without destination, returns the tree: further plugins run on that tree\n// (mutate mode).\nfunction remark2rehype(destination, options) {\n  if (destination && !destination.process) {\n    options = destination\n    destination = null\n  }\n\n  return destination ? bridge(destination, options) : mutate(options)\n}\n\n// Bridge mode.\n// Runs the destination with the new hast tree.\nfunction bridge(destination, options) {\n  return transformer\n\n  function transformer(node, file, next) {\n    destination.run(mdast2hast(node, options), file, done)\n\n    function done(error) {\n      next(error)\n    }\n  }\n}\n\n// Mutate-mode.\n// Further transformers run on the hast tree.\nfunction mutate(options) {\n  return transformer\n\n  function transformer(node) {\n    return mdast2hast(node, options)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVtYXJrLXJlaHlwZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixpQkFBaUIsbUJBQU8sQ0FBQyw0RUFBb0I7O0FBRTdDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9yZW1hcmstcmVoeXBlL2luZGV4LmpzP2IwZTMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBtZGFzdDJoYXN0ID0gcmVxdWlyZSgnbWRhc3QtdXRpbC10by1oYXN0JylcblxubW9kdWxlLmV4cG9ydHMgPSByZW1hcmsycmVoeXBlXG5cbi8vIEF0dGFjaGVyLlxuLy8gSWYgYSBkZXN0aW5hdGlvbiBpcyBnaXZlbiwgcnVucyB0aGUgZGVzdGluYXRpb24gd2l0aCB0aGUgbmV3IGhhc3QgdHJlZVxuLy8gKGJyaWRnZSBtb2RlKS5cbi8vIFdpdGhvdXQgZGVzdGluYXRpb24sIHJldHVybnMgdGhlIHRyZWU6IGZ1cnRoZXIgcGx1Z2lucyBydW4gb24gdGhhdCB0cmVlXG4vLyAobXV0YXRlIG1vZGUpLlxuZnVuY3Rpb24gcmVtYXJrMnJlaHlwZShkZXN0aW5hdGlvbiwgb3B0aW9ucykge1xuICBpZiAoZGVzdGluYXRpb24gJiYgIWRlc3RpbmF0aW9uLnByb2Nlc3MpIHtcbiAgICBvcHRpb25zID0gZGVzdGluYXRpb25cbiAgICBkZXN0aW5hdGlvbiA9IG51bGxcbiAgfVxuXG4gIHJldHVybiBkZXN0aW5hdGlvbiA/IGJyaWRnZShkZXN0aW5hdGlvbiwgb3B0aW9ucykgOiBtdXRhdGUob3B0aW9ucylcbn1cblxuLy8gQnJpZGdlIG1vZGUuXG4vLyBSdW5zIHRoZSBkZXN0aW5hdGlvbiB3aXRoIHRoZSBuZXcgaGFzdCB0cmVlLlxuZnVuY3Rpb24gYnJpZGdlKGRlc3RpbmF0aW9uLCBvcHRpb25zKSB7XG4gIHJldHVybiB0cmFuc2Zvcm1lclxuXG4gIGZ1bmN0aW9uIHRyYW5zZm9ybWVyKG5vZGUsIGZpbGUsIG5leHQpIHtcbiAgICBkZXN0aW5hdGlvbi5ydW4obWRhc3QyaGFzdChub2RlLCBvcHRpb25zKSwgZmlsZSwgZG9uZSlcblxuICAgIGZ1bmN0aW9uIGRvbmUoZXJyb3IpIHtcbiAgICAgIG5leHQoZXJyb3IpXG4gICAgfVxuICB9XG59XG5cbi8vIE11dGF0ZS1tb2RlLlxuLy8gRnVydGhlciB0cmFuc2Zvcm1lcnMgcnVuIG9uIHRoZSBoYXN0IHRyZWUuXG5mdW5jdGlvbiBtdXRhdGUob3B0aW9ucykge1xuICByZXR1cm4gdHJhbnNmb3JtZXJcblxuICBmdW5jdGlvbiB0cmFuc2Zvcm1lcihub2RlKSB7XG4gICAgcmV0dXJuIG1kYXN0Mmhhc3Qobm9kZSwgb3B0aW9ucylcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/remark-rehype/index.js\n");

/***/ })

};
;