"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unist-util-position";
exports.ids = ["vendor-chunks/unist-util-position"];
exports.modules = {

/***/ "(ssr)/./node_modules/unist-util-position/index.js":
/*!***************************************************!*\
  !*** ./node_modules/unist-util-position/index.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nvar start = factory('start')\nvar end = factory('end')\n\nmodule.exports = position\n\nposition.start = start\nposition.end = end\n\nfunction position(node) {\n  return {start: start(node), end: end(node)}\n}\n\nfunction factory(type) {\n  point.displayName = type\n\n  return point\n\n  function point(node) {\n    var point = (node && node.position && node.position[type]) || {}\n\n    return {\n      line: point.line || null,\n      column: point.column || null,\n      offset: isNaN(point.offset) ? null : point.offset\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW5pc3QtdXRpbC1wb3NpdGlvbi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxVQUFVO0FBQ1Y7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy91bmlzdC11dGlsLXBvc2l0aW9uL2luZGV4LmpzP2Q2OGIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBzdGFydCA9IGZhY3RvcnkoJ3N0YXJ0JylcbnZhciBlbmQgPSBmYWN0b3J5KCdlbmQnKVxuXG5tb2R1bGUuZXhwb3J0cyA9IHBvc2l0aW9uXG5cbnBvc2l0aW9uLnN0YXJ0ID0gc3RhcnRcbnBvc2l0aW9uLmVuZCA9IGVuZFxuXG5mdW5jdGlvbiBwb3NpdGlvbihub2RlKSB7XG4gIHJldHVybiB7c3RhcnQ6IHN0YXJ0KG5vZGUpLCBlbmQ6IGVuZChub2RlKX1cbn1cblxuZnVuY3Rpb24gZmFjdG9yeSh0eXBlKSB7XG4gIHBvaW50LmRpc3BsYXlOYW1lID0gdHlwZVxuXG4gIHJldHVybiBwb2ludFxuXG4gIGZ1bmN0aW9uIHBvaW50KG5vZGUpIHtcbiAgICB2YXIgcG9pbnQgPSAobm9kZSAmJiBub2RlLnBvc2l0aW9uICYmIG5vZGUucG9zaXRpb25bdHlwZV0pIHx8IHt9XG5cbiAgICByZXR1cm4ge1xuICAgICAgbGluZTogcG9pbnQubGluZSB8fCBudWxsLFxuICAgICAgY29sdW1uOiBwb2ludC5jb2x1bW4gfHwgbnVsbCxcbiAgICAgIG9mZnNldDogaXNOYU4ocG9pbnQub2Zmc2V0KSA/IG51bGwgOiBwb2ludC5vZmZzZXRcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unist-util-position/index.js\n");

/***/ })

};
;