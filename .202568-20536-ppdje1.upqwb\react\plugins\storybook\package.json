{"scripts": {"storybook": "start-storybook -p 6006 -s public", "build-storybook": "build-storybook -s public"}, "eslintConfig": {"overrides": [{"files": ["**/*.stories.*"], "rules": {"import/no-anonymous-default-export": "off"}}]}, "devDependencies": {"css-loader": "^5.0.1", "sass-loader": "^10.1.1", "storybook-addon-styled-component-theme": "^1.3.0", "@babel/core": "^7.12.10", "@storybook/preset-scss": "^1.0.3", "babel-loader": "^8.1.0", "style-loader": "^2.0.0", "@storybook/addon-actions": "^6.2.9", "@storybook/addon-essentials": "^6.2.9", "@storybook/addon-knobs": "^6.2.9", "@storybook/addon-links": "^6.2.9", "@storybook/node-logger": "^6.2.9", "@storybook/preset-create-react-app": "^3.1.7", "@storybook/react": "^6.2.9"}}