"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unist-util-generated";
exports.ids = ["vendor-chunks/unist-util-generated"];
exports.modules = {

/***/ "(ssr)/./node_modules/unist-util-generated/index.js":
/*!****************************************************!*\
  !*** ./node_modules/unist-util-generated/index.js ***!
  \****************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = generated\n\nfunction generated(node) {\n  return (\n    !node ||\n    !node.position ||\n    !node.position.start ||\n    !node.position.start.line ||\n    !node.position.start.column ||\n    !node.position.end ||\n    !node.position.end.line ||\n    !node.position.end.column\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW5pc3QtdXRpbC1nZW5lcmF0ZWQvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy91bmlzdC11dGlsLWdlbmVyYXRlZC9pbmRleC5qcz8yOWE2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IGdlbmVyYXRlZFxuXG5mdW5jdGlvbiBnZW5lcmF0ZWQobm9kZSkge1xuICByZXR1cm4gKFxuICAgICFub2RlIHx8XG4gICAgIW5vZGUucG9zaXRpb24gfHxcbiAgICAhbm9kZS5wb3NpdGlvbi5zdGFydCB8fFxuICAgICFub2RlLnBvc2l0aW9uLnN0YXJ0LmxpbmUgfHxcbiAgICAhbm9kZS5wb3NpdGlvbi5zdGFydC5jb2x1bW4gfHxcbiAgICAhbm9kZS5wb3NpdGlvbi5lbmQgfHxcbiAgICAhbm9kZS5wb3NpdGlvbi5lbmQubGluZSB8fFxuICAgICFub2RlLnBvc2l0aW9uLmVuZC5jb2x1bW5cbiAgKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unist-util-generated/index.js\n");

/***/ })

};
;