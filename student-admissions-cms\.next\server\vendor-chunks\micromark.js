"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark";
exports.ids = ["vendor-chunks/micromark"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark/dist/character/ascii-alpha.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark/dist/character/ascii-alpha.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar regexCheck = __webpack_require__(/*! ../util/regex-check.js */ \"(ssr)/./node_modules/micromark/dist/util/regex-check.js\")\n\nvar asciiAlpha = regexCheck(/[A-Za-z]/)\n\nmodule.exports = asciiAlpha\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL2FzY2lpLWFscGhhLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGlCQUFpQixtQkFBTyxDQUFDLHVGQUF3Qjs7QUFFakQ7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L2NoYXJhY3Rlci9hc2NpaS1hbHBoYS5qcz8yMmZiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgcmVnZXhDaGVjayA9IHJlcXVpcmUoJy4uL3V0aWwvcmVnZXgtY2hlY2suanMnKVxuXG52YXIgYXNjaWlBbHBoYSA9IHJlZ2V4Q2hlY2soL1tBLVphLXpdLylcblxubW9kdWxlLmV4cG9ydHMgPSBhc2NpaUFscGhhXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/ascii-alpha.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/character/ascii-alphanumeric.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark/dist/character/ascii-alphanumeric.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar regexCheck = __webpack_require__(/*! ../util/regex-check.js */ \"(ssr)/./node_modules/micromark/dist/util/regex-check.js\")\n\nvar asciiAlphanumeric = regexCheck(/[\\dA-Za-z]/)\n\nmodule.exports = asciiAlphanumeric\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL2FzY2lpLWFscGhhbnVtZXJpYy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixpQkFBaUIsbUJBQU8sQ0FBQyx1RkFBd0I7O0FBRWpEOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC9jaGFyYWN0ZXIvYXNjaWktYWxwaGFudW1lcmljLmpzPzYwYzQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciByZWdleENoZWNrID0gcmVxdWlyZSgnLi4vdXRpbC9yZWdleC1jaGVjay5qcycpXG5cbnZhciBhc2NpaUFscGhhbnVtZXJpYyA9IHJlZ2V4Q2hlY2soL1tcXGRBLVphLXpdLylcblxubW9kdWxlLmV4cG9ydHMgPSBhc2NpaUFscGhhbnVtZXJpY1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/ascii-alphanumeric.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/character/ascii-atext.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark/dist/character/ascii-atext.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar regexCheck = __webpack_require__(/*! ../util/regex-check.js */ \"(ssr)/./node_modules/micromark/dist/util/regex-check.js\")\n\nvar asciiAtext = regexCheck(/[#-'*+\\--9=?A-Z^-~]/)\n\nmodule.exports = asciiAtext\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL2FzY2lpLWF0ZXh0LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGlCQUFpQixtQkFBTyxDQUFDLHVGQUF3Qjs7QUFFakQ7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L2NoYXJhY3Rlci9hc2NpaS1hdGV4dC5qcz8xODY2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgcmVnZXhDaGVjayA9IHJlcXVpcmUoJy4uL3V0aWwvcmVnZXgtY2hlY2suanMnKVxuXG52YXIgYXNjaWlBdGV4dCA9IHJlZ2V4Q2hlY2soL1sjLScqK1xcLS05PT9BLVpeLX5dLylcblxubW9kdWxlLmV4cG9ydHMgPSBhc2NpaUF0ZXh0XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/ascii-atext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/character/ascii-control.js":
/*!****************************************************************!*\
  !*** ./node_modules/micromark/dist/character/ascii-control.js ***!
  \****************************************************************/
/***/ ((module) => {

eval("\n\n// Note: EOF is seen as ASCII control here, because `null < 32 == true`.\nfunction asciiControl(code) {\n  return (\n    // Special whitespace codes (which have negative values), C0 and Control\n    // character DEL\n    code < 32 || code === 127\n  )\n}\n\nmodule.exports = asciiControl\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL2FzY2lpLWNvbnRyb2wuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL2FzY2lpLWNvbnRyb2wuanM/YmQyMCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuLy8gTm90ZTogRU9GIGlzIHNlZW4gYXMgQVNDSUkgY29udHJvbCBoZXJlLCBiZWNhdXNlIGBudWxsIDwgMzIgPT0gdHJ1ZWAuXG5mdW5jdGlvbiBhc2NpaUNvbnRyb2woY29kZSkge1xuICByZXR1cm4gKFxuICAgIC8vIFNwZWNpYWwgd2hpdGVzcGFjZSBjb2RlcyAod2hpY2ggaGF2ZSBuZWdhdGl2ZSB2YWx1ZXMpLCBDMCBhbmQgQ29udHJvbFxuICAgIC8vIGNoYXJhY3RlciBERUxcbiAgICBjb2RlIDwgMzIgfHwgY29kZSA9PT0gMTI3XG4gIClcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBhc2NpaUNvbnRyb2xcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/ascii-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/character/ascii-digit.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark/dist/character/ascii-digit.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar regexCheck = __webpack_require__(/*! ../util/regex-check.js */ \"(ssr)/./node_modules/micromark/dist/util/regex-check.js\")\n\nvar asciiDigit = regexCheck(/\\d/)\n\nmodule.exports = asciiDigit\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL2FzY2lpLWRpZ2l0LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGlCQUFpQixtQkFBTyxDQUFDLHVGQUF3Qjs7QUFFakQ7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L2NoYXJhY3Rlci9hc2NpaS1kaWdpdC5qcz9kNDRlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgcmVnZXhDaGVjayA9IHJlcXVpcmUoJy4uL3V0aWwvcmVnZXgtY2hlY2suanMnKVxuXG52YXIgYXNjaWlEaWdpdCA9IHJlZ2V4Q2hlY2soL1xcZC8pXG5cbm1vZHVsZS5leHBvcnRzID0gYXNjaWlEaWdpdFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/ascii-digit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/character/ascii-hex-digit.js":
/*!******************************************************************!*\
  !*** ./node_modules/micromark/dist/character/ascii-hex-digit.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar regexCheck = __webpack_require__(/*! ../util/regex-check.js */ \"(ssr)/./node_modules/micromark/dist/util/regex-check.js\")\n\nvar asciiHexDigit = regexCheck(/[\\dA-Fa-f]/)\n\nmodule.exports = asciiHexDigit\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL2FzY2lpLWhleC1kaWdpdC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixpQkFBaUIsbUJBQU8sQ0FBQyx1RkFBd0I7O0FBRWpEOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC9jaGFyYWN0ZXIvYXNjaWktaGV4LWRpZ2l0LmpzPzYxZDciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciByZWdleENoZWNrID0gcmVxdWlyZSgnLi4vdXRpbC9yZWdleC1jaGVjay5qcycpXG5cbnZhciBhc2NpaUhleERpZ2l0ID0gcmVnZXhDaGVjaygvW1xcZEEtRmEtZl0vKVxuXG5tb2R1bGUuZXhwb3J0cyA9IGFzY2lpSGV4RGlnaXRcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/ascii-hex-digit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/character/ascii-punctuation.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark/dist/character/ascii-punctuation.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar regexCheck = __webpack_require__(/*! ../util/regex-check.js */ \"(ssr)/./node_modules/micromark/dist/util/regex-check.js\")\n\nvar asciiPunctuation = regexCheck(/[!-/:-@[-`{-~]/)\n\nmodule.exports = asciiPunctuation\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL2FzY2lpLXB1bmN0dWF0aW9uLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGlCQUFpQixtQkFBTyxDQUFDLHVGQUF3Qjs7QUFFakQsOENBQThDOztBQUU5QyIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL2FzY2lpLXB1bmN0dWF0aW9uLmpzPzQ3OWYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciByZWdleENoZWNrID0gcmVxdWlyZSgnLi4vdXRpbC9yZWdleC1jaGVjay5qcycpXG5cbnZhciBhc2NpaVB1bmN0dWF0aW9uID0gcmVnZXhDaGVjaygvWyEtLzotQFstYHstfl0vKVxuXG5tb2R1bGUuZXhwb3J0cyA9IGFzY2lpUHVuY3R1YXRpb25cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/ascii-punctuation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/character/markdown-line-ending-or-space.js":
/*!********************************************************************************!*\
  !*** ./node_modules/micromark/dist/character/markdown-line-ending-or-space.js ***!
  \********************************************************************************/
/***/ ((module) => {

eval("\n\nfunction markdownLineEndingOrSpace(code) {\n  return code < 0 || code === 32\n}\n\nmodule.exports = markdownLineEndingOrSpace\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL21hcmtkb3duLWxpbmUtZW5kaW5nLW9yLXNwYWNlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL21hcmtkb3duLWxpbmUtZW5kaW5nLW9yLXNwYWNlLmpzP2Q2ZmEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmZ1bmN0aW9uIG1hcmtkb3duTGluZUVuZGluZ09yU3BhY2UoY29kZSkge1xuICByZXR1cm4gY29kZSA8IDAgfHwgY29kZSA9PT0gMzJcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBtYXJrZG93bkxpbmVFbmRpbmdPclNwYWNlXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/markdown-line-ending-or-space.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark/dist/character/markdown-line-ending.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("\n\nfunction markdownLineEnding(code) {\n  return code < -2\n}\n\nmodule.exports = markdownLineEnding\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL21hcmtkb3duLWxpbmUtZW5kaW5nLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL21hcmtkb3duLWxpbmUtZW5kaW5nLmpzPzNkNjciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmZ1bmN0aW9uIG1hcmtkb3duTGluZUVuZGluZyhjb2RlKSB7XG4gIHJldHVybiBjb2RlIDwgLTJcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBtYXJrZG93bkxpbmVFbmRpbmdcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/character/markdown-space.js":
/*!*****************************************************************!*\
  !*** ./node_modules/micromark/dist/character/markdown-space.js ***!
  \*****************************************************************/
/***/ ((module) => {

eval("\n\nfunction markdownSpace(code) {\n  return code === -2 || code === -1 || code === 32\n}\n\nmodule.exports = markdownSpace\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL21hcmtkb3duLXNwYWNlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL21hcmtkb3duLXNwYWNlLmpzPzFmYjQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmZ1bmN0aW9uIG1hcmtkb3duU3BhY2UoY29kZSkge1xuICByZXR1cm4gY29kZSA9PT0gLTIgfHwgY29kZSA9PT0gLTEgfHwgY29kZSA9PT0gMzJcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBtYXJrZG93blNwYWNlXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/markdown-space.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/character/unicode-punctuation.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark/dist/character/unicode-punctuation.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar unicodePunctuationRegex = __webpack_require__(/*! ../constant/unicode-punctuation-regex.js */ \"(ssr)/./node_modules/micromark/dist/constant/unicode-punctuation-regex.js\")\nvar regexCheck = __webpack_require__(/*! ../util/regex-check.js */ \"(ssr)/./node_modules/micromark/dist/util/regex-check.js\")\n\n// In fact adds to the bundle size.\n\nvar unicodePunctuation = regexCheck(unicodePunctuationRegex)\n\nmodule.exports = unicodePunctuation\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL3VuaWNvZGUtcHVuY3R1YXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosOEJBQThCLG1CQUFPLENBQUMsMkhBQTBDO0FBQ2hGLGlCQUFpQixtQkFBTyxDQUFDLHVGQUF3Qjs7QUFFakQ7O0FBRUE7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L2NoYXJhY3Rlci91bmljb2RlLXB1bmN0dWF0aW9uLmpzP2ZjMDAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciB1bmljb2RlUHVuY3R1YXRpb25SZWdleCA9IHJlcXVpcmUoJy4uL2NvbnN0YW50L3VuaWNvZGUtcHVuY3R1YXRpb24tcmVnZXguanMnKVxudmFyIHJlZ2V4Q2hlY2sgPSByZXF1aXJlKCcuLi91dGlsL3JlZ2V4LWNoZWNrLmpzJylcblxuLy8gSW4gZmFjdCBhZGRzIHRvIHRoZSBidW5kbGUgc2l6ZS5cblxudmFyIHVuaWNvZGVQdW5jdHVhdGlvbiA9IHJlZ2V4Q2hlY2sodW5pY29kZVB1bmN0dWF0aW9uUmVnZXgpXG5cbm1vZHVsZS5leHBvcnRzID0gdW5pY29kZVB1bmN0dWF0aW9uXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/unicode-punctuation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/character/unicode-whitespace.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark/dist/character/unicode-whitespace.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar regexCheck = __webpack_require__(/*! ../util/regex-check.js */ \"(ssr)/./node_modules/micromark/dist/util/regex-check.js\")\n\nvar unicodeWhitespace = regexCheck(/\\s/)\n\nmodule.exports = unicodeWhitespace\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY2hhcmFjdGVyL3VuaWNvZGUtd2hpdGVzcGFjZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixpQkFBaUIsbUJBQU8sQ0FBQyx1RkFBd0I7O0FBRWpEOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC9jaGFyYWN0ZXIvdW5pY29kZS13aGl0ZXNwYWNlLmpzPzdhNWIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciByZWdleENoZWNrID0gcmVxdWlyZSgnLi4vdXRpbC9yZWdleC1jaGVjay5qcycpXG5cbnZhciB1bmljb2RlV2hpdGVzcGFjZSA9IHJlZ2V4Q2hlY2soL1xccy8pXG5cbm1vZHVsZS5leHBvcnRzID0gdW5pY29kZVdoaXRlc3BhY2VcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/character/unicode-whitespace.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/constant/assign.js":
/*!********************************************************!*\
  !*** ./node_modules/micromark/dist/constant/assign.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\n\nvar assign = Object.assign\n\nmodule.exports = assign\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY29uc3RhbnQvYXNzaWduLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC9jb25zdGFudC9hc3NpZ24uanM/NDc2OCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIGFzc2lnbiA9IE9iamVjdC5hc3NpZ25cblxubW9kdWxlLmV4cG9ydHMgPSBhc3NpZ25cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/constant/assign.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/constant/from-char-code.js":
/*!****************************************************************!*\
  !*** ./node_modules/micromark/dist/constant/from-char-code.js ***!
  \****************************************************************/
/***/ ((module) => {

eval("\n\nvar fromCharCode = String.fromCharCode\n\nmodule.exports = fromCharCode\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY29uc3RhbnQvZnJvbS1jaGFyLWNvZGUuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L2NvbnN0YW50L2Zyb20tY2hhci1jb2RlLmpzPzUzNDYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBmcm9tQ2hhckNvZGUgPSBTdHJpbmcuZnJvbUNoYXJDb2RlXG5cbm1vZHVsZS5leHBvcnRzID0gZnJvbUNoYXJDb2RlXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/constant/from-char-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/constant/has-own-property.js":
/*!******************************************************************!*\
  !*** ./node_modules/micromark/dist/constant/has-own-property.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("\n\nvar own = {}.hasOwnProperty\n\nmodule.exports = own\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY29uc3RhbnQvaGFzLW93bi1wcm9wZXJ0eS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixZQUFZOztBQUVaIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC9jb25zdGFudC9oYXMtb3duLXByb3BlcnR5LmpzPzdmYzciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBvd24gPSB7fS5oYXNPd25Qcm9wZXJ0eVxuXG5tb2R1bGUuZXhwb3J0cyA9IG93blxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/constant/has-own-property.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/constant/html-block-names.js":
/*!******************************************************************!*\
  !*** ./node_modules/micromark/dist/constant/html-block-names.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("\n\n// This module is copied from <https://spec.commonmark.org/0.29/#html-blocks>.\nvar basics = [\n  'address',\n  'article',\n  'aside',\n  'base',\n  'basefont',\n  'blockquote',\n  'body',\n  'caption',\n  'center',\n  'col',\n  'colgroup',\n  'dd',\n  'details',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'frame',\n  'frameset',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hr',\n  'html',\n  'iframe',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'menu',\n  'menuitem',\n  'nav',\n  'noframes',\n  'ol',\n  'optgroup',\n  'option',\n  'p',\n  'param',\n  'section',\n  'source',\n  'summary',\n  'table',\n  'tbody',\n  'td',\n  'tfoot',\n  'th',\n  'thead',\n  'title',\n  'tr',\n  'track',\n  'ul'\n]\n\nmodule.exports = basics\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY29uc3RhbnQvaHRtbC1ibG9jay1uYW1lcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC9jb25zdGFudC9odG1sLWJsb2NrLW5hbWVzLmpzPzRiZDAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbi8vIFRoaXMgbW9kdWxlIGlzIGNvcGllZCBmcm9tIDxodHRwczovL3NwZWMuY29tbW9ubWFyay5vcmcvMC4yOS8jaHRtbC1ibG9ja3M+LlxudmFyIGJhc2ljcyA9IFtcbiAgJ2FkZHJlc3MnLFxuICAnYXJ0aWNsZScsXG4gICdhc2lkZScsXG4gICdiYXNlJyxcbiAgJ2Jhc2Vmb250JyxcbiAgJ2Jsb2NrcXVvdGUnLFxuICAnYm9keScsXG4gICdjYXB0aW9uJyxcbiAgJ2NlbnRlcicsXG4gICdjb2wnLFxuICAnY29sZ3JvdXAnLFxuICAnZGQnLFxuICAnZGV0YWlscycsXG4gICdkaWFsb2cnLFxuICAnZGlyJyxcbiAgJ2RpdicsXG4gICdkbCcsXG4gICdkdCcsXG4gICdmaWVsZHNldCcsXG4gICdmaWdjYXB0aW9uJyxcbiAgJ2ZpZ3VyZScsXG4gICdmb290ZXInLFxuICAnZm9ybScsXG4gICdmcmFtZScsXG4gICdmcmFtZXNldCcsXG4gICdoMScsXG4gICdoMicsXG4gICdoMycsXG4gICdoNCcsXG4gICdoNScsXG4gICdoNicsXG4gICdoZWFkJyxcbiAgJ2hlYWRlcicsXG4gICdocicsXG4gICdodG1sJyxcbiAgJ2lmcmFtZScsXG4gICdsZWdlbmQnLFxuICAnbGknLFxuICAnbGluaycsXG4gICdtYWluJyxcbiAgJ21lbnUnLFxuICAnbWVudWl0ZW0nLFxuICAnbmF2JyxcbiAgJ25vZnJhbWVzJyxcbiAgJ29sJyxcbiAgJ29wdGdyb3VwJyxcbiAgJ29wdGlvbicsXG4gICdwJyxcbiAgJ3BhcmFtJyxcbiAgJ3NlY3Rpb24nLFxuICAnc291cmNlJyxcbiAgJ3N1bW1hcnknLFxuICAndGFibGUnLFxuICAndGJvZHknLFxuICAndGQnLFxuICAndGZvb3QnLFxuICAndGgnLFxuICAndGhlYWQnLFxuICAndGl0bGUnLFxuICAndHInLFxuICAndHJhY2snLFxuICAndWwnXG5dXG5cbm1vZHVsZS5leHBvcnRzID0gYmFzaWNzXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/constant/html-block-names.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/constant/html-raw-names.js":
/*!****************************************************************!*\
  !*** ./node_modules/micromark/dist/constant/html-raw-names.js ***!
  \****************************************************************/
/***/ ((module) => {

eval("\n\n// This module is copied from <https://spec.commonmark.org/0.29/#html-blocks>.\nvar raws = ['pre', 'script', 'style', 'textarea']\n\nmodule.exports = raws\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY29uc3RhbnQvaHRtbC1yYXctbmFtZXMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY29uc3RhbnQvaHRtbC1yYXctbmFtZXMuanM/MjRkOCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuLy8gVGhpcyBtb2R1bGUgaXMgY29waWVkIGZyb20gPGh0dHBzOi8vc3BlYy5jb21tb25tYXJrLm9yZy8wLjI5LyNodG1sLWJsb2Nrcz4uXG52YXIgcmF3cyA9IFsncHJlJywgJ3NjcmlwdCcsICdzdHlsZScsICd0ZXh0YXJlYSddXG5cbm1vZHVsZS5leHBvcnRzID0gcmF3c1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/constant/html-raw-names.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/constant/splice.js":
/*!********************************************************!*\
  !*** ./node_modules/micromark/dist/constant/splice.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\n\nvar splice = [].splice\n\nmodule.exports = splice\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvY29uc3RhbnQvc3BsaWNlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC9jb25zdGFudC9zcGxpY2UuanM/M2Y5NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIHNwbGljZSA9IFtdLnNwbGljZVxuXG5tb2R1bGUuZXhwb3J0cyA9IHNwbGljZVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/constant/splice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/constant/unicode-punctuation-regex.js":
/*!***************************************************************************!*\
  !*** ./node_modules/micromark/dist/constant/unicode-punctuation-regex.js ***!
  \***************************************************************************/
/***/ ((module) => {

eval("\n\n// This module is generated by `script/`.\n//\n// CommonMark handles attention (emphasis, strong) markers based on what comes\n// before or after them.\n// One such difference is if those characters are Unicode punctuation.\n// This script is generated from the Unicode data.\nvar unicodePunctuation = /[!-\\/:-@\\[-`\\{-~\\xA1\\xA7\\xAB\\xB6\\xB7\\xBB\\xBF\\u037E\\u0387\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061E\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u09FD\\u0A76\\u0AF0\\u0C77\\u0C84\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F14\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1360-\\u1368\\u1400\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CC0-\\u1CC7\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u2308-\\u230B\\u2329\\u232A\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30-\\u2E4F\\u2E52\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA8FC\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uAAF0\\uAAF1\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]/\n\nmodule.exports = unicodePunctuation\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/constant/unicode-punctuation-regex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/constructs.js":
/*!***************************************************!*\
  !*** ./node_modules/micromark/dist/constructs.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({value: true}))\n\nvar text$1 = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dist/initialize/text.js\")\nvar attention = __webpack_require__(/*! ./tokenize/attention.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/attention.js\")\nvar autolink = __webpack_require__(/*! ./tokenize/autolink.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/autolink.js\")\nvar blockQuote = __webpack_require__(/*! ./tokenize/block-quote.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/block-quote.js\")\nvar characterEscape = __webpack_require__(/*! ./tokenize/character-escape.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/character-escape.js\")\nvar characterReference = __webpack_require__(/*! ./tokenize/character-reference.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/character-reference.js\")\nvar codeFenced = __webpack_require__(/*! ./tokenize/code-fenced.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/code-fenced.js\")\nvar codeIndented = __webpack_require__(/*! ./tokenize/code-indented.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/code-indented.js\")\nvar codeText = __webpack_require__(/*! ./tokenize/code-text.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/code-text.js\")\nvar definition = __webpack_require__(/*! ./tokenize/definition.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/definition.js\")\nvar hardBreakEscape = __webpack_require__(/*! ./tokenize/hard-break-escape.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/hard-break-escape.js\")\nvar headingAtx = __webpack_require__(/*! ./tokenize/heading-atx.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/heading-atx.js\")\nvar htmlFlow = __webpack_require__(/*! ./tokenize/html-flow.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/html-flow.js\")\nvar htmlText = __webpack_require__(/*! ./tokenize/html-text.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/html-text.js\")\nvar labelEnd = __webpack_require__(/*! ./tokenize/label-end.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/label-end.js\")\nvar labelStartImage = __webpack_require__(/*! ./tokenize/label-start-image.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/label-start-image.js\")\nvar labelStartLink = __webpack_require__(/*! ./tokenize/label-start-link.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/label-start-link.js\")\nvar lineEnding = __webpack_require__(/*! ./tokenize/line-ending.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/line-ending.js\")\nvar list = __webpack_require__(/*! ./tokenize/list.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/list.js\")\nvar setextUnderline = __webpack_require__(/*! ./tokenize/setext-underline.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/setext-underline.js\")\nvar thematicBreak = __webpack_require__(/*! ./tokenize/thematic-break.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/thematic-break.js\")\n\nvar document = {\n  42: list,\n  // Asterisk\n  43: list,\n  // Plus sign\n  45: list,\n  // Dash\n  48: list,\n  // 0\n  49: list,\n  // 1\n  50: list,\n  // 2\n  51: list,\n  // 3\n  52: list,\n  // 4\n  53: list,\n  // 5\n  54: list,\n  // 6\n  55: list,\n  // 7\n  56: list,\n  // 8\n  57: list,\n  // 9\n  62: blockQuote // Greater than\n}\nvar contentInitial = {\n  91: definition // Left square bracket\n}\nvar flowInitial = {\n  '-2': codeIndented,\n  // Horizontal tab\n  '-1': codeIndented,\n  // Virtual space\n  32: codeIndented // Space\n}\nvar flow = {\n  35: headingAtx,\n  // Number sign\n  42: thematicBreak,\n  // Asterisk\n  45: [setextUnderline, thematicBreak],\n  // Dash\n  60: htmlFlow,\n  // Less than\n  61: setextUnderline,\n  // Equals to\n  95: thematicBreak,\n  // Underscore\n  96: codeFenced,\n  // Grave accent\n  126: codeFenced // Tilde\n}\nvar string = {\n  38: characterReference,\n  // Ampersand\n  92: characterEscape // Backslash\n}\nvar text = {\n  '-5': lineEnding,\n  // Carriage return\n  '-4': lineEnding,\n  // Line feed\n  '-3': lineEnding,\n  // Carriage return + line feed\n  33: labelStartImage,\n  // Exclamation mark\n  38: characterReference,\n  // Ampersand\n  42: attention,\n  // Asterisk\n  60: [autolink, htmlText],\n  // Less than\n  91: labelStartLink,\n  // Left square bracket\n  92: [hardBreakEscape, characterEscape],\n  // Backslash\n  93: labelEnd,\n  // Right square bracket\n  95: attention,\n  // Underscore\n  96: codeText // Grave accent\n}\nvar insideSpan = {\n  null: [attention, text$1.resolver]\n}\nvar disable = {\n  null: []\n}\n\nexports.contentInitial = contentInitial\nexports.disable = disable\nexports.document = document\nexports.flow = flow\nexports.flowInitial = flowInitial\nexports.insideSpan = insideSpan\nexports.string = string\nexports.text = text\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/constructs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/initialize/content.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dist/initialize/content.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({value: true}))\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar factorySpace = __webpack_require__(/*! ../tokenize/factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nvar tokenize = initializeContent\n\nfunction initializeContent(effects) {\n  var contentStart = effects.attempt(\n    this.parser.constructs.contentInitial,\n    afterContentStartConstruct,\n    paragraphInitial\n  )\n  var previous\n  return contentStart\n\n  function afterContentStartConstruct(code) {\n    if (code === null) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter('lineEnding')\n    effects.consume(code)\n    effects.exit('lineEnding')\n    return factorySpace(effects, contentStart, 'linePrefix')\n  }\n\n  function paragraphInitial(code) {\n    effects.enter('paragraph')\n    return lineStart(code)\n  }\n\n  function lineStart(code) {\n    var token = effects.enter('chunkText', {\n      contentType: 'text',\n      previous: previous\n    })\n\n    if (previous) {\n      previous.next = token\n    }\n\n    previous = token\n    return data(code)\n  }\n\n  function data(code) {\n    if (code === null) {\n      effects.exit('chunkText')\n      effects.exit('paragraph')\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      effects.exit('chunkText')\n      return lineStart\n    } // Data.\n\n    effects.consume(code)\n    return data\n  }\n}\n\nexports.tokenize = tokenize\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/initialize/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/initialize/document.js":
/*!************************************************************!*\
  !*** ./node_modules/micromark/dist/initialize/document.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({value: true}))\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar factorySpace = __webpack_require__(/*! ../tokenize/factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\nvar partialBlankLine = __webpack_require__(/*! ../tokenize/partial-blank-line.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/partial-blank-line.js\")\n\nvar tokenize = initializeDocument\nvar containerConstruct = {\n  tokenize: tokenizeContainer\n}\nvar lazyFlowConstruct = {\n  tokenize: tokenizeLazyFlow\n}\n\nfunction initializeDocument(effects) {\n  var self = this\n  var stack = []\n  var continued = 0\n  var inspectConstruct = {\n    tokenize: tokenizeInspect,\n    partial: true\n  }\n  var inspectResult\n  var childFlow\n  var childToken\n  return start\n\n  function start(code) {\n    if (continued < stack.length) {\n      self.containerState = stack[continued][1]\n      return effects.attempt(\n        stack[continued][0].continuation,\n        documentContinue,\n        documentContinued\n      )(code)\n    }\n\n    return documentContinued(code)\n  }\n\n  function documentContinue(code) {\n    continued++\n    return start(code)\n  }\n\n  function documentContinued(code) {\n    // If we’re in a concrete construct (such as when expecting another line of\n    // HTML, or we resulted in lazy content), we can immediately start flow.\n    if (inspectResult && inspectResult.flowContinue) {\n      return flowStart(code)\n    }\n\n    self.interrupt =\n      childFlow &&\n      childFlow.currentConstruct &&\n      childFlow.currentConstruct.interruptible\n    self.containerState = {}\n    return effects.attempt(\n      containerConstruct,\n      containerContinue,\n      flowStart\n    )(code)\n  }\n\n  function containerContinue(code) {\n    stack.push([self.currentConstruct, self.containerState])\n    self.containerState = undefined\n    return documentContinued(code)\n  }\n\n  function flowStart(code) {\n    if (code === null) {\n      exitContainers(0, true)\n      effects.consume(code)\n      return\n    }\n\n    childFlow = childFlow || self.parser.flow(self.now())\n    effects.enter('chunkFlow', {\n      contentType: 'flow',\n      previous: childToken,\n      _tokenizer: childFlow\n    })\n    return flowContinue(code)\n  }\n\n  function flowContinue(code) {\n    if (code === null) {\n      continueFlow(effects.exit('chunkFlow'))\n      return flowStart(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      continueFlow(effects.exit('chunkFlow'))\n      return effects.check(inspectConstruct, documentAfterPeek)\n    }\n\n    effects.consume(code)\n    return flowContinue\n  }\n\n  function documentAfterPeek(code) {\n    exitContainers(\n      inspectResult.continued,\n      inspectResult && inspectResult.flowEnd\n    )\n    continued = 0\n    return start(code)\n  }\n\n  function continueFlow(token) {\n    if (childToken) childToken.next = token\n    childToken = token\n    childFlow.lazy = inspectResult && inspectResult.lazy\n    childFlow.defineSkip(token.start)\n    childFlow.write(self.sliceStream(token))\n  }\n\n  function exitContainers(size, end) {\n    var index = stack.length // Close the flow.\n\n    if (childFlow && end) {\n      childFlow.write([null])\n      childToken = childFlow = undefined\n    } // Exit open containers.\n\n    while (index-- > size) {\n      self.containerState = stack[index][1]\n      stack[index][0].exit.call(self, effects)\n    }\n\n    stack.length = size\n  }\n\n  function tokenizeInspect(effects, ok) {\n    var subcontinued = 0\n    inspectResult = {}\n    return inspectStart\n\n    function inspectStart(code) {\n      if (subcontinued < stack.length) {\n        self.containerState = stack[subcontinued][1]\n        return effects.attempt(\n          stack[subcontinued][0].continuation,\n          inspectContinue,\n          inspectLess\n        )(code)\n      } // If we’re continued but in a concrete flow, we can’t have more\n      // containers.\n\n      if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n        inspectResult.flowContinue = true\n        return inspectDone(code)\n      }\n\n      self.interrupt =\n        childFlow.currentConstruct && childFlow.currentConstruct.interruptible\n      self.containerState = {}\n      return effects.attempt(\n        containerConstruct,\n        inspectFlowEnd,\n        inspectDone\n      )(code)\n    }\n\n    function inspectContinue(code) {\n      subcontinued++\n      return self.containerState._closeFlow\n        ? inspectFlowEnd(code)\n        : inspectStart(code)\n    }\n\n    function inspectLess(code) {\n      if (childFlow.currentConstruct && childFlow.currentConstruct.lazy) {\n        // Maybe another container?\n        self.containerState = {}\n        return effects.attempt(\n          containerConstruct,\n          inspectFlowEnd, // Maybe flow, or a blank line?\n          effects.attempt(\n            lazyFlowConstruct,\n            inspectFlowEnd,\n            effects.check(partialBlankLine, inspectFlowEnd, inspectLazy)\n          )\n        )(code)\n      } // Otherwise we’re interrupting.\n\n      return inspectFlowEnd(code)\n    }\n\n    function inspectLazy(code) {\n      // Act as if all containers are continued.\n      subcontinued = stack.length\n      inspectResult.lazy = true\n      inspectResult.flowContinue = true\n      return inspectDone(code)\n    } // We’re done with flow if we have more containers, or an interruption.\n\n    function inspectFlowEnd(code) {\n      inspectResult.flowEnd = true\n      return inspectDone(code)\n    }\n\n    function inspectDone(code) {\n      inspectResult.continued = subcontinued\n      self.interrupt = self.containerState = undefined\n      return ok(code)\n    }\n  }\n}\n\nfunction tokenizeContainer(effects, ok, nok) {\n  return factorySpace(\n    effects,\n    effects.attempt(this.parser.constructs.document, ok, nok),\n    'linePrefix',\n    this.parser.constructs.disable.null.indexOf('codeIndented') > -1\n      ? undefined\n      : 4\n  )\n}\n\nfunction tokenizeLazyFlow(effects, ok, nok) {\n  return factorySpace(\n    effects,\n    effects.lazy(this.parser.constructs.flow, ok, nok),\n    'linePrefix',\n    this.parser.constructs.disable.null.indexOf('codeIndented') > -1\n      ? undefined\n      : 4\n  )\n}\n\nexports.tokenize = tokenize\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/initialize/document.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/initialize/flow.js":
/*!********************************************************!*\
  !*** ./node_modules/micromark/dist/initialize/flow.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({value: true}))\n\nvar content = __webpack_require__(/*! ../tokenize/content.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/content.js\")\nvar factorySpace = __webpack_require__(/*! ../tokenize/factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\nvar partialBlankLine = __webpack_require__(/*! ../tokenize/partial-blank-line.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/partial-blank-line.js\")\n\nvar tokenize = initializeFlow\n\nfunction initializeFlow(effects) {\n  var self = this\n  var initial = effects.attempt(\n    // Try to parse a blank line.\n    partialBlankLine,\n    atBlankEnding, // Try to parse initial flow (essentially, only code).\n    effects.attempt(\n      this.parser.constructs.flowInitial,\n      afterConstruct,\n      factorySpace(\n        effects,\n        effects.attempt(\n          this.parser.constructs.flow,\n          afterConstruct,\n          effects.attempt(content, afterConstruct)\n        ),\n        'linePrefix'\n      )\n    )\n  )\n  return initial\n\n  function atBlankEnding(code) {\n    if (code === null) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter('lineEndingBlank')\n    effects.consume(code)\n    effects.exit('lineEndingBlank')\n    self.currentConstruct = undefined\n    return initial\n  }\n\n  function afterConstruct(code) {\n    if (code === null) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter('lineEnding')\n    effects.consume(code)\n    effects.exit('lineEnding')\n    self.currentConstruct = undefined\n    return initial\n  }\n}\n\nexports.tokenize = tokenize\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/initialize/flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/initialize/text.js":
/*!********************************************************!*\
  !*** ./node_modules/micromark/dist/initialize/text.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({value: true}))\n\nvar assign = __webpack_require__(/*! ../constant/assign.js */ \"(ssr)/./node_modules/micromark/dist/constant/assign.js\")\nvar shallow = __webpack_require__(/*! ../util/shallow.js */ \"(ssr)/./node_modules/micromark/dist/util/shallow.js\")\n\nvar text = initializeFactory('text')\nvar string = initializeFactory('string')\nvar resolver = {\n  resolveAll: createResolver()\n}\n\nfunction initializeFactory(field) {\n  return {\n    tokenize: initializeText,\n    resolveAll: createResolver(\n      field === 'text' ? resolveAllLineSuffixes : undefined\n    )\n  }\n\n  function initializeText(effects) {\n    var self = this\n    var constructs = this.parser.constructs[field]\n    var text = effects.attempt(constructs, start, notText)\n    return start\n\n    function start(code) {\n      return atBreak(code) ? text(code) : notText(code)\n    }\n\n    function notText(code) {\n      if (code === null) {\n        effects.consume(code)\n        return\n      }\n\n      effects.enter('data')\n      effects.consume(code)\n      return data\n    }\n\n    function data(code) {\n      if (atBreak(code)) {\n        effects.exit('data')\n        return text(code)\n      } // Data.\n\n      effects.consume(code)\n      return data\n    }\n\n    function atBreak(code) {\n      var list = constructs[code]\n      var index = -1\n\n      if (code === null) {\n        return true\n      }\n\n      if (list) {\n        while (++index < list.length) {\n          if (\n            !list[index].previous ||\n            list[index].previous.call(self, self.previous)\n          ) {\n            return true\n          }\n        }\n      }\n    }\n  }\n}\n\nfunction createResolver(extraResolver) {\n  return resolveAllText\n\n  function resolveAllText(events, context) {\n    var index = -1\n    var enter // A rather boring computation (to merge adjacent `data` events) which\n    // improves mm performance by 29%.\n\n    while (++index <= events.length) {\n      if (enter === undefined) {\n        if (events[index] && events[index][1].type === 'data') {\n          enter = index\n          index++\n        }\n      } else if (!events[index] || events[index][1].type !== 'data') {\n        // Don’t do anything if there is one data token.\n        if (index !== enter + 2) {\n          events[enter][1].end = events[index - 1][1].end\n          events.splice(enter + 2, index - enter - 2)\n          index = enter + 2\n        }\n\n        enter = undefined\n      }\n    }\n\n    return extraResolver ? extraResolver(events, context) : events\n  }\n} // A rather ugly set of instructions which again looks at chunks in the input\n// stream.\n// The reason to do this here is that it is *much* faster to parse in reverse.\n// And that we can’t hook into `null` to split the line suffix before an EOF.\n// To do: figure out if we can make this into a clean utility, or even in core.\n// As it will be useful for GFMs literal autolink extension (and maybe even\n// tables?)\n\nfunction resolveAllLineSuffixes(events, context) {\n  var eventIndex = -1\n  var chunks\n  var data\n  var chunk\n  var index\n  var bufferIndex\n  var size\n  var tabs\n  var token\n\n  while (++eventIndex <= events.length) {\n    if (\n      (eventIndex === events.length ||\n        events[eventIndex][1].type === 'lineEnding') &&\n      events[eventIndex - 1][1].type === 'data'\n    ) {\n      data = events[eventIndex - 1][1]\n      chunks = context.sliceStream(data)\n      index = chunks.length\n      bufferIndex = -1\n      size = 0\n      tabs = undefined\n\n      while (index--) {\n        chunk = chunks[index]\n\n        if (typeof chunk === 'string') {\n          bufferIndex = chunk.length\n\n          while (chunk.charCodeAt(bufferIndex - 1) === 32) {\n            size++\n            bufferIndex--\n          }\n\n          if (bufferIndex) break\n          bufferIndex = -1\n        } // Number\n        else if (chunk === -2) {\n          tabs = true\n          size++\n        } else if (chunk === -1);\n        else {\n          // Replacement character, exit.\n          index++\n          break\n        }\n      }\n\n      if (size) {\n        token = {\n          type:\n            eventIndex === events.length || tabs || size < 2\n              ? 'lineSuffix'\n              : 'hardBreakTrailing',\n          start: {\n            line: data.end.line,\n            column: data.end.column - size,\n            offset: data.end.offset - size,\n            _index: data.start._index + index,\n            _bufferIndex: index\n              ? bufferIndex\n              : data.start._bufferIndex + bufferIndex\n          },\n          end: shallow(data.end)\n        }\n        data.end = shallow(token.start)\n\n        if (data.start.offset === data.end.offset) {\n          assign(data, token)\n        } else {\n          events.splice(\n            eventIndex,\n            0,\n            ['enter', token, context],\n            ['exit', token, context]\n          )\n          eventIndex += 2\n        }\n      }\n\n      eventIndex++\n    }\n  }\n\n  return events\n}\n\nexports.resolver = resolver\nexports.string = string\nexports.text = text\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/initialize/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/parse.js":
/*!**********************************************!*\
  !*** ./node_modules/micromark/dist/parse.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar content = __webpack_require__(/*! ./initialize/content.js */ \"(ssr)/./node_modules/micromark/dist/initialize/content.js\")\nvar document = __webpack_require__(/*! ./initialize/document.js */ \"(ssr)/./node_modules/micromark/dist/initialize/document.js\")\nvar flow = __webpack_require__(/*! ./initialize/flow.js */ \"(ssr)/./node_modules/micromark/dist/initialize/flow.js\")\nvar text = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dist/initialize/text.js\")\nvar combineExtensions = __webpack_require__(/*! ./util/combine-extensions.js */ \"(ssr)/./node_modules/micromark/dist/util/combine-extensions.js\")\nvar createTokenizer = __webpack_require__(/*! ./util/create-tokenizer.js */ \"(ssr)/./node_modules/micromark/dist/util/create-tokenizer.js\")\nvar miniflat = __webpack_require__(/*! ./util/miniflat.js */ \"(ssr)/./node_modules/micromark/dist/util/miniflat.js\")\nvar constructs = __webpack_require__(/*! ./constructs.js */ \"(ssr)/./node_modules/micromark/dist/constructs.js\")\n\nfunction parse(options) {\n  var settings = options || {}\n  var parser = {\n    defined: [],\n    constructs: combineExtensions(\n      [constructs].concat(miniflat(settings.extensions))\n    ),\n    content: create(content),\n    document: create(document),\n    flow: create(flow),\n    string: create(text.string),\n    text: create(text.text)\n  }\n  return parser\n\n  function create(initializer) {\n    return creator\n\n    function creator(from) {\n      return createTokenizer(parser, initializer, from)\n    }\n  }\n}\n\nmodule.exports = parse\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/postprocess.js":
/*!****************************************************!*\
  !*** ./node_modules/micromark/dist/postprocess.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar subtokenize = __webpack_require__(/*! ./util/subtokenize.js */ \"(ssr)/./node_modules/micromark/dist/util/subtokenize.js\")\n\nfunction postprocess(events) {\n  while (!subtokenize(events)) {\n    // Empty\n  }\n\n  return events\n}\n\nmodule.exports = postprocess\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvcG9zdHByb2Nlc3MuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosa0JBQWtCLG1CQUFPLENBQUMsc0ZBQXVCOztBQUVqRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC9wb3N0cHJvY2Vzcy5qcz9iM2JmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgc3VidG9rZW5pemUgPSByZXF1aXJlKCcuL3V0aWwvc3VidG9rZW5pemUuanMnKVxuXG5mdW5jdGlvbiBwb3N0cHJvY2VzcyhldmVudHMpIHtcbiAgd2hpbGUgKCFzdWJ0b2tlbml6ZShldmVudHMpKSB7XG4gICAgLy8gRW1wdHlcbiAgfVxuXG4gIHJldHVybiBldmVudHNcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBwb3N0cHJvY2Vzc1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/postprocess.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/preprocess.js":
/*!***************************************************!*\
  !*** ./node_modules/micromark/dist/preprocess.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nvar search = /[\\0\\t\\n\\r]/g\n\nfunction preprocess() {\n  var start = true\n  var column = 1\n  var buffer = ''\n  var atCarriageReturn\n  return preprocessor\n\n  function preprocessor(value, encoding, end) {\n    var chunks = []\n    var match\n    var next\n    var startPosition\n    var endPosition\n    var code\n    value = buffer + value.toString(encoding)\n    startPosition = 0\n    buffer = ''\n\n    if (start) {\n      if (value.charCodeAt(0) === 65279) {\n        startPosition++\n      }\n\n      start = undefined\n    }\n\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition\n      match = search.exec(value)\n      endPosition = match ? match.index : value.length\n      code = value.charCodeAt(endPosition)\n\n      if (!match) {\n        buffer = value.slice(startPosition)\n        break\n      }\n\n      if (code === 10 && startPosition === endPosition && atCarriageReturn) {\n        chunks.push(-3)\n        atCarriageReturn = undefined\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(-5)\n          atCarriageReturn = undefined\n        }\n\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition))\n          column += endPosition - startPosition\n        }\n\n        if (code === 0) {\n          chunks.push(65533)\n          column++\n        } else if (code === 9) {\n          next = Math.ceil(column / 4) * 4\n          chunks.push(-2)\n\n          while (column++ < next) chunks.push(-1)\n        } else if (code === 10) {\n          chunks.push(-4)\n          column = 1\n        } // Must be carriage return.\n        else {\n          atCarriageReturn = true\n          column = 1\n        }\n      }\n\n      startPosition = endPosition + 1\n    }\n\n    if (end) {\n      if (atCarriageReturn) chunks.push(-5)\n      if (buffer) chunks.push(buffer)\n      chunks.push(null)\n    }\n\n    return chunks\n  }\n}\n\nmodule.exports = preprocess\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/preprocess.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/attention.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/attention.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar chunkedPush = __webpack_require__(/*! ../util/chunked-push.js */ \"(ssr)/./node_modules/micromark/dist/util/chunked-push.js\")\nvar chunkedSplice = __webpack_require__(/*! ../util/chunked-splice.js */ \"(ssr)/./node_modules/micromark/dist/util/chunked-splice.js\")\nvar classifyCharacter = __webpack_require__(/*! ../util/classify-character.js */ \"(ssr)/./node_modules/micromark/dist/util/classify-character.js\")\nvar movePoint = __webpack_require__(/*! ../util/move-point.js */ \"(ssr)/./node_modules/micromark/dist/util/move-point.js\")\nvar resolveAll = __webpack_require__(/*! ../util/resolve-all.js */ \"(ssr)/./node_modules/micromark/dist/util/resolve-all.js\")\nvar shallow = __webpack_require__(/*! ../util/shallow.js */ \"(ssr)/./node_modules/micromark/dist/util/shallow.js\")\n\nvar attention = {\n  name: 'attention',\n  tokenize: tokenizeAttention,\n  resolveAll: resolveAllAttention\n}\n\nfunction resolveAllAttention(events, context) {\n  var index = -1\n  var open\n  var group\n  var text\n  var openingSequence\n  var closingSequence\n  var use\n  var nextEvents\n  var offset // Walk through all events.\n  //\n  // Note: performance of this is fine on an mb of normal markdown, but it’s\n  // a bottleneck for malicious stuff.\n\n  while (++index < events.length) {\n    // Find a token that can close.\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === 'attentionSequence' &&\n      events[index][1]._close\n    ) {\n      open = index // Now walk back to find an opener.\n\n      while (open--) {\n        // Find a token that can open the closer.\n        if (\n          events[open][0] === 'exit' &&\n          events[open][1].type === 'attentionSequence' &&\n          events[open][1]._open && // If the markers are the same:\n          context.sliceSerialize(events[open][1]).charCodeAt(0) ===\n            context.sliceSerialize(events[index][1]).charCodeAt(0)\n        ) {\n          // If the opening can close or the closing can open,\n          // and the close size *is not* a multiple of three,\n          // but the sum of the opening and closing size *is* multiple of three,\n          // then don’t match.\n          if (\n            (events[open][1]._close || events[index][1]._open) &&\n            (events[index][1].end.offset - events[index][1].start.offset) % 3 &&\n            !(\n              (events[open][1].end.offset -\n                events[open][1].start.offset +\n                events[index][1].end.offset -\n                events[index][1].start.offset) %\n              3\n            )\n          ) {\n            continue\n          } // Number of markers to use from the sequence.\n\n          use =\n            events[open][1].end.offset - events[open][1].start.offset > 1 &&\n            events[index][1].end.offset - events[index][1].start.offset > 1\n              ? 2\n              : 1\n          openingSequence = {\n            type: use > 1 ? 'strongSequence' : 'emphasisSequence',\n            start: movePoint(shallow(events[open][1].end), -use),\n            end: shallow(events[open][1].end)\n          }\n          closingSequence = {\n            type: use > 1 ? 'strongSequence' : 'emphasisSequence',\n            start: shallow(events[index][1].start),\n            end: movePoint(shallow(events[index][1].start), use)\n          }\n          text = {\n            type: use > 1 ? 'strongText' : 'emphasisText',\n            start: shallow(events[open][1].end),\n            end: shallow(events[index][1].start)\n          }\n          group = {\n            type: use > 1 ? 'strong' : 'emphasis',\n            start: shallow(openingSequence.start),\n            end: shallow(closingSequence.end)\n          }\n          events[open][1].end = shallow(openingSequence.start)\n          events[index][1].start = shallow(closingSequence.end)\n          nextEvents = [] // If there are more markers in the opening, add them before.\n\n          if (events[open][1].end.offset - events[open][1].start.offset) {\n            nextEvents = chunkedPush(nextEvents, [\n              ['enter', events[open][1], context],\n              ['exit', events[open][1], context]\n            ])\n          } // Opening.\n\n          nextEvents = chunkedPush(nextEvents, [\n            ['enter', group, context],\n            ['enter', openingSequence, context],\n            ['exit', openingSequence, context],\n            ['enter', text, context]\n          ]) // Between.\n\n          nextEvents = chunkedPush(\n            nextEvents,\n            resolveAll(\n              context.parser.constructs.insideSpan.null,\n              events.slice(open + 1, index),\n              context\n            )\n          ) // Closing.\n\n          nextEvents = chunkedPush(nextEvents, [\n            ['exit', text, context],\n            ['enter', closingSequence, context],\n            ['exit', closingSequence, context],\n            ['exit', group, context]\n          ]) // If there are more markers in the closing, add them after.\n\n          if (events[index][1].end.offset - events[index][1].start.offset) {\n            offset = 2\n            nextEvents = chunkedPush(nextEvents, [\n              ['enter', events[index][1], context],\n              ['exit', events[index][1], context]\n            ])\n          } else {\n            offset = 0\n          }\n\n          chunkedSplice(events, open - 1, index - open + 3, nextEvents)\n          index = open + nextEvents.length - offset - 2\n          break\n        }\n      }\n    }\n  } // Remove remaining sequences.\n\n  index = -1\n\n  while (++index < events.length) {\n    if (events[index][1].type === 'attentionSequence') {\n      events[index][1].type = 'data'\n    }\n  }\n\n  return events\n}\n\nfunction tokenizeAttention(effects, ok) {\n  var before = classifyCharacter(this.previous)\n  var marker\n  return start\n\n  function start(code) {\n    effects.enter('attentionSequence')\n    marker = code\n    return sequence(code)\n  }\n\n  function sequence(code) {\n    var token\n    var after\n    var open\n    var close\n\n    if (code === marker) {\n      effects.consume(code)\n      return sequence\n    }\n\n    token = effects.exit('attentionSequence')\n    after = classifyCharacter(code)\n    open = !after || (after === 2 && before)\n    close = !before || (before === 2 && after)\n    token._open = marker === 42 ? open : open && (before || !close)\n    token._close = marker === 42 ? close : close && (after || !open)\n    return ok(code)\n  }\n}\n\nmodule.exports = attention\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/attention.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/autolink.js":
/*!**********************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/autolink.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar asciiAlpha = __webpack_require__(/*! ../character/ascii-alpha.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-alpha.js\")\nvar asciiAlphanumeric = __webpack_require__(/*! ../character/ascii-alphanumeric.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-alphanumeric.js\")\nvar asciiAtext = __webpack_require__(/*! ../character/ascii-atext.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-atext.js\")\nvar asciiControl = __webpack_require__(/*! ../character/ascii-control.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-control.js\")\n\nvar autolink = {\n  name: 'autolink',\n  tokenize: tokenizeAutolink\n}\n\nfunction tokenizeAutolink(effects, ok, nok) {\n  var size = 1\n  return start\n\n  function start(code) {\n    effects.enter('autolink')\n    effects.enter('autolinkMarker')\n    effects.consume(code)\n    effects.exit('autolinkMarker')\n    effects.enter('autolinkProtocol')\n    return open\n  }\n\n  function open(code) {\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return schemeOrEmailAtext\n    }\n\n    return asciiAtext(code) ? emailAtext(code) : nok(code)\n  }\n\n  function schemeOrEmailAtext(code) {\n    return code === 43 || code === 45 || code === 46 || asciiAlphanumeric(code)\n      ? schemeInsideOrEmailAtext(code)\n      : emailAtext(code)\n  }\n\n  function schemeInsideOrEmailAtext(code) {\n    if (code === 58) {\n      effects.consume(code)\n      return urlInside\n    }\n\n    if (\n      (code === 43 || code === 45 || code === 46 || asciiAlphanumeric(code)) &&\n      size++ < 32\n    ) {\n      effects.consume(code)\n      return schemeInsideOrEmailAtext\n    }\n\n    return emailAtext(code)\n  }\n\n  function urlInside(code) {\n    if (code === 62) {\n      effects.exit('autolinkProtocol')\n      return end(code)\n    }\n\n    if (code === 32 || code === 60 || asciiControl(code)) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return urlInside\n  }\n\n  function emailAtext(code) {\n    if (code === 64) {\n      effects.consume(code)\n      size = 0\n      return emailAtSignOrDot\n    }\n\n    if (asciiAtext(code)) {\n      effects.consume(code)\n      return emailAtext\n    }\n\n    return nok(code)\n  }\n\n  function emailAtSignOrDot(code) {\n    return asciiAlphanumeric(code) ? emailLabel(code) : nok(code)\n  }\n\n  function emailLabel(code) {\n    if (code === 46) {\n      effects.consume(code)\n      size = 0\n      return emailAtSignOrDot\n    }\n\n    if (code === 62) {\n      // Exit, then change the type.\n      effects.exit('autolinkProtocol').type = 'autolinkEmail'\n      return end(code)\n    }\n\n    return emailValue(code)\n  }\n\n  function emailValue(code) {\n    if ((code === 45 || asciiAlphanumeric(code)) && size++ < 63) {\n      effects.consume(code)\n      return code === 45 ? emailValue : emailLabel\n    }\n\n    return nok(code)\n  }\n\n  function end(code) {\n    effects.enter('autolinkMarker')\n    effects.consume(code)\n    effects.exit('autolinkMarker')\n    effects.exit('autolink')\n    return ok\n  }\n}\n\nmodule.exports = autolink\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/autolink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/block-quote.js":
/*!*************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/block-quote.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownSpace = __webpack_require__(/*! ../character/markdown-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-space.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nvar blockQuote = {\n  name: 'blockQuote',\n  tokenize: tokenizeBlockQuoteStart,\n  continuation: {\n    tokenize: tokenizeBlockQuoteContinuation\n  },\n  exit: exit\n}\n\nfunction tokenizeBlockQuoteStart(effects, ok, nok) {\n  var self = this\n  return start\n\n  function start(code) {\n    if (code === 62) {\n      if (!self.containerState.open) {\n        effects.enter('blockQuote', {\n          _container: true\n        })\n        self.containerState.open = true\n      }\n\n      effects.enter('blockQuotePrefix')\n      effects.enter('blockQuoteMarker')\n      effects.consume(code)\n      effects.exit('blockQuoteMarker')\n      return after\n    }\n\n    return nok(code)\n  }\n\n  function after(code) {\n    if (markdownSpace(code)) {\n      effects.enter('blockQuotePrefixWhitespace')\n      effects.consume(code)\n      effects.exit('blockQuotePrefixWhitespace')\n      effects.exit('blockQuotePrefix')\n      return ok\n    }\n\n    effects.exit('blockQuotePrefix')\n    return ok(code)\n  }\n}\n\nfunction tokenizeBlockQuoteContinuation(effects, ok, nok) {\n  return factorySpace(\n    effects,\n    effects.attempt(blockQuote, ok, nok),\n    'linePrefix',\n    this.parser.constructs.disable.null.indexOf('codeIndented') > -1\n      ? undefined\n      : 4\n  )\n}\n\nfunction exit(effects) {\n  effects.exit('blockQuote')\n}\n\nmodule.exports = blockQuote\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/block-quote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/character-escape.js":
/*!******************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/character-escape.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar asciiPunctuation = __webpack_require__(/*! ../character/ascii-punctuation.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-punctuation.js\")\n\nvar characterEscape = {\n  name: 'characterEscape',\n  tokenize: tokenizeCharacterEscape\n}\n\nfunction tokenizeCharacterEscape(effects, ok, nok) {\n  return start\n\n  function start(code) {\n    effects.enter('characterEscape')\n    effects.enter('escapeMarker')\n    effects.consume(code)\n    effects.exit('escapeMarker')\n    return open\n  }\n\n  function open(code) {\n    if (asciiPunctuation(code)) {\n      effects.enter('characterEscapeValue')\n      effects.consume(code)\n      effects.exit('characterEscapeValue')\n      effects.exit('characterEscape')\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n\nmodule.exports = characterEscape\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdG9rZW5pemUvY2hhcmFjdGVyLWVzY2FwZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWix1QkFBdUIsbUJBQU8sQ0FBQyw2R0FBbUM7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L3Rva2VuaXplL2NoYXJhY3Rlci1lc2NhcGUuanM/NjRlZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIGFzY2lpUHVuY3R1YXRpb24gPSByZXF1aXJlKCcuLi9jaGFyYWN0ZXIvYXNjaWktcHVuY3R1YXRpb24uanMnKVxuXG52YXIgY2hhcmFjdGVyRXNjYXBlID0ge1xuICBuYW1lOiAnY2hhcmFjdGVyRXNjYXBlJyxcbiAgdG9rZW5pemU6IHRva2VuaXplQ2hhcmFjdGVyRXNjYXBlXG59XG5cbmZ1bmN0aW9uIHRva2VuaXplQ2hhcmFjdGVyRXNjYXBlKGVmZmVjdHMsIG9rLCBub2spIHtcbiAgcmV0dXJuIHN0YXJ0XG5cbiAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgIGVmZmVjdHMuZW50ZXIoJ2NoYXJhY3RlckVzY2FwZScpXG4gICAgZWZmZWN0cy5lbnRlcignZXNjYXBlTWFya2VyJylcbiAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICBlZmZlY3RzLmV4aXQoJ2VzY2FwZU1hcmtlcicpXG4gICAgcmV0dXJuIG9wZW5cbiAgfVxuXG4gIGZ1bmN0aW9uIG9wZW4oY29kZSkge1xuICAgIGlmIChhc2NpaVB1bmN0dWF0aW9uKGNvZGUpKSB7XG4gICAgICBlZmZlY3RzLmVudGVyKCdjaGFyYWN0ZXJFc2NhcGVWYWx1ZScpXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIGVmZmVjdHMuZXhpdCgnY2hhcmFjdGVyRXNjYXBlVmFsdWUnKVxuICAgICAgZWZmZWN0cy5leGl0KCdjaGFyYWN0ZXJFc2NhcGUnKVxuICAgICAgcmV0dXJuIG9rXG4gICAgfVxuXG4gICAgcmV0dXJuIG5vayhjb2RlKVxuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gY2hhcmFjdGVyRXNjYXBlXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/character-escape.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/character-reference.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/character-reference.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar decodeEntity = __webpack_require__(/*! parse-entities/decode-entity.js */ \"(ssr)/./node_modules/parse-entities/decode-entity.js\")\nvar asciiAlphanumeric = __webpack_require__(/*! ../character/ascii-alphanumeric.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-alphanumeric.js\")\nvar asciiDigit = __webpack_require__(/*! ../character/ascii-digit.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-digit.js\")\nvar asciiHexDigit = __webpack_require__(/*! ../character/ascii-hex-digit.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-hex-digit.js\")\n\nfunction _interopDefaultLegacy(e) {\n  return e && typeof e === 'object' && 'default' in e ? e : {default: e}\n}\n\nvar decodeEntity__default = /*#__PURE__*/ _interopDefaultLegacy(decodeEntity)\n\nvar characterReference = {\n  name: 'characterReference',\n  tokenize: tokenizeCharacterReference\n}\n\nfunction tokenizeCharacterReference(effects, ok, nok) {\n  var self = this\n  var size = 0\n  var max\n  var test\n  return start\n\n  function start(code) {\n    effects.enter('characterReference')\n    effects.enter('characterReferenceMarker')\n    effects.consume(code)\n    effects.exit('characterReferenceMarker')\n    return open\n  }\n\n  function open(code) {\n    if (code === 35) {\n      effects.enter('characterReferenceMarkerNumeric')\n      effects.consume(code)\n      effects.exit('characterReferenceMarkerNumeric')\n      return numeric\n    }\n\n    effects.enter('characterReferenceValue')\n    max = 31\n    test = asciiAlphanumeric\n    return value(code)\n  }\n\n  function numeric(code) {\n    if (code === 88 || code === 120) {\n      effects.enter('characterReferenceMarkerHexadecimal')\n      effects.consume(code)\n      effects.exit('characterReferenceMarkerHexadecimal')\n      effects.enter('characterReferenceValue')\n      max = 6\n      test = asciiHexDigit\n      return value\n    }\n\n    effects.enter('characterReferenceValue')\n    max = 7\n    test = asciiDigit\n    return value(code)\n  }\n\n  function value(code) {\n    var token\n\n    if (code === 59 && size) {\n      token = effects.exit('characterReferenceValue')\n\n      if (\n        test === asciiAlphanumeric &&\n        !decodeEntity__default['default'](self.sliceSerialize(token))\n      ) {\n        return nok(code)\n      }\n\n      effects.enter('characterReferenceMarker')\n      effects.consume(code)\n      effects.exit('characterReferenceMarker')\n      effects.exit('characterReference')\n      return ok\n    }\n\n    if (test(code) && size++ < max) {\n      effects.consume(code)\n      return value\n    }\n\n    return nok(code)\n  }\n}\n\nmodule.exports = characterReference\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/character-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/code-fenced.js":
/*!*************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/code-fenced.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar markdownLineEndingOrSpace = __webpack_require__(/*! ../character/markdown-line-ending-or-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending-or-space.js\")\nvar prefixSize = __webpack_require__(/*! ../util/prefix-size.js */ \"(ssr)/./node_modules/micromark/dist/util/prefix-size.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nvar codeFenced = {\n  name: 'codeFenced',\n  tokenize: tokenizeCodeFenced,\n  concrete: true\n}\n\nfunction tokenizeCodeFenced(effects, ok, nok) {\n  var self = this\n  var closingFenceConstruct = {\n    tokenize: tokenizeClosingFence,\n    partial: true\n  }\n  var initialPrefix = prefixSize(this.events, 'linePrefix')\n  var sizeOpen = 0\n  var marker\n  return start\n\n  function start(code) {\n    effects.enter('codeFenced')\n    effects.enter('codeFencedFence')\n    effects.enter('codeFencedFenceSequence')\n    marker = code\n    return sequenceOpen(code)\n  }\n\n  function sequenceOpen(code) {\n    if (code === marker) {\n      effects.consume(code)\n      sizeOpen++\n      return sequenceOpen\n    }\n\n    effects.exit('codeFencedFenceSequence')\n    return sizeOpen < 3\n      ? nok(code)\n      : factorySpace(effects, infoOpen, 'whitespace')(code)\n  }\n\n  function infoOpen(code) {\n    if (code === null || markdownLineEnding(code)) {\n      return openAfter(code)\n    }\n\n    effects.enter('codeFencedFenceInfo')\n    effects.enter('chunkString', {\n      contentType: 'string'\n    })\n    return info(code)\n  }\n\n  function info(code) {\n    if (code === null || markdownLineEndingOrSpace(code)) {\n      effects.exit('chunkString')\n      effects.exit('codeFencedFenceInfo')\n      return factorySpace(effects, infoAfter, 'whitespace')(code)\n    }\n\n    if (code === 96 && code === marker) return nok(code)\n    effects.consume(code)\n    return info\n  }\n\n  function infoAfter(code) {\n    if (code === null || markdownLineEnding(code)) {\n      return openAfter(code)\n    }\n\n    effects.enter('codeFencedFenceMeta')\n    effects.enter('chunkString', {\n      contentType: 'string'\n    })\n    return meta(code)\n  }\n\n  function meta(code) {\n    if (code === null || markdownLineEnding(code)) {\n      effects.exit('chunkString')\n      effects.exit('codeFencedFenceMeta')\n      return openAfter(code)\n    }\n\n    if (code === 96 && code === marker) return nok(code)\n    effects.consume(code)\n    return meta\n  }\n\n  function openAfter(code) {\n    effects.exit('codeFencedFence')\n    return self.interrupt ? ok(code) : content(code)\n  }\n\n  function content(code) {\n    if (code === null) {\n      return after(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter('lineEnding')\n      effects.consume(code)\n      effects.exit('lineEnding')\n      return effects.attempt(\n        closingFenceConstruct,\n        after,\n        initialPrefix\n          ? factorySpace(effects, content, 'linePrefix', initialPrefix + 1)\n          : content\n      )\n    }\n\n    effects.enter('codeFlowValue')\n    return contentContinue(code)\n  }\n\n  function contentContinue(code) {\n    if (code === null || markdownLineEnding(code)) {\n      effects.exit('codeFlowValue')\n      return content(code)\n    }\n\n    effects.consume(code)\n    return contentContinue\n  }\n\n  function after(code) {\n    effects.exit('codeFenced')\n    return ok(code)\n  }\n\n  function tokenizeClosingFence(effects, ok, nok) {\n    var size = 0\n    return factorySpace(\n      effects,\n      closingSequenceStart,\n      'linePrefix',\n      this.parser.constructs.disable.null.indexOf('codeIndented') > -1\n        ? undefined\n        : 4\n    )\n\n    function closingSequenceStart(code) {\n      effects.enter('codeFencedFence')\n      effects.enter('codeFencedFenceSequence')\n      return closingSequence(code)\n    }\n\n    function closingSequence(code) {\n      if (code === marker) {\n        effects.consume(code)\n        size++\n        return closingSequence\n      }\n\n      if (size < sizeOpen) return nok(code)\n      effects.exit('codeFencedFenceSequence')\n      return factorySpace(effects, closingSequenceEnd, 'whitespace')(code)\n    }\n\n    function closingSequenceEnd(code) {\n      if (code === null || markdownLineEnding(code)) {\n        effects.exit('codeFencedFence')\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\nmodule.exports = codeFenced\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/code-fenced.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/code-indented.js":
/*!***************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/code-indented.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar chunkedSplice = __webpack_require__(/*! ../util/chunked-splice.js */ \"(ssr)/./node_modules/micromark/dist/util/chunked-splice.js\")\nvar prefixSize = __webpack_require__(/*! ../util/prefix-size.js */ \"(ssr)/./node_modules/micromark/dist/util/prefix-size.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nvar codeIndented = {\n  name: 'codeIndented',\n  tokenize: tokenizeCodeIndented,\n  resolve: resolveCodeIndented\n}\nvar indentedContentConstruct = {\n  tokenize: tokenizeIndentedContent,\n  partial: true\n}\n\nfunction resolveCodeIndented(events, context) {\n  var code = {\n    type: 'codeIndented',\n    start: events[0][1].start,\n    end: events[events.length - 1][1].end\n  }\n  chunkedSplice(events, 0, 0, [['enter', code, context]])\n  chunkedSplice(events, events.length, 0, [['exit', code, context]])\n  return events\n}\n\nfunction tokenizeCodeIndented(effects, ok, nok) {\n  return effects.attempt(indentedContentConstruct, afterPrefix, nok)\n\n  function afterPrefix(code) {\n    if (code === null) {\n      return ok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      return effects.attempt(indentedContentConstruct, afterPrefix, ok)(code)\n    }\n\n    effects.enter('codeFlowValue')\n    return content(code)\n  }\n\n  function content(code) {\n    if (code === null || markdownLineEnding(code)) {\n      effects.exit('codeFlowValue')\n      return afterPrefix(code)\n    }\n\n    effects.consume(code)\n    return content\n  }\n}\n\nfunction tokenizeIndentedContent(effects, ok, nok) {\n  var self = this\n  return factorySpace(effects, afterPrefix, 'linePrefix', 4 + 1)\n\n  function afterPrefix(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter('lineEnding')\n      effects.consume(code)\n      effects.exit('lineEnding')\n      return factorySpace(effects, afterPrefix, 'linePrefix', 4 + 1)\n    }\n\n    return prefixSize(self.events, 'linePrefix') < 4 ? nok(code) : ok(code)\n  }\n}\n\nmodule.exports = codeIndented\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/code-indented.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/code-text.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/code-text.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\n\nvar codeText = {\n  name: 'codeText',\n  tokenize: tokenizeCodeText,\n  resolve: resolveCodeText,\n  previous: previous\n}\n\nfunction resolveCodeText(events) {\n  var tailExitIndex = events.length - 4\n  var headEnterIndex = 3\n  var index\n  var enter // If we start and end with an EOL or a space.\n\n  if (\n    (events[headEnterIndex][1].type === 'lineEnding' ||\n      events[headEnterIndex][1].type === 'space') &&\n    (events[tailExitIndex][1].type === 'lineEnding' ||\n      events[tailExitIndex][1].type === 'space')\n  ) {\n    index = headEnterIndex // And we have data.\n\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === 'codeTextData') {\n        // Then we have padding.\n        events[tailExitIndex][1].type = events[headEnterIndex][1].type =\n          'codeTextPadding'\n        headEnterIndex += 2\n        tailExitIndex -= 2\n        break\n      }\n    }\n  } // Merge adjacent spaces and data.\n\n  index = headEnterIndex - 1\n  tailExitIndex++\n\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (index !== tailExitIndex && events[index][1].type !== 'lineEnding') {\n        enter = index\n      }\n    } else if (\n      index === tailExitIndex ||\n      events[index][1].type === 'lineEnding'\n    ) {\n      events[enter][1].type = 'codeTextData'\n\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end\n        events.splice(enter + 2, index - enter - 2)\n        tailExitIndex -= index - enter - 2\n        index = enter + 2\n      }\n\n      enter = undefined\n    }\n  }\n\n  return events\n}\n\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return (\n    code !== 96 ||\n    this.events[this.events.length - 1][1].type === 'characterEscape'\n  )\n}\n\nfunction tokenizeCodeText(effects, ok, nok) {\n  var sizeOpen = 0\n  var size\n  var token\n  return start\n\n  function start(code) {\n    effects.enter('codeText')\n    effects.enter('codeTextSequence')\n    return openingSequence(code)\n  }\n\n  function openingSequence(code) {\n    if (code === 96) {\n      effects.consume(code)\n      sizeOpen++\n      return openingSequence\n    }\n\n    effects.exit('codeTextSequence')\n    return gap(code)\n  }\n\n  function gap(code) {\n    // EOF.\n    if (code === null) {\n      return nok(code)\n    } // Closing fence?\n    // Could also be data.\n\n    if (code === 96) {\n      token = effects.enter('codeTextSequence')\n      size = 0\n      return closingSequence(code)\n    } // Tabs don’t work, and virtual spaces don’t make sense.\n\n    if (code === 32) {\n      effects.enter('space')\n      effects.consume(code)\n      effects.exit('space')\n      return gap\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter('lineEnding')\n      effects.consume(code)\n      effects.exit('lineEnding')\n      return gap\n    } // Data.\n\n    effects.enter('codeTextData')\n    return data(code)\n  } // In code.\n\n  function data(code) {\n    if (\n      code === null ||\n      code === 32 ||\n      code === 96 ||\n      markdownLineEnding(code)\n    ) {\n      effects.exit('codeTextData')\n      return gap(code)\n    }\n\n    effects.consume(code)\n    return data\n  } // Closing fence.\n\n  function closingSequence(code) {\n    // More.\n    if (code === 96) {\n      effects.consume(code)\n      size++\n      return closingSequence\n    } // Done!\n\n    if (size === sizeOpen) {\n      effects.exit('codeTextSequence')\n      effects.exit('codeText')\n      return ok(code)\n    } // More or less accents: mark as data.\n\n    token.type = 'codeTextData'\n    return data(code)\n  }\n}\n\nmodule.exports = codeText\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/code-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/content.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/content.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar prefixSize = __webpack_require__(/*! ../util/prefix-size.js */ \"(ssr)/./node_modules/micromark/dist/util/prefix-size.js\")\nvar subtokenize = __webpack_require__(/*! ../util/subtokenize.js */ \"(ssr)/./node_modules/micromark/dist/util/subtokenize.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\n// No name because it must not be turned off.\nvar content = {\n  tokenize: tokenizeContent,\n  resolve: resolveContent,\n  interruptible: true,\n  lazy: true\n}\nvar continuationConstruct = {\n  tokenize: tokenizeContinuation,\n  partial: true\n} // Content is transparent: it’s parsed right now. That way, definitions are also\n// parsed right now: before text in paragraphs (specifically, media) are parsed.\n\nfunction resolveContent(events) {\n  subtokenize(events)\n  return events\n}\n\nfunction tokenizeContent(effects, ok) {\n  var previous\n  return start\n\n  function start(code) {\n    effects.enter('content')\n    previous = effects.enter('chunkContent', {\n      contentType: 'content'\n    })\n    return data(code)\n  }\n\n  function data(code) {\n    if (code === null) {\n      return contentEnd(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      return effects.check(\n        continuationConstruct,\n        contentContinue,\n        contentEnd\n      )(code)\n    } // Data.\n\n    effects.consume(code)\n    return data\n  }\n\n  function contentEnd(code) {\n    effects.exit('chunkContent')\n    effects.exit('content')\n    return ok(code)\n  }\n\n  function contentContinue(code) {\n    effects.consume(code)\n    effects.exit('chunkContent')\n    previous = previous.next = effects.enter('chunkContent', {\n      contentType: 'content',\n      previous: previous\n    })\n    return data\n  }\n}\n\nfunction tokenizeContinuation(effects, ok, nok) {\n  var self = this\n  return startLookahead\n\n  function startLookahead(code) {\n    effects.enter('lineEnding')\n    effects.consume(code)\n    effects.exit('lineEnding')\n    return factorySpace(effects, prefixed, 'linePrefix')\n  }\n\n  function prefixed(code) {\n    if (code === null || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    if (\n      self.parser.constructs.disable.null.indexOf('codeIndented') > -1 ||\n      prefixSize(self.events, 'linePrefix') < 4\n    ) {\n      return effects.interrupt(self.parser.constructs.flow, nok, ok)(code)\n    }\n\n    return ok(code)\n  }\n}\n\nmodule.exports = content\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/definition.js":
/*!************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/definition.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar markdownLineEndingOrSpace = __webpack_require__(/*! ../character/markdown-line-ending-or-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending-or-space.js\")\nvar normalizeIdentifier = __webpack_require__(/*! ../util/normalize-identifier.js */ \"(ssr)/./node_modules/micromark/dist/util/normalize-identifier.js\")\nvar factoryDestination = __webpack_require__(/*! ./factory-destination.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-destination.js\")\nvar factoryLabel = __webpack_require__(/*! ./factory-label.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-label.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\nvar factoryWhitespace = __webpack_require__(/*! ./factory-whitespace.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-whitespace.js\")\nvar factoryTitle = __webpack_require__(/*! ./factory-title.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-title.js\")\n\nvar definition = {\n  name: 'definition',\n  tokenize: tokenizeDefinition\n}\nvar titleConstruct = {\n  tokenize: tokenizeTitle,\n  partial: true\n}\n\nfunction tokenizeDefinition(effects, ok, nok) {\n  var self = this\n  var identifier\n  return start\n\n  function start(code) {\n    effects.enter('definition')\n    return factoryLabel.call(\n      self,\n      effects,\n      labelAfter,\n      nok,\n      'definitionLabel',\n      'definitionLabelMarker',\n      'definitionLabelString'\n    )(code)\n  }\n\n  function labelAfter(code) {\n    identifier = normalizeIdentifier(\n      self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n    )\n\n    if (code === 58) {\n      effects.enter('definitionMarker')\n      effects.consume(code)\n      effects.exit('definitionMarker') // Note: blank lines can’t exist in content.\n\n      return factoryWhitespace(\n        effects,\n        factoryDestination(\n          effects,\n          effects.attempt(\n            titleConstruct,\n            factorySpace(effects, after, 'whitespace'),\n            factorySpace(effects, after, 'whitespace')\n          ),\n          nok,\n          'definitionDestination',\n          'definitionDestinationLiteral',\n          'definitionDestinationLiteralMarker',\n          'definitionDestinationRaw',\n          'definitionDestinationString'\n        )\n      )\n    }\n\n    return nok(code)\n  }\n\n  function after(code) {\n    if (code === null || markdownLineEnding(code)) {\n      effects.exit('definition')\n\n      if (self.parser.defined.indexOf(identifier) < 0) {\n        self.parser.defined.push(identifier)\n      }\n\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\nfunction tokenizeTitle(effects, ok, nok) {\n  return start\n\n  function start(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, before)(code)\n      : nok(code)\n  }\n\n  function before(code) {\n    if (code === 34 || code === 39 || code === 40) {\n      return factoryTitle(\n        effects,\n        factorySpace(effects, after, 'whitespace'),\n        nok,\n        'definitionTitle',\n        'definitionTitleMarker',\n        'definitionTitleString'\n      )(code)\n    }\n\n    return nok(code)\n  }\n\n  function after(code) {\n    return code === null || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n\nmodule.exports = definition\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/definition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/factory-destination.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/factory-destination.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar asciiControl = __webpack_require__(/*! ../character/ascii-control.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-control.js\")\nvar markdownLineEndingOrSpace = __webpack_require__(/*! ../character/markdown-line-ending-or-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending-or-space.js\")\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\n\n// eslint-disable-next-line max-params\nfunction destinationFactory(\n  effects,\n  ok,\n  nok,\n  type,\n  literalType,\n  literalMarkerType,\n  rawType,\n  stringType,\n  max\n) {\n  var limit = max || Infinity\n  var balance = 0\n  return start\n\n  function start(code) {\n    if (code === 60) {\n      effects.enter(type)\n      effects.enter(literalType)\n      effects.enter(literalMarkerType)\n      effects.consume(code)\n      effects.exit(literalMarkerType)\n      return destinationEnclosedBefore\n    }\n\n    if (asciiControl(code) || code === 41) {\n      return nok(code)\n    }\n\n    effects.enter(type)\n    effects.enter(rawType)\n    effects.enter(stringType)\n    effects.enter('chunkString', {\n      contentType: 'string'\n    })\n    return destinationRaw(code)\n  }\n\n  function destinationEnclosedBefore(code) {\n    if (code === 62) {\n      effects.enter(literalMarkerType)\n      effects.consume(code)\n      effects.exit(literalMarkerType)\n      effects.exit(literalType)\n      effects.exit(type)\n      return ok\n    }\n\n    effects.enter(stringType)\n    effects.enter('chunkString', {\n      contentType: 'string'\n    })\n    return destinationEnclosed(code)\n  }\n\n  function destinationEnclosed(code) {\n    if (code === 62) {\n      effects.exit('chunkString')\n      effects.exit(stringType)\n      return destinationEnclosedBefore(code)\n    }\n\n    if (code === null || code === 60 || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return code === 92 ? destinationEnclosedEscape : destinationEnclosed\n  }\n\n  function destinationEnclosedEscape(code) {\n    if (code === 60 || code === 62 || code === 92) {\n      effects.consume(code)\n      return destinationEnclosed\n    }\n\n    return destinationEnclosed(code)\n  }\n\n  function destinationRaw(code) {\n    if (code === 40) {\n      if (++balance > limit) return nok(code)\n      effects.consume(code)\n      return destinationRaw\n    }\n\n    if (code === 41) {\n      if (!balance--) {\n        effects.exit('chunkString')\n        effects.exit(stringType)\n        effects.exit(rawType)\n        effects.exit(type)\n        return ok(code)\n      }\n\n      effects.consume(code)\n      return destinationRaw\n    }\n\n    if (code === null || markdownLineEndingOrSpace(code)) {\n      if (balance) return nok(code)\n      effects.exit('chunkString')\n      effects.exit(stringType)\n      effects.exit(rawType)\n      effects.exit(type)\n      return ok(code)\n    }\n\n    if (asciiControl(code)) return nok(code)\n    effects.consume(code)\n    return code === 92 ? destinationRawEscape : destinationRaw\n  }\n\n  function destinationRawEscape(code) {\n    if (code === 40 || code === 41 || code === 92) {\n      effects.consume(code)\n      return destinationRaw\n    }\n\n    return destinationRaw(code)\n  }\n}\n\nmodule.exports = destinationFactory\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/factory-destination.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/factory-label.js":
/*!***************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/factory-label.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar markdownSpace = __webpack_require__(/*! ../character/markdown-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-space.js\")\n\n// eslint-disable-next-line max-params\nfunction labelFactory(effects, ok, nok, type, markerType, stringType) {\n  var self = this\n  var size = 0\n  var data\n  return start\n\n  function start(code) {\n    effects.enter(type)\n    effects.enter(markerType)\n    effects.consume(code)\n    effects.exit(markerType)\n    effects.enter(stringType)\n    return atBreak\n  }\n\n  function atBreak(code) {\n    if (\n      code === null ||\n      code === 91 ||\n      (code === 93 && !data) ||\n      /* c8 ignore next */\n      (code === 94 &&\n        /* c8 ignore next */\n        !size &&\n        /* c8 ignore next */\n        '_hiddenFootnoteSupport' in self.parser.constructs) ||\n      size > 999\n    ) {\n      return nok(code)\n    }\n\n    if (code === 93) {\n      effects.exit(stringType)\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      effects.exit(type)\n      return ok\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter('lineEnding')\n      effects.consume(code)\n      effects.exit('lineEnding')\n      return atBreak\n    }\n\n    effects.enter('chunkString', {\n      contentType: 'string'\n    })\n    return label(code)\n  }\n\n  function label(code) {\n    if (\n      code === null ||\n      code === 91 ||\n      code === 93 ||\n      markdownLineEnding(code) ||\n      size++ > 999\n    ) {\n      effects.exit('chunkString')\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    data = data || !markdownSpace(code)\n    return code === 92 ? labelEscape : label\n  }\n\n  function labelEscape(code) {\n    if (code === 91 || code === 92 || code === 93) {\n      effects.consume(code)\n      size++\n      return label\n    }\n\n    return label(code)\n  }\n}\n\nmodule.exports = labelFactory\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/factory-label.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js":
/*!***************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/factory-space.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownSpace = __webpack_require__(/*! ../character/markdown-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-space.js\")\n\nfunction spaceFactory(effects, ok, type, max) {\n  var limit = max ? max - 1 : Infinity\n  var size = 0\n  return start\n\n  function start(code) {\n    if (markdownSpace(code)) {\n      effects.enter(type)\n      return prefix(code)\n    }\n\n    return ok(code)\n  }\n\n  function prefix(code) {\n    if (markdownSpace(code) && size++ < limit) {\n      effects.consume(code)\n      return prefix\n    }\n\n    effects.exit(type)\n    return ok(code)\n  }\n}\n\nmodule.exports = spaceFactory\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdG9rZW5pemUvZmFjdG9yeS1zcGFjZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixvQkFBb0IsbUJBQU8sQ0FBQyx1R0FBZ0M7O0FBRTVEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC90b2tlbml6ZS9mYWN0b3J5LXNwYWNlLmpzPzMyMzkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBtYXJrZG93blNwYWNlID0gcmVxdWlyZSgnLi4vY2hhcmFjdGVyL21hcmtkb3duLXNwYWNlLmpzJylcblxuZnVuY3Rpb24gc3BhY2VGYWN0b3J5KGVmZmVjdHMsIG9rLCB0eXBlLCBtYXgpIHtcbiAgdmFyIGxpbWl0ID0gbWF4ID8gbWF4IC0gMSA6IEluZmluaXR5XG4gIHZhciBzaXplID0gMFxuICByZXR1cm4gc3RhcnRcblxuICBmdW5jdGlvbiBzdGFydChjb2RlKSB7XG4gICAgaWYgKG1hcmtkb3duU3BhY2UoY29kZSkpIHtcbiAgICAgIGVmZmVjdHMuZW50ZXIodHlwZSlcbiAgICAgIHJldHVybiBwcmVmaXgoY29kZSlcbiAgICB9XG5cbiAgICByZXR1cm4gb2soY29kZSlcbiAgfVxuXG4gIGZ1bmN0aW9uIHByZWZpeChjb2RlKSB7XG4gICAgaWYgKG1hcmtkb3duU3BhY2UoY29kZSkgJiYgc2l6ZSsrIDwgbGltaXQpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIHByZWZpeFxuICAgIH1cblxuICAgIGVmZmVjdHMuZXhpdCh0eXBlKVxuICAgIHJldHVybiBvayhjb2RlKVxuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gc3BhY2VGYWN0b3J5XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/factory-title.js":
/*!***************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/factory-title.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nfunction titleFactory(effects, ok, nok, type, markerType, stringType) {\n  var marker\n  return start\n\n  function start(code) {\n    effects.enter(type)\n    effects.enter(markerType)\n    effects.consume(code)\n    effects.exit(markerType)\n    marker = code === 40 ? 41 : code\n    return atFirstTitleBreak\n  }\n\n  function atFirstTitleBreak(code) {\n    if (code === marker) {\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      effects.exit(type)\n      return ok\n    }\n\n    effects.enter(stringType)\n    return atTitleBreak(code)\n  }\n\n  function atTitleBreak(code) {\n    if (code === marker) {\n      effects.exit(stringType)\n      return atFirstTitleBreak(marker)\n    }\n\n    if (code === null) {\n      return nok(code)\n    } // Note: blank lines can’t exist in content.\n\n    if (markdownLineEnding(code)) {\n      effects.enter('lineEnding')\n      effects.consume(code)\n      effects.exit('lineEnding')\n      return factorySpace(effects, atTitleBreak, 'linePrefix')\n    }\n\n    effects.enter('chunkString', {\n      contentType: 'string'\n    })\n    return title(code)\n  }\n\n  function title(code) {\n    if (code === marker || code === null || markdownLineEnding(code)) {\n      effects.exit('chunkString')\n      return atTitleBreak(code)\n    }\n\n    effects.consume(code)\n    return code === 92 ? titleEscape : title\n  }\n\n  function titleEscape(code) {\n    if (code === marker || code === 92) {\n      effects.consume(code)\n      return title\n    }\n\n    return title(code)\n  }\n}\n\nmodule.exports = titleFactory\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/factory-title.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/factory-whitespace.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/factory-whitespace.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar markdownSpace = __webpack_require__(/*! ../character/markdown-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-space.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nfunction whitespaceFactory(effects, ok) {\n  var seen\n  return start\n\n  function start(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter('lineEnding')\n      effects.consume(code)\n      effects.exit('lineEnding')\n      seen = true\n      return start\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(\n        effects,\n        start,\n        seen ? 'linePrefix' : 'lineSuffix'\n      )(code)\n    }\n\n    return ok(code)\n  }\n}\n\nmodule.exports = whitespaceFactory\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdG9rZW5pemUvZmFjdG9yeS13aGl0ZXNwYWNlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLHlCQUF5QixtQkFBTyxDQUFDLG1IQUFzQztBQUN2RSxvQkFBb0IsbUJBQU8sQ0FBQyx1R0FBZ0M7QUFDNUQsbUJBQW1CLG1CQUFPLENBQUMseUZBQW9COztBQUUvQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L3Rva2VuaXplL2ZhY3Rvcnktd2hpdGVzcGFjZS5qcz9jYWVkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgbWFya2Rvd25MaW5lRW5kaW5nID0gcmVxdWlyZSgnLi4vY2hhcmFjdGVyL21hcmtkb3duLWxpbmUtZW5kaW5nLmpzJylcbnZhciBtYXJrZG93blNwYWNlID0gcmVxdWlyZSgnLi4vY2hhcmFjdGVyL21hcmtkb3duLXNwYWNlLmpzJylcbnZhciBmYWN0b3J5U3BhY2UgPSByZXF1aXJlKCcuL2ZhY3Rvcnktc3BhY2UuanMnKVxuXG5mdW5jdGlvbiB3aGl0ZXNwYWNlRmFjdG9yeShlZmZlY3RzLCBvaykge1xuICB2YXIgc2VlblxuICByZXR1cm4gc3RhcnRcblxuICBmdW5jdGlvbiBzdGFydChjb2RlKSB7XG4gICAgaWYgKG1hcmtkb3duTGluZUVuZGluZyhjb2RlKSkge1xuICAgICAgZWZmZWN0cy5lbnRlcignbGluZUVuZGluZycpXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIGVmZmVjdHMuZXhpdCgnbGluZUVuZGluZycpXG4gICAgICBzZWVuID0gdHJ1ZVxuICAgICAgcmV0dXJuIHN0YXJ0XG4gICAgfVxuXG4gICAgaWYgKG1hcmtkb3duU3BhY2UoY29kZSkpIHtcbiAgICAgIHJldHVybiBmYWN0b3J5U3BhY2UoXG4gICAgICAgIGVmZmVjdHMsXG4gICAgICAgIHN0YXJ0LFxuICAgICAgICBzZWVuID8gJ2xpbmVQcmVmaXgnIDogJ2xpbmVTdWZmaXgnXG4gICAgICApKGNvZGUpXG4gICAgfVxuXG4gICAgcmV0dXJuIG9rKGNvZGUpXG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSB3aGl0ZXNwYWNlRmFjdG9yeVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/factory-whitespace.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/hard-break-escape.js":
/*!*******************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/hard-break-escape.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\n\nvar hardBreakEscape = {\n  name: 'hardBreakEscape',\n  tokenize: tokenizeHardBreakEscape\n}\n\nfunction tokenizeHardBreakEscape(effects, ok, nok) {\n  return start\n\n  function start(code) {\n    effects.enter('hardBreakEscape')\n    effects.enter('escapeMarker')\n    effects.consume(code)\n    return open\n  }\n\n  function open(code) {\n    if (markdownLineEnding(code)) {\n      effects.exit('escapeMarker')\n      effects.exit('hardBreakEscape')\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\nmodule.exports = hardBreakEscape\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdG9rZW5pemUvaGFyZC1icmVhay1lc2NhcGUuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVoseUJBQXlCLG1CQUFPLENBQUMsbUhBQXNDOztBQUV2RTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC90b2tlbml6ZS9oYXJkLWJyZWFrLWVzY2FwZS5qcz82ZmM4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgbWFya2Rvd25MaW5lRW5kaW5nID0gcmVxdWlyZSgnLi4vY2hhcmFjdGVyL21hcmtkb3duLWxpbmUtZW5kaW5nLmpzJylcblxudmFyIGhhcmRCcmVha0VzY2FwZSA9IHtcbiAgbmFtZTogJ2hhcmRCcmVha0VzY2FwZScsXG4gIHRva2VuaXplOiB0b2tlbml6ZUhhcmRCcmVha0VzY2FwZVxufVxuXG5mdW5jdGlvbiB0b2tlbml6ZUhhcmRCcmVha0VzY2FwZShlZmZlY3RzLCBvaywgbm9rKSB7XG4gIHJldHVybiBzdGFydFxuXG4gIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICBlZmZlY3RzLmVudGVyKCdoYXJkQnJlYWtFc2NhcGUnKVxuICAgIGVmZmVjdHMuZW50ZXIoJ2VzY2FwZU1hcmtlcicpXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgcmV0dXJuIG9wZW5cbiAgfVxuXG4gIGZ1bmN0aW9uIG9wZW4oY29kZSkge1xuICAgIGlmIChtYXJrZG93bkxpbmVFbmRpbmcoY29kZSkpIHtcbiAgICAgIGVmZmVjdHMuZXhpdCgnZXNjYXBlTWFya2VyJylcbiAgICAgIGVmZmVjdHMuZXhpdCgnaGFyZEJyZWFrRXNjYXBlJylcbiAgICAgIHJldHVybiBvayhjb2RlKVxuICAgIH1cblxuICAgIHJldHVybiBub2soY29kZSlcbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IGhhcmRCcmVha0VzY2FwZVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/hard-break-escape.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/heading-atx.js":
/*!*************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/heading-atx.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar markdownLineEndingOrSpace = __webpack_require__(/*! ../character/markdown-line-ending-or-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending-or-space.js\")\nvar markdownSpace = __webpack_require__(/*! ../character/markdown-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-space.js\")\nvar chunkedSplice = __webpack_require__(/*! ../util/chunked-splice.js */ \"(ssr)/./node_modules/micromark/dist/util/chunked-splice.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nvar headingAtx = {\n  name: 'headingAtx',\n  tokenize: tokenizeHeadingAtx,\n  resolve: resolveHeadingAtx\n}\n\nfunction resolveHeadingAtx(events, context) {\n  var contentEnd = events.length - 2\n  var contentStart = 3\n  var content\n  var text // Prefix whitespace, part of the opening.\n\n  if (events[contentStart][1].type === 'whitespace') {\n    contentStart += 2\n  } // Suffix whitespace, part of the closing.\n\n  if (\n    contentEnd - 2 > contentStart &&\n    events[contentEnd][1].type === 'whitespace'\n  ) {\n    contentEnd -= 2\n  }\n\n  if (\n    events[contentEnd][1].type === 'atxHeadingSequence' &&\n    (contentStart === contentEnd - 1 ||\n      (contentEnd - 4 > contentStart &&\n        events[contentEnd - 2][1].type === 'whitespace'))\n  ) {\n    contentEnd -= contentStart + 1 === contentEnd ? 2 : 4\n  }\n\n  if (contentEnd > contentStart) {\n    content = {\n      type: 'atxHeadingText',\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end\n    }\n    text = {\n      type: 'chunkText',\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end,\n      contentType: 'text'\n    }\n    chunkedSplice(events, contentStart, contentEnd - contentStart + 1, [\n      ['enter', content, context],\n      ['enter', text, context],\n      ['exit', text, context],\n      ['exit', content, context]\n    ])\n  }\n\n  return events\n}\n\nfunction tokenizeHeadingAtx(effects, ok, nok) {\n  var self = this\n  var size = 0\n  return start\n\n  function start(code) {\n    effects.enter('atxHeading')\n    effects.enter('atxHeadingSequence')\n    return fenceOpenInside(code)\n  }\n\n  function fenceOpenInside(code) {\n    if (code === 35 && size++ < 6) {\n      effects.consume(code)\n      return fenceOpenInside\n    }\n\n    if (code === null || markdownLineEndingOrSpace(code)) {\n      effects.exit('atxHeadingSequence')\n      return self.interrupt ? ok(code) : headingBreak(code)\n    }\n\n    return nok(code)\n  }\n\n  function headingBreak(code) {\n    if (code === 35) {\n      effects.enter('atxHeadingSequence')\n      return sequence(code)\n    }\n\n    if (code === null || markdownLineEnding(code)) {\n      effects.exit('atxHeading')\n      return ok(code)\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(effects, headingBreak, 'whitespace')(code)\n    }\n\n    effects.enter('atxHeadingText')\n    return data(code)\n  }\n\n  function sequence(code) {\n    if (code === 35) {\n      effects.consume(code)\n      return sequence\n    }\n\n    effects.exit('atxHeadingSequence')\n    return headingBreak(code)\n  }\n\n  function data(code) {\n    if (code === null || code === 35 || markdownLineEndingOrSpace(code)) {\n      effects.exit('atxHeadingText')\n      return headingBreak(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n}\n\nmodule.exports = headingAtx\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/heading-atx.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/html-flow.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/html-flow.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar asciiAlpha = __webpack_require__(/*! ../character/ascii-alpha.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-alpha.js\")\nvar asciiAlphanumeric = __webpack_require__(/*! ../character/ascii-alphanumeric.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-alphanumeric.js\")\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar markdownLineEndingOrSpace = __webpack_require__(/*! ../character/markdown-line-ending-or-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending-or-space.js\")\nvar markdownSpace = __webpack_require__(/*! ../character/markdown-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-space.js\")\nvar fromCharCode = __webpack_require__(/*! ../constant/from-char-code.js */ \"(ssr)/./node_modules/micromark/dist/constant/from-char-code.js\")\nvar htmlBlockNames = __webpack_require__(/*! ../constant/html-block-names.js */ \"(ssr)/./node_modules/micromark/dist/constant/html-block-names.js\")\nvar htmlRawNames = __webpack_require__(/*! ../constant/html-raw-names.js */ \"(ssr)/./node_modules/micromark/dist/constant/html-raw-names.js\")\nvar partialBlankLine = __webpack_require__(/*! ./partial-blank-line.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/partial-blank-line.js\")\n\nvar htmlFlow = {\n  name: 'htmlFlow',\n  tokenize: tokenizeHtmlFlow,\n  resolveTo: resolveToHtmlFlow,\n  concrete: true\n}\nvar nextBlankConstruct = {\n  tokenize: tokenizeNextBlank,\n  partial: true\n}\n\nfunction resolveToHtmlFlow(events) {\n  var index = events.length\n\n  while (index--) {\n    if (events[index][0] === 'enter' && events[index][1].type === 'htmlFlow') {\n      break\n    }\n  }\n\n  if (index > 1 && events[index - 2][1].type === 'linePrefix') {\n    // Add the prefix start to the HTML token.\n    events[index][1].start = events[index - 2][1].start // Add the prefix start to the HTML line token.\n\n    events[index + 1][1].start = events[index - 2][1].start // Remove the line prefix.\n\n    events.splice(index - 2, 2)\n  }\n\n  return events\n}\n\nfunction tokenizeHtmlFlow(effects, ok, nok) {\n  var self = this\n  var kind\n  var startTag\n  var buffer\n  var index\n  var marker\n  return start\n\n  function start(code) {\n    effects.enter('htmlFlow')\n    effects.enter('htmlFlowData')\n    effects.consume(code)\n    return open\n  }\n\n  function open(code) {\n    if (code === 33) {\n      effects.consume(code)\n      return declarationStart\n    }\n\n    if (code === 47) {\n      effects.consume(code)\n      return tagCloseStart\n    }\n\n    if (code === 63) {\n      effects.consume(code)\n      kind = 3 // While we’re in an instruction instead of a declaration, we’re on a `?`\n      // right now, so we do need to search for `>`, similar to declarations.\n\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      buffer = fromCharCode(code)\n      startTag = true\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  function declarationStart(code) {\n    if (code === 45) {\n      effects.consume(code)\n      kind = 2\n      return commentOpenInside\n    }\n\n    if (code === 91) {\n      effects.consume(code)\n      kind = 5\n      buffer = 'CDATA['\n      index = 0\n      return cdataOpenInside\n    }\n\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      kind = 4\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  function commentOpenInside(code) {\n    if (code === 45) {\n      effects.consume(code)\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  function cdataOpenInside(code) {\n    if (code === buffer.charCodeAt(index++)) {\n      effects.consume(code)\n      return index === buffer.length\n        ? self.interrupt\n          ? ok\n          : continuation\n        : cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  function tagCloseStart(code) {\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      buffer = fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  function tagName(code) {\n    if (\n      code === null ||\n      code === 47 ||\n      code === 62 ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      if (\n        code !== 47 &&\n        startTag &&\n        htmlRawNames.indexOf(buffer.toLowerCase()) > -1\n      ) {\n        kind = 1\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      if (htmlBlockNames.indexOf(buffer.toLowerCase()) > -1) {\n        kind = 6\n\n        if (code === 47) {\n          effects.consume(code)\n          return basicSelfClosing\n        }\n\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      kind = 7 // Do not support complete HTML when interrupting.\n\n      return self.interrupt\n        ? nok(code)\n        : startTag\n        ? completeAttributeNameBefore(code)\n        : completeClosingTagAfter(code)\n    }\n\n    if (code === 45 || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      buffer += fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  function basicSelfClosing(code) {\n    if (code === 62) {\n      effects.consume(code)\n      return self.interrupt ? ok : continuation\n    }\n\n    return nok(code)\n  }\n\n  function completeClosingTagAfter(code) {\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeClosingTagAfter\n    }\n\n    return completeEnd(code)\n  }\n\n  function completeAttributeNameBefore(code) {\n    if (code === 47) {\n      effects.consume(code)\n      return completeEnd\n    }\n\n    if (code === 58 || code === 95 || asciiAlpha(code)) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeNameBefore\n    }\n\n    return completeEnd(code)\n  }\n\n  function completeAttributeName(code) {\n    if (\n      code === 45 ||\n      code === 46 ||\n      code === 58 ||\n      code === 95 ||\n      asciiAlphanumeric(code)\n    ) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    return completeAttributeNameAfter(code)\n  }\n\n  function completeAttributeNameAfter(code) {\n    if (code === 61) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeNameAfter\n    }\n\n    return completeAttributeNameBefore(code)\n  }\n\n  function completeAttributeValueBefore(code) {\n    if (\n      code === null ||\n      code === 60 ||\n      code === 61 ||\n      code === 62 ||\n      code === 96\n    ) {\n      return nok(code)\n    }\n\n    if (code === 34 || code === 39) {\n      effects.consume(code)\n      marker = code\n      return completeAttributeValueQuoted\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    marker = undefined\n    return completeAttributeValueUnquoted(code)\n  }\n\n  function completeAttributeValueQuoted(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return completeAttributeValueQuotedAfter\n    }\n\n    if (code === null || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueQuoted\n  }\n\n  function completeAttributeValueUnquoted(code) {\n    if (\n      code === null ||\n      code === 34 ||\n      code === 39 ||\n      code === 60 ||\n      code === 61 ||\n      code === 62 ||\n      code === 96 ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return completeAttributeNameAfter(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueUnquoted\n  }\n\n  function completeAttributeValueQuotedAfter(code) {\n    if (code === 47 || code === 62 || markdownSpace(code)) {\n      return completeAttributeNameBefore(code)\n    }\n\n    return nok(code)\n  }\n\n  function completeEnd(code) {\n    if (code === 62) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  function completeAfter(code) {\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return code === null || markdownLineEnding(code)\n      ? continuation(code)\n      : nok(code)\n  }\n\n  function continuation(code) {\n    if (code === 45 && kind === 2) {\n      effects.consume(code)\n      return continuationCommentInside\n    }\n\n    if (code === 60 && kind === 1) {\n      effects.consume(code)\n      return continuationRawTagOpen\n    }\n\n    if (code === 62 && kind === 4) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    if (code === 63 && kind === 3) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    if (code === 93 && kind === 5) {\n      effects.consume(code)\n      return continuationCharacterDataInside\n    }\n\n    if (markdownLineEnding(code) && (kind === 6 || kind === 7)) {\n      return effects.check(\n        nextBlankConstruct,\n        continuationClose,\n        continuationAtLineEnding\n      )(code)\n    }\n\n    if (code === null || markdownLineEnding(code)) {\n      return continuationAtLineEnding(code)\n    }\n\n    effects.consume(code)\n    return continuation\n  }\n\n  function continuationAtLineEnding(code) {\n    effects.exit('htmlFlowData')\n    return htmlContinueStart(code)\n  }\n\n  function htmlContinueStart(code) {\n    if (code === null) {\n      return done(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter('lineEnding')\n      effects.consume(code)\n      effects.exit('lineEnding')\n      return htmlContinueStart\n    }\n\n    effects.enter('htmlFlowData')\n    return continuation(code)\n  }\n\n  function continuationCommentInside(code) {\n    if (code === 45) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  function continuationRawTagOpen(code) {\n    if (code === 47) {\n      effects.consume(code)\n      buffer = ''\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  function continuationRawEndTag(code) {\n    if (code === 62 && htmlRawNames.indexOf(buffer.toLowerCase()) > -1) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    if (asciiAlpha(code) && buffer.length < 8) {\n      effects.consume(code)\n      buffer += fromCharCode(code)\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  function continuationCharacterDataInside(code) {\n    if (code === 93) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  function continuationDeclarationInside(code) {\n    if (code === 62) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    return continuation(code)\n  }\n\n  function continuationClose(code) {\n    if (code === null || markdownLineEnding(code)) {\n      effects.exit('htmlFlowData')\n      return done(code)\n    }\n\n    effects.consume(code)\n    return continuationClose\n  }\n\n  function done(code) {\n    effects.exit('htmlFlow')\n    return ok(code)\n  }\n}\n\nfunction tokenizeNextBlank(effects, ok, nok) {\n  return start\n\n  function start(code) {\n    effects.exit('htmlFlowData')\n    effects.enter('lineEndingBlank')\n    effects.consume(code)\n    effects.exit('lineEndingBlank')\n    return effects.attempt(partialBlankLine, ok, nok)\n  }\n}\n\nmodule.exports = htmlFlow\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/html-flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/html-text.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/html-text.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar asciiAlpha = __webpack_require__(/*! ../character/ascii-alpha.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-alpha.js\")\nvar asciiAlphanumeric = __webpack_require__(/*! ../character/ascii-alphanumeric.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-alphanumeric.js\")\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar markdownLineEndingOrSpace = __webpack_require__(/*! ../character/markdown-line-ending-or-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending-or-space.js\")\nvar markdownSpace = __webpack_require__(/*! ../character/markdown-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-space.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nvar htmlText = {\n  name: 'htmlText',\n  tokenize: tokenizeHtmlText\n}\n\nfunction tokenizeHtmlText(effects, ok, nok) {\n  var self = this\n  var marker\n  var buffer\n  var index\n  var returnState\n  return start\n\n  function start(code) {\n    effects.enter('htmlText')\n    effects.enter('htmlTextData')\n    effects.consume(code)\n    return open\n  }\n\n  function open(code) {\n    if (code === 33) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === 47) {\n      effects.consume(code)\n      return tagCloseStart\n    }\n\n    if (code === 63) {\n      effects.consume(code)\n      return instruction\n    }\n\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    return nok(code)\n  }\n\n  function declarationOpen(code) {\n    if (code === 45) {\n      effects.consume(code)\n      return commentOpen\n    }\n\n    if (code === 91) {\n      effects.consume(code)\n      buffer = 'CDATA['\n      index = 0\n      return cdataOpen\n    }\n\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return declaration\n    }\n\n    return nok(code)\n  }\n\n  function commentOpen(code) {\n    if (code === 45) {\n      effects.consume(code)\n      return commentStart\n    }\n\n    return nok(code)\n  }\n\n  function commentStart(code) {\n    if (code === null || code === 62) {\n      return nok(code)\n    }\n\n    if (code === 45) {\n      effects.consume(code)\n      return commentStartDash\n    }\n\n    return comment(code)\n  }\n\n  function commentStartDash(code) {\n    if (code === null || code === 62) {\n      return nok(code)\n    }\n\n    return comment(code)\n  }\n\n  function comment(code) {\n    if (code === null) {\n      return nok(code)\n    }\n\n    if (code === 45) {\n      effects.consume(code)\n      return commentClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = comment\n      return atLineEnding(code)\n    }\n\n    effects.consume(code)\n    return comment\n  }\n\n  function commentClose(code) {\n    if (code === 45) {\n      effects.consume(code)\n      return end\n    }\n\n    return comment(code)\n  }\n\n  function cdataOpen(code) {\n    if (code === buffer.charCodeAt(index++)) {\n      effects.consume(code)\n      return index === buffer.length ? cdata : cdataOpen\n    }\n\n    return nok(code)\n  }\n\n  function cdata(code) {\n    if (code === null) {\n      return nok(code)\n    }\n\n    if (code === 93) {\n      effects.consume(code)\n      return cdataClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = cdata\n      return atLineEnding(code)\n    }\n\n    effects.consume(code)\n    return cdata\n  }\n\n  function cdataClose(code) {\n    if (code === 93) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  function cdataEnd(code) {\n    if (code === 62) {\n      return end(code)\n    }\n\n    if (code === 93) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  function declaration(code) {\n    if (code === null || code === 62) {\n      return end(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = declaration\n      return atLineEnding(code)\n    }\n\n    effects.consume(code)\n    return declaration\n  }\n\n  function instruction(code) {\n    if (code === null) {\n      return nok(code)\n    }\n\n    if (code === 63) {\n      effects.consume(code)\n      return instructionClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = instruction\n      return atLineEnding(code)\n    }\n\n    effects.consume(code)\n    return instruction\n  }\n\n  function instructionClose(code) {\n    return code === 62 ? end(code) : instruction(code)\n  }\n\n  function tagCloseStart(code) {\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return nok(code)\n  }\n\n  function tagClose(code) {\n    if (code === 45 || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return tagCloseBetween(code)\n  }\n\n  function tagCloseBetween(code) {\n    if (markdownLineEnding(code)) {\n      returnState = tagCloseBetween\n      return atLineEnding(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagCloseBetween\n    }\n\n    return end(code)\n  }\n\n  function tagOpen(code) {\n    if (code === 45 || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    if (code === 47 || code === 62 || markdownLineEndingOrSpace(code)) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  function tagOpenBetween(code) {\n    if (code === 47) {\n      effects.consume(code)\n      return end\n    }\n\n    if (code === 58 || code === 95 || asciiAlpha(code)) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenBetween\n      return atLineEnding(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenBetween\n    }\n\n    return end(code)\n  }\n\n  function tagOpenAttributeName(code) {\n    if (\n      code === 45 ||\n      code === 46 ||\n      code === 58 ||\n      code === 95 ||\n      asciiAlphanumeric(code)\n    ) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    return tagOpenAttributeNameAfter(code)\n  }\n\n  function tagOpenAttributeNameAfter(code) {\n    if (code === 61) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeNameAfter\n      return atLineEnding(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenAttributeNameAfter\n    }\n\n    return tagOpenBetween(code)\n  }\n\n  function tagOpenAttributeValueBefore(code) {\n    if (\n      code === null ||\n      code === 60 ||\n      code === 61 ||\n      code === 62 ||\n      code === 96\n    ) {\n      return nok(code)\n    }\n\n    if (code === 34 || code === 39) {\n      effects.consume(code)\n      marker = code\n      return tagOpenAttributeValueQuoted\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueBefore\n      return atLineEnding(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    effects.consume(code)\n    marker = undefined\n    return tagOpenAttributeValueUnquoted\n  }\n\n  function tagOpenAttributeValueQuoted(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return tagOpenAttributeValueQuotedAfter\n    }\n\n    if (code === null) {\n      return nok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueQuoted\n      return atLineEnding(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueQuoted\n  }\n\n  function tagOpenAttributeValueQuotedAfter(code) {\n    if (code === 62 || code === 47 || markdownLineEndingOrSpace(code)) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  function tagOpenAttributeValueUnquoted(code) {\n    if (\n      code === null ||\n      code === 34 ||\n      code === 39 ||\n      code === 60 ||\n      code === 61 ||\n      code === 96\n    ) {\n      return nok(code)\n    }\n\n    if (code === 62 || markdownLineEndingOrSpace(code)) {\n      return tagOpenBetween(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  } // We can’t have blank lines in content, so no need to worry about empty\n  // tokens.\n\n  function atLineEnding(code) {\n    effects.exit('htmlTextData')\n    effects.enter('lineEnding')\n    effects.consume(code)\n    effects.exit('lineEnding')\n    return factorySpace(\n      effects,\n      afterPrefix,\n      'linePrefix',\n      self.parser.constructs.disable.null.indexOf('codeIndented') > -1\n        ? undefined\n        : 4\n    )\n  }\n\n  function afterPrefix(code) {\n    effects.enter('htmlTextData')\n    return returnState(code)\n  }\n\n  function end(code) {\n    if (code === 62) {\n      effects.consume(code)\n      effects.exit('htmlTextData')\n      effects.exit('htmlText')\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n\nmodule.exports = htmlText\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdG9rZW5pemUvaHRtbC10ZXh0LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGlCQUFpQixtQkFBTyxDQUFDLGlHQUE2QjtBQUN0RCx3QkFBd0IsbUJBQU8sQ0FBQywrR0FBb0M7QUFDcEUseUJBQXlCLG1CQUFPLENBQUMsbUhBQXNDO0FBQ3ZFLGdDQUFnQyxtQkFBTyxDQUFDLHFJQUErQztBQUN2RixvQkFBb0IsbUJBQU8sQ0FBQyx1R0FBZ0M7QUFDNUQsbUJBQW1CLG1CQUFPLENBQUMseUZBQW9COztBQUUvQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L3Rva2VuaXplL2h0bWwtdGV4dC5qcz9lNmUxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgYXNjaWlBbHBoYSA9IHJlcXVpcmUoJy4uL2NoYXJhY3Rlci9hc2NpaS1hbHBoYS5qcycpXG52YXIgYXNjaWlBbHBoYW51bWVyaWMgPSByZXF1aXJlKCcuLi9jaGFyYWN0ZXIvYXNjaWktYWxwaGFudW1lcmljLmpzJylcbnZhciBtYXJrZG93bkxpbmVFbmRpbmcgPSByZXF1aXJlKCcuLi9jaGFyYWN0ZXIvbWFya2Rvd24tbGluZS1lbmRpbmcuanMnKVxudmFyIG1hcmtkb3duTGluZUVuZGluZ09yU3BhY2UgPSByZXF1aXJlKCcuLi9jaGFyYWN0ZXIvbWFya2Rvd24tbGluZS1lbmRpbmctb3Itc3BhY2UuanMnKVxudmFyIG1hcmtkb3duU3BhY2UgPSByZXF1aXJlKCcuLi9jaGFyYWN0ZXIvbWFya2Rvd24tc3BhY2UuanMnKVxudmFyIGZhY3RvcnlTcGFjZSA9IHJlcXVpcmUoJy4vZmFjdG9yeS1zcGFjZS5qcycpXG5cbnZhciBodG1sVGV4dCA9IHtcbiAgbmFtZTogJ2h0bWxUZXh0JyxcbiAgdG9rZW5pemU6IHRva2VuaXplSHRtbFRleHRcbn1cblxuZnVuY3Rpb24gdG9rZW5pemVIdG1sVGV4dChlZmZlY3RzLCBvaywgbm9rKSB7XG4gIHZhciBzZWxmID0gdGhpc1xuICB2YXIgbWFya2VyXG4gIHZhciBidWZmZXJcbiAgdmFyIGluZGV4XG4gIHZhciByZXR1cm5TdGF0ZVxuICByZXR1cm4gc3RhcnRcblxuICBmdW5jdGlvbiBzdGFydChjb2RlKSB7XG4gICAgZWZmZWN0cy5lbnRlcignaHRtbFRleHQnKVxuICAgIGVmZmVjdHMuZW50ZXIoJ2h0bWxUZXh0RGF0YScpXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgcmV0dXJuIG9wZW5cbiAgfVxuXG4gIGZ1bmN0aW9uIG9wZW4oY29kZSkge1xuICAgIGlmIChjb2RlID09PSAzMykge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gZGVjbGFyYXRpb25PcGVuXG4gICAgfVxuXG4gICAgaWYgKGNvZGUgPT09IDQ3KSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiB0YWdDbG9zZVN0YXJ0XG4gICAgfVxuXG4gICAgaWYgKGNvZGUgPT09IDYzKSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBpbnN0cnVjdGlvblxuICAgIH1cblxuICAgIGlmIChhc2NpaUFscGhhKGNvZGUpKSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiB0YWdPcGVuXG4gICAgfVxuXG4gICAgcmV0dXJuIG5vayhjb2RlKVxuICB9XG5cbiAgZnVuY3Rpb24gZGVjbGFyYXRpb25PcGVuKGNvZGUpIHtcbiAgICBpZiAoY29kZSA9PT0gNDUpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIGNvbW1lbnRPcGVuXG4gICAgfVxuXG4gICAgaWYgKGNvZGUgPT09IDkxKSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIGJ1ZmZlciA9ICdDREFUQVsnXG4gICAgICBpbmRleCA9IDBcbiAgICAgIHJldHVybiBjZGF0YU9wZW5cbiAgICB9XG5cbiAgICBpZiAoYXNjaWlBbHBoYShjb2RlKSkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gZGVjbGFyYXRpb25cbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cblxuICBmdW5jdGlvbiBjb21tZW50T3Blbihjb2RlKSB7XG4gICAgaWYgKGNvZGUgPT09IDQ1KSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBjb21tZW50U3RhcnRcbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cblxuICBmdW5jdGlvbiBjb21tZW50U3RhcnQoY29kZSkge1xuICAgIGlmIChjb2RlID09PSBudWxsIHx8IGNvZGUgPT09IDYyKSB7XG4gICAgICByZXR1cm4gbm9rKGNvZGUpXG4gICAgfVxuXG4gICAgaWYgKGNvZGUgPT09IDQ1KSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBjb21tZW50U3RhcnREYXNoXG4gICAgfVxuXG4gICAgcmV0dXJuIGNvbW1lbnQoY29kZSlcbiAgfVxuXG4gIGZ1bmN0aW9uIGNvbW1lbnRTdGFydERhc2goY29kZSkge1xuICAgIGlmIChjb2RlID09PSBudWxsIHx8IGNvZGUgPT09IDYyKSB7XG4gICAgICByZXR1cm4gbm9rKGNvZGUpXG4gICAgfVxuXG4gICAgcmV0dXJuIGNvbW1lbnQoY29kZSlcbiAgfVxuXG4gIGZ1bmN0aW9uIGNvbW1lbnQoY29kZSkge1xuICAgIGlmIChjb2RlID09PSBudWxsKSB7XG4gICAgICByZXR1cm4gbm9rKGNvZGUpXG4gICAgfVxuXG4gICAgaWYgKGNvZGUgPT09IDQ1KSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBjb21tZW50Q2xvc2VcbiAgICB9XG5cbiAgICBpZiAobWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpKSB7XG4gICAgICByZXR1cm5TdGF0ZSA9IGNvbW1lbnRcbiAgICAgIHJldHVybiBhdExpbmVFbmRpbmcoY29kZSlcbiAgICB9XG5cbiAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICByZXR1cm4gY29tbWVudFxuICB9XG5cbiAgZnVuY3Rpb24gY29tbWVudENsb3NlKGNvZGUpIHtcbiAgICBpZiAoY29kZSA9PT0gNDUpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIGVuZFxuICAgIH1cblxuICAgIHJldHVybiBjb21tZW50KGNvZGUpXG4gIH1cblxuICBmdW5jdGlvbiBjZGF0YU9wZW4oY29kZSkge1xuICAgIGlmIChjb2RlID09PSBidWZmZXIuY2hhckNvZGVBdChpbmRleCsrKSkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gaW5kZXggPT09IGJ1ZmZlci5sZW5ndGggPyBjZGF0YSA6IGNkYXRhT3BlblxuICAgIH1cblxuICAgIHJldHVybiBub2soY29kZSlcbiAgfVxuXG4gIGZ1bmN0aW9uIGNkYXRhKGNvZGUpIHtcbiAgICBpZiAoY29kZSA9PT0gbnVsbCkge1xuICAgICAgcmV0dXJuIG5vayhjb2RlKVxuICAgIH1cblxuICAgIGlmIChjb2RlID09PSA5Mykge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gY2RhdGFDbG9zZVxuICAgIH1cblxuICAgIGlmIChtYXJrZG93bkxpbmVFbmRpbmcoY29kZSkpIHtcbiAgICAgIHJldHVyblN0YXRlID0gY2RhdGFcbiAgICAgIHJldHVybiBhdExpbmVFbmRpbmcoY29kZSlcbiAgICB9XG5cbiAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICByZXR1cm4gY2RhdGFcbiAgfVxuXG4gIGZ1bmN0aW9uIGNkYXRhQ2xvc2UoY29kZSkge1xuICAgIGlmIChjb2RlID09PSA5Mykge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gY2RhdGFFbmRcbiAgICB9XG5cbiAgICByZXR1cm4gY2RhdGEoY29kZSlcbiAgfVxuXG4gIGZ1bmN0aW9uIGNkYXRhRW5kKGNvZGUpIHtcbiAgICBpZiAoY29kZSA9PT0gNjIpIHtcbiAgICAgIHJldHVybiBlbmQoY29kZSlcbiAgICB9XG5cbiAgICBpZiAoY29kZSA9PT0gOTMpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIGNkYXRhRW5kXG4gICAgfVxuXG4gICAgcmV0dXJuIGNkYXRhKGNvZGUpXG4gIH1cblxuICBmdW5jdGlvbiBkZWNsYXJhdGlvbihjb2RlKSB7XG4gICAgaWYgKGNvZGUgPT09IG51bGwgfHwgY29kZSA9PT0gNjIpIHtcbiAgICAgIHJldHVybiBlbmQoY29kZSlcbiAgICB9XG5cbiAgICBpZiAobWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpKSB7XG4gICAgICByZXR1cm5TdGF0ZSA9IGRlY2xhcmF0aW9uXG4gICAgICByZXR1cm4gYXRMaW5lRW5kaW5nKGNvZGUpXG4gICAgfVxuXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgcmV0dXJuIGRlY2xhcmF0aW9uXG4gIH1cblxuICBmdW5jdGlvbiBpbnN0cnVjdGlvbihjb2RlKSB7XG4gICAgaWYgKGNvZGUgPT09IG51bGwpIHtcbiAgICAgIHJldHVybiBub2soY29kZSlcbiAgICB9XG5cbiAgICBpZiAoY29kZSA9PT0gNjMpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIGluc3RydWN0aW9uQ2xvc2VcbiAgICB9XG5cbiAgICBpZiAobWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpKSB7XG4gICAgICByZXR1cm5TdGF0ZSA9IGluc3RydWN0aW9uXG4gICAgICByZXR1cm4gYXRMaW5lRW5kaW5nKGNvZGUpXG4gICAgfVxuXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgcmV0dXJuIGluc3RydWN0aW9uXG4gIH1cblxuICBmdW5jdGlvbiBpbnN0cnVjdGlvbkNsb3NlKGNvZGUpIHtcbiAgICByZXR1cm4gY29kZSA9PT0gNjIgPyBlbmQoY29kZSkgOiBpbnN0cnVjdGlvbihjb2RlKVxuICB9XG5cbiAgZnVuY3Rpb24gdGFnQ2xvc2VTdGFydChjb2RlKSB7XG4gICAgaWYgKGFzY2lpQWxwaGEoY29kZSkpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIHRhZ0Nsb3NlXG4gICAgfVxuXG4gICAgcmV0dXJuIG5vayhjb2RlKVxuICB9XG5cbiAgZnVuY3Rpb24gdGFnQ2xvc2UoY29kZSkge1xuICAgIGlmIChjb2RlID09PSA0NSB8fCBhc2NpaUFscGhhbnVtZXJpYyhjb2RlKSkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gdGFnQ2xvc2VcbiAgICB9XG5cbiAgICByZXR1cm4gdGFnQ2xvc2VCZXR3ZWVuKGNvZGUpXG4gIH1cblxuICBmdW5jdGlvbiB0YWdDbG9zZUJldHdlZW4oY29kZSkge1xuICAgIGlmIChtYXJrZG93bkxpbmVFbmRpbmcoY29kZSkpIHtcbiAgICAgIHJldHVyblN0YXRlID0gdGFnQ2xvc2VCZXR3ZWVuXG4gICAgICByZXR1cm4gYXRMaW5lRW5kaW5nKGNvZGUpXG4gICAgfVxuXG4gICAgaWYgKG1hcmtkb3duU3BhY2UoY29kZSkpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIHRhZ0Nsb3NlQmV0d2VlblxuICAgIH1cblxuICAgIHJldHVybiBlbmQoY29kZSlcbiAgfVxuXG4gIGZ1bmN0aW9uIHRhZ09wZW4oY29kZSkge1xuICAgIGlmIChjb2RlID09PSA0NSB8fCBhc2NpaUFscGhhbnVtZXJpYyhjb2RlKSkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gdGFnT3BlblxuICAgIH1cblxuICAgIGlmIChjb2RlID09PSA0NyB8fCBjb2RlID09PSA2MiB8fCBtYXJrZG93bkxpbmVFbmRpbmdPclNwYWNlKGNvZGUpKSB7XG4gICAgICByZXR1cm4gdGFnT3BlbkJldHdlZW4oY29kZSlcbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cblxuICBmdW5jdGlvbiB0YWdPcGVuQmV0d2Vlbihjb2RlKSB7XG4gICAgaWYgKGNvZGUgPT09IDQ3KSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBlbmRcbiAgICB9XG5cbiAgICBpZiAoY29kZSA9PT0gNTggfHwgY29kZSA9PT0gOTUgfHwgYXNjaWlBbHBoYShjb2RlKSkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gdGFnT3BlbkF0dHJpYnV0ZU5hbWVcbiAgICB9XG5cbiAgICBpZiAobWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpKSB7XG4gICAgICByZXR1cm5TdGF0ZSA9IHRhZ09wZW5CZXR3ZWVuXG4gICAgICByZXR1cm4gYXRMaW5lRW5kaW5nKGNvZGUpXG4gICAgfVxuXG4gICAgaWYgKG1hcmtkb3duU3BhY2UoY29kZSkpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIHRhZ09wZW5CZXR3ZWVuXG4gICAgfVxuXG4gICAgcmV0dXJuIGVuZChjb2RlKVxuICB9XG5cbiAgZnVuY3Rpb24gdGFnT3BlbkF0dHJpYnV0ZU5hbWUoY29kZSkge1xuICAgIGlmIChcbiAgICAgIGNvZGUgPT09IDQ1IHx8XG4gICAgICBjb2RlID09PSA0NiB8fFxuICAgICAgY29kZSA9PT0gNTggfHxcbiAgICAgIGNvZGUgPT09IDk1IHx8XG4gICAgICBhc2NpaUFscGhhbnVtZXJpYyhjb2RlKVxuICAgICkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gdGFnT3BlbkF0dHJpYnV0ZU5hbWVcbiAgICB9XG5cbiAgICByZXR1cm4gdGFnT3BlbkF0dHJpYnV0ZU5hbWVBZnRlcihjb2RlKVxuICB9XG5cbiAgZnVuY3Rpb24gdGFnT3BlbkF0dHJpYnV0ZU5hbWVBZnRlcihjb2RlKSB7XG4gICAgaWYgKGNvZGUgPT09IDYxKSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiB0YWdPcGVuQXR0cmlidXRlVmFsdWVCZWZvcmVcbiAgICB9XG5cbiAgICBpZiAobWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpKSB7XG4gICAgICByZXR1cm5TdGF0ZSA9IHRhZ09wZW5BdHRyaWJ1dGVOYW1lQWZ0ZXJcbiAgICAgIHJldHVybiBhdExpbmVFbmRpbmcoY29kZSlcbiAgICB9XG5cbiAgICBpZiAobWFya2Rvd25TcGFjZShjb2RlKSkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gdGFnT3BlbkF0dHJpYnV0ZU5hbWVBZnRlclxuICAgIH1cblxuICAgIHJldHVybiB0YWdPcGVuQmV0d2Vlbihjb2RlKVxuICB9XG5cbiAgZnVuY3Rpb24gdGFnT3BlbkF0dHJpYnV0ZVZhbHVlQmVmb3JlKGNvZGUpIHtcbiAgICBpZiAoXG4gICAgICBjb2RlID09PSBudWxsIHx8XG4gICAgICBjb2RlID09PSA2MCB8fFxuICAgICAgY29kZSA9PT0gNjEgfHxcbiAgICAgIGNvZGUgPT09IDYyIHx8XG4gICAgICBjb2RlID09PSA5NlxuICAgICkge1xuICAgICAgcmV0dXJuIG5vayhjb2RlKVxuICAgIH1cblxuICAgIGlmIChjb2RlID09PSAzNCB8fCBjb2RlID09PSAzOSkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICBtYXJrZXIgPSBjb2RlXG4gICAgICByZXR1cm4gdGFnT3BlbkF0dHJpYnV0ZVZhbHVlUXVvdGVkXG4gICAgfVxuXG4gICAgaWYgKG1hcmtkb3duTGluZUVuZGluZyhjb2RlKSkge1xuICAgICAgcmV0dXJuU3RhdGUgPSB0YWdPcGVuQXR0cmlidXRlVmFsdWVCZWZvcmVcbiAgICAgIHJldHVybiBhdExpbmVFbmRpbmcoY29kZSlcbiAgICB9XG5cbiAgICBpZiAobWFya2Rvd25TcGFjZShjb2RlKSkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm4gdGFnT3BlbkF0dHJpYnV0ZVZhbHVlQmVmb3JlXG4gICAgfVxuXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgbWFya2VyID0gdW5kZWZpbmVkXG4gICAgcmV0dXJuIHRhZ09wZW5BdHRyaWJ1dGVWYWx1ZVVucXVvdGVkXG4gIH1cblxuICBmdW5jdGlvbiB0YWdPcGVuQXR0cmlidXRlVmFsdWVRdW90ZWQoY29kZSkge1xuICAgIGlmIChjb2RlID09PSBtYXJrZXIpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIHRhZ09wZW5BdHRyaWJ1dGVWYWx1ZVF1b3RlZEFmdGVyXG4gICAgfVxuXG4gICAgaWYgKGNvZGUgPT09IG51bGwpIHtcbiAgICAgIHJldHVybiBub2soY29kZSlcbiAgICB9XG5cbiAgICBpZiAobWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpKSB7XG4gICAgICByZXR1cm5TdGF0ZSA9IHRhZ09wZW5BdHRyaWJ1dGVWYWx1ZVF1b3RlZFxuICAgICAgcmV0dXJuIGF0TGluZUVuZGluZyhjb2RlKVxuICAgIH1cblxuICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgIHJldHVybiB0YWdPcGVuQXR0cmlidXRlVmFsdWVRdW90ZWRcbiAgfVxuXG4gIGZ1bmN0aW9uIHRhZ09wZW5BdHRyaWJ1dGVWYWx1ZVF1b3RlZEFmdGVyKGNvZGUpIHtcbiAgICBpZiAoY29kZSA9PT0gNjIgfHwgY29kZSA9PT0gNDcgfHwgbWFya2Rvd25MaW5lRW5kaW5nT3JTcGFjZShjb2RlKSkge1xuICAgICAgcmV0dXJuIHRhZ09wZW5CZXR3ZWVuKGNvZGUpXG4gICAgfVxuXG4gICAgcmV0dXJuIG5vayhjb2RlKVxuICB9XG5cbiAgZnVuY3Rpb24gdGFnT3BlbkF0dHJpYnV0ZVZhbHVlVW5xdW90ZWQoY29kZSkge1xuICAgIGlmIChcbiAgICAgIGNvZGUgPT09IG51bGwgfHxcbiAgICAgIGNvZGUgPT09IDM0IHx8XG4gICAgICBjb2RlID09PSAzOSB8fFxuICAgICAgY29kZSA9PT0gNjAgfHxcbiAgICAgIGNvZGUgPT09IDYxIHx8XG4gICAgICBjb2RlID09PSA5NlxuICAgICkge1xuICAgICAgcmV0dXJuIG5vayhjb2RlKVxuICAgIH1cblxuICAgIGlmIChjb2RlID09PSA2MiB8fCBtYXJrZG93bkxpbmVFbmRpbmdPclNwYWNlKGNvZGUpKSB7XG4gICAgICByZXR1cm4gdGFnT3BlbkJldHdlZW4oY29kZSlcbiAgICB9XG5cbiAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICByZXR1cm4gdGFnT3BlbkF0dHJpYnV0ZVZhbHVlVW5xdW90ZWRcbiAgfSAvLyBXZSBjYW7igJl0IGhhdmUgYmxhbmsgbGluZXMgaW4gY29udGVudCwgc28gbm8gbmVlZCB0byB3b3JyeSBhYm91dCBlbXB0eVxuICAvLyB0b2tlbnMuXG5cbiAgZnVuY3Rpb24gYXRMaW5lRW5kaW5nKGNvZGUpIHtcbiAgICBlZmZlY3RzLmV4aXQoJ2h0bWxUZXh0RGF0YScpXG4gICAgZWZmZWN0cy5lbnRlcignbGluZUVuZGluZycpXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgZWZmZWN0cy5leGl0KCdsaW5lRW5kaW5nJylcbiAgICByZXR1cm4gZmFjdG9yeVNwYWNlKFxuICAgICAgZWZmZWN0cyxcbiAgICAgIGFmdGVyUHJlZml4LFxuICAgICAgJ2xpbmVQcmVmaXgnLFxuICAgICAgc2VsZi5wYXJzZXIuY29uc3RydWN0cy5kaXNhYmxlLm51bGwuaW5kZXhPZignY29kZUluZGVudGVkJykgPiAtMVxuICAgICAgICA/IHVuZGVmaW5lZFxuICAgICAgICA6IDRcbiAgICApXG4gIH1cblxuICBmdW5jdGlvbiBhZnRlclByZWZpeChjb2RlKSB7XG4gICAgZWZmZWN0cy5lbnRlcignaHRtbFRleHREYXRhJylcbiAgICByZXR1cm4gcmV0dXJuU3RhdGUoY29kZSlcbiAgfVxuXG4gIGZ1bmN0aW9uIGVuZChjb2RlKSB7XG4gICAgaWYgKGNvZGUgPT09IDYyKSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIGVmZmVjdHMuZXhpdCgnaHRtbFRleHREYXRhJylcbiAgICAgIGVmZmVjdHMuZXhpdCgnaHRtbFRleHQnKVxuICAgICAgcmV0dXJuIG9rXG4gICAgfVxuXG4gICAgcmV0dXJuIG5vayhjb2RlKVxuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gaHRtbFRleHRcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/html-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/label-end.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/label-end.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEndingOrSpace = __webpack_require__(/*! ../character/markdown-line-ending-or-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending-or-space.js\")\nvar chunkedPush = __webpack_require__(/*! ../util/chunked-push.js */ \"(ssr)/./node_modules/micromark/dist/util/chunked-push.js\")\nvar chunkedSplice = __webpack_require__(/*! ../util/chunked-splice.js */ \"(ssr)/./node_modules/micromark/dist/util/chunked-splice.js\")\nvar normalizeIdentifier = __webpack_require__(/*! ../util/normalize-identifier.js */ \"(ssr)/./node_modules/micromark/dist/util/normalize-identifier.js\")\nvar resolveAll = __webpack_require__(/*! ../util/resolve-all.js */ \"(ssr)/./node_modules/micromark/dist/util/resolve-all.js\")\nvar shallow = __webpack_require__(/*! ../util/shallow.js */ \"(ssr)/./node_modules/micromark/dist/util/shallow.js\")\nvar factoryDestination = __webpack_require__(/*! ./factory-destination.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-destination.js\")\nvar factoryLabel = __webpack_require__(/*! ./factory-label.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-label.js\")\nvar factoryTitle = __webpack_require__(/*! ./factory-title.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-title.js\")\nvar factoryWhitespace = __webpack_require__(/*! ./factory-whitespace.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-whitespace.js\")\n\nvar labelEnd = {\n  name: 'labelEnd',\n  tokenize: tokenizeLabelEnd,\n  resolveTo: resolveToLabelEnd,\n  resolveAll: resolveAllLabelEnd\n}\nvar resourceConstruct = {\n  tokenize: tokenizeResource\n}\nvar fullReferenceConstruct = {\n  tokenize: tokenizeFullReference\n}\nvar collapsedReferenceConstruct = {\n  tokenize: tokenizeCollapsedReference\n}\n\nfunction resolveAllLabelEnd(events) {\n  var index = -1\n  var token\n\n  while (++index < events.length) {\n    token = events[index][1]\n\n    if (\n      !token._used &&\n      (token.type === 'labelImage' ||\n        token.type === 'labelLink' ||\n        token.type === 'labelEnd')\n    ) {\n      // Remove the marker.\n      events.splice(index + 1, token.type === 'labelImage' ? 4 : 2)\n      token.type = 'data'\n      index++\n    }\n  }\n\n  return events\n}\n\nfunction resolveToLabelEnd(events, context) {\n  var index = events.length\n  var offset = 0\n  var group\n  var label\n  var text\n  var token\n  var open\n  var close\n  var media // Find an opening.\n\n  while (index--) {\n    token = events[index][1]\n\n    if (open) {\n      // If we see another link, or inactive link label, we’ve been here before.\n      if (\n        token.type === 'link' ||\n        (token.type === 'labelLink' && token._inactive)\n      ) {\n        break\n      } // Mark other link openings as inactive, as we can’t have links in\n      // links.\n\n      if (events[index][0] === 'enter' && token.type === 'labelLink') {\n        token._inactive = true\n      }\n    } else if (close) {\n      if (\n        events[index][0] === 'enter' &&\n        (token.type === 'labelImage' || token.type === 'labelLink') &&\n        !token._balanced\n      ) {\n        open = index\n\n        if (token.type !== 'labelLink') {\n          offset = 2\n          break\n        }\n      }\n    } else if (token.type === 'labelEnd') {\n      close = index\n    }\n  }\n\n  group = {\n    type: events[open][1].type === 'labelLink' ? 'link' : 'image',\n    start: shallow(events[open][1].start),\n    end: shallow(events[events.length - 1][1].end)\n  }\n  label = {\n    type: 'label',\n    start: shallow(events[open][1].start),\n    end: shallow(events[close][1].end)\n  }\n  text = {\n    type: 'labelText',\n    start: shallow(events[open + offset + 2][1].end),\n    end: shallow(events[close - 2][1].start)\n  }\n  media = [\n    ['enter', group, context],\n    ['enter', label, context]\n  ] // Opening marker.\n\n  media = chunkedPush(media, events.slice(open + 1, open + offset + 3)) // Text open.\n\n  media = chunkedPush(media, [['enter', text, context]]) // Between.\n\n  media = chunkedPush(\n    media,\n    resolveAll(\n      context.parser.constructs.insideSpan.null,\n      events.slice(open + offset + 4, close - 3),\n      context\n    )\n  ) // Text close, marker close, label close.\n\n  media = chunkedPush(media, [\n    ['exit', text, context],\n    events[close - 2],\n    events[close - 1],\n    ['exit', label, context]\n  ]) // Reference, resource, or so.\n\n  media = chunkedPush(media, events.slice(close + 1)) // Media close.\n\n  media = chunkedPush(media, [['exit', group, context]])\n  chunkedSplice(events, open, events.length, media)\n  return events\n}\n\nfunction tokenizeLabelEnd(effects, ok, nok) {\n  var self = this\n  var index = self.events.length\n  var labelStart\n  var defined // Find an opening.\n\n  while (index--) {\n    if (\n      (self.events[index][1].type === 'labelImage' ||\n        self.events[index][1].type === 'labelLink') &&\n      !self.events[index][1]._balanced\n    ) {\n      labelStart = self.events[index][1]\n      break\n    }\n  }\n\n  return start\n\n  function start(code) {\n    if (!labelStart) {\n      return nok(code)\n    } // It’s a balanced bracket, but contains a link.\n\n    if (labelStart._inactive) return balanced(code)\n    defined =\n      self.parser.defined.indexOf(\n        normalizeIdentifier(\n          self.sliceSerialize({\n            start: labelStart.end,\n            end: self.now()\n          })\n        )\n      ) > -1\n    effects.enter('labelEnd')\n    effects.enter('labelMarker')\n    effects.consume(code)\n    effects.exit('labelMarker')\n    effects.exit('labelEnd')\n    return afterLabelEnd\n  }\n\n  function afterLabelEnd(code) {\n    // Resource: `[asd](fgh)`.\n    if (code === 40) {\n      return effects.attempt(\n        resourceConstruct,\n        ok,\n        defined ? ok : balanced\n      )(code)\n    } // Collapsed (`[asd][]`) or full (`[asd][fgh]`) reference?\n\n    if (code === 91) {\n      return effects.attempt(\n        fullReferenceConstruct,\n        ok,\n        defined\n          ? effects.attempt(collapsedReferenceConstruct, ok, balanced)\n          : balanced\n      )(code)\n    } // Shortcut reference: `[asd]`?\n\n    return defined ? ok(code) : balanced(code)\n  }\n\n  function balanced(code) {\n    labelStart._balanced = true\n    return nok(code)\n  }\n}\n\nfunction tokenizeResource(effects, ok, nok) {\n  return start\n\n  function start(code) {\n    effects.enter('resource')\n    effects.enter('resourceMarker')\n    effects.consume(code)\n    effects.exit('resourceMarker')\n    return factoryWhitespace(effects, open)\n  }\n\n  function open(code) {\n    if (code === 41) {\n      return end(code)\n    }\n\n    return factoryDestination(\n      effects,\n      destinationAfter,\n      nok,\n      'resourceDestination',\n      'resourceDestinationLiteral',\n      'resourceDestinationLiteralMarker',\n      'resourceDestinationRaw',\n      'resourceDestinationString',\n      3\n    )(code)\n  }\n\n  function destinationAfter(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, between)(code)\n      : end(code)\n  }\n\n  function between(code) {\n    if (code === 34 || code === 39 || code === 40) {\n      return factoryTitle(\n        effects,\n        factoryWhitespace(effects, end),\n        nok,\n        'resourceTitle',\n        'resourceTitleMarker',\n        'resourceTitleString'\n      )(code)\n    }\n\n    return end(code)\n  }\n\n  function end(code) {\n    if (code === 41) {\n      effects.enter('resourceMarker')\n      effects.consume(code)\n      effects.exit('resourceMarker')\n      effects.exit('resource')\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n\nfunction tokenizeFullReference(effects, ok, nok) {\n  var self = this\n  return start\n\n  function start(code) {\n    return factoryLabel.call(\n      self,\n      effects,\n      afterLabel,\n      nok,\n      'reference',\n      'referenceMarker',\n      'referenceString'\n    )(code)\n  }\n\n  function afterLabel(code) {\n    return self.parser.defined.indexOf(\n      normalizeIdentifier(\n        self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n      )\n    ) < 0\n      ? nok(code)\n      : ok(code)\n  }\n}\n\nfunction tokenizeCollapsedReference(effects, ok, nok) {\n  return start\n\n  function start(code) {\n    effects.enter('reference')\n    effects.enter('referenceMarker')\n    effects.consume(code)\n    effects.exit('referenceMarker')\n    return open\n  }\n\n  function open(code) {\n    if (code === 93) {\n      effects.enter('referenceMarker')\n      effects.consume(code)\n      effects.exit('referenceMarker')\n      effects.exit('reference')\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n\nmodule.exports = labelEnd\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/label-end.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/label-start-image.js":
/*!*******************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/label-start-image.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar labelEnd = __webpack_require__(/*! ./label-end.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/label-end.js\")\n\nvar labelStartImage = {\n  name: 'labelStartImage',\n  tokenize: tokenizeLabelStartImage,\n  resolveAll: labelEnd.resolveAll\n}\n\nfunction tokenizeLabelStartImage(effects, ok, nok) {\n  var self = this\n  return start\n\n  function start(code) {\n    effects.enter('labelImage')\n    effects.enter('labelImageMarker')\n    effects.consume(code)\n    effects.exit('labelImageMarker')\n    return open\n  }\n\n  function open(code) {\n    if (code === 91) {\n      effects.enter('labelMarker')\n      effects.consume(code)\n      effects.exit('labelMarker')\n      effects.exit('labelImage')\n      return after\n    }\n\n    return nok(code)\n  }\n\n  function after(code) {\n    /* c8 ignore next */\n    return code === 94 &&\n      /* c8 ignore next */\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? /* c8 ignore next */\n        nok(code)\n      : ok(code)\n  }\n}\n\nmodule.exports = labelStartImage\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdG9rZW5pemUvbGFiZWwtc3RhcnQtaW1hZ2UuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZUFBZSxtQkFBTyxDQUFDLGlGQUFnQjs7QUFFdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC90b2tlbml6ZS9sYWJlbC1zdGFydC1pbWFnZS5qcz80NDgxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgbGFiZWxFbmQgPSByZXF1aXJlKCcuL2xhYmVsLWVuZC5qcycpXG5cbnZhciBsYWJlbFN0YXJ0SW1hZ2UgPSB7XG4gIG5hbWU6ICdsYWJlbFN0YXJ0SW1hZ2UnLFxuICB0b2tlbml6ZTogdG9rZW5pemVMYWJlbFN0YXJ0SW1hZ2UsXG4gIHJlc29sdmVBbGw6IGxhYmVsRW5kLnJlc29sdmVBbGxcbn1cblxuZnVuY3Rpb24gdG9rZW5pemVMYWJlbFN0YXJ0SW1hZ2UoZWZmZWN0cywgb2ssIG5vaykge1xuICB2YXIgc2VsZiA9IHRoaXNcbiAgcmV0dXJuIHN0YXJ0XG5cbiAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgIGVmZmVjdHMuZW50ZXIoJ2xhYmVsSW1hZ2UnKVxuICAgIGVmZmVjdHMuZW50ZXIoJ2xhYmVsSW1hZ2VNYXJrZXInKVxuICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgIGVmZmVjdHMuZXhpdCgnbGFiZWxJbWFnZU1hcmtlcicpXG4gICAgcmV0dXJuIG9wZW5cbiAgfVxuXG4gIGZ1bmN0aW9uIG9wZW4oY29kZSkge1xuICAgIGlmIChjb2RlID09PSA5MSkge1xuICAgICAgZWZmZWN0cy5lbnRlcignbGFiZWxNYXJrZXInKVxuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICBlZmZlY3RzLmV4aXQoJ2xhYmVsTWFya2VyJylcbiAgICAgIGVmZmVjdHMuZXhpdCgnbGFiZWxJbWFnZScpXG4gICAgICByZXR1cm4gYWZ0ZXJcbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cblxuICBmdW5jdGlvbiBhZnRlcihjb2RlKSB7XG4gICAgLyogYzggaWdub3JlIG5leHQgKi9cbiAgICByZXR1cm4gY29kZSA9PT0gOTQgJiZcbiAgICAgIC8qIGM4IGlnbm9yZSBuZXh0ICovXG4gICAgICAnX2hpZGRlbkZvb3Rub3RlU3VwcG9ydCcgaW4gc2VsZi5wYXJzZXIuY29uc3RydWN0c1xuICAgICAgPyAvKiBjOCBpZ25vcmUgbmV4dCAqL1xuICAgICAgICBub2soY29kZSlcbiAgICAgIDogb2soY29kZSlcbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IGxhYmVsU3RhcnRJbWFnZVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/label-start-image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/label-start-link.js":
/*!******************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/label-start-link.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar labelEnd = __webpack_require__(/*! ./label-end.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/label-end.js\")\n\nvar labelStartLink = {\n  name: 'labelStartLink',\n  tokenize: tokenizeLabelStartLink,\n  resolveAll: labelEnd.resolveAll\n}\n\nfunction tokenizeLabelStartLink(effects, ok, nok) {\n  var self = this\n  return start\n\n  function start(code) {\n    effects.enter('labelLink')\n    effects.enter('labelMarker')\n    effects.consume(code)\n    effects.exit('labelMarker')\n    effects.exit('labelLink')\n    return after\n  }\n\n  function after(code) {\n    /* c8 ignore next */\n    return code === 94 &&\n      /* c8 ignore next */\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? /* c8 ignore next */\n        nok(code)\n      : ok(code)\n  }\n}\n\nmodule.exports = labelStartLink\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdG9rZW5pemUvbGFiZWwtc3RhcnQtbGluay5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixlQUFlLG1CQUFPLENBQUMsaUZBQWdCOztBQUV2QztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC90b2tlbml6ZS9sYWJlbC1zdGFydC1saW5rLmpzPzZiZTAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBsYWJlbEVuZCA9IHJlcXVpcmUoJy4vbGFiZWwtZW5kLmpzJylcblxudmFyIGxhYmVsU3RhcnRMaW5rID0ge1xuICBuYW1lOiAnbGFiZWxTdGFydExpbmsnLFxuICB0b2tlbml6ZTogdG9rZW5pemVMYWJlbFN0YXJ0TGluayxcbiAgcmVzb2x2ZUFsbDogbGFiZWxFbmQucmVzb2x2ZUFsbFxufVxuXG5mdW5jdGlvbiB0b2tlbml6ZUxhYmVsU3RhcnRMaW5rKGVmZmVjdHMsIG9rLCBub2spIHtcbiAgdmFyIHNlbGYgPSB0aGlzXG4gIHJldHVybiBzdGFydFxuXG4gIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICBlZmZlY3RzLmVudGVyKCdsYWJlbExpbmsnKVxuICAgIGVmZmVjdHMuZW50ZXIoJ2xhYmVsTWFya2VyJylcbiAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICBlZmZlY3RzLmV4aXQoJ2xhYmVsTWFya2VyJylcbiAgICBlZmZlY3RzLmV4aXQoJ2xhYmVsTGluaycpXG4gICAgcmV0dXJuIGFmdGVyXG4gIH1cblxuICBmdW5jdGlvbiBhZnRlcihjb2RlKSB7XG4gICAgLyogYzggaWdub3JlIG5leHQgKi9cbiAgICByZXR1cm4gY29kZSA9PT0gOTQgJiZcbiAgICAgIC8qIGM4IGlnbm9yZSBuZXh0ICovXG4gICAgICAnX2hpZGRlbkZvb3Rub3RlU3VwcG9ydCcgaW4gc2VsZi5wYXJzZXIuY29uc3RydWN0c1xuICAgICAgPyAvKiBjOCBpZ25vcmUgbmV4dCAqL1xuICAgICAgICBub2soY29kZSlcbiAgICAgIDogb2soY29kZSlcbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IGxhYmVsU3RhcnRMaW5rXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/label-start-link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/line-ending.js":
/*!*************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/line-ending.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nvar lineEnding = {\n  name: 'lineEnding',\n  tokenize: tokenizeLineEnding\n}\n\nfunction tokenizeLineEnding(effects, ok) {\n  return start\n\n  function start(code) {\n    effects.enter('lineEnding')\n    effects.consume(code)\n    effects.exit('lineEnding')\n    return factorySpace(effects, ok, 'linePrefix')\n  }\n}\n\nmodule.exports = lineEnding\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdG9rZW5pemUvbGluZS1lbmRpbmcuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosbUJBQW1CLG1CQUFPLENBQUMseUZBQW9COztBQUUvQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC90b2tlbml6ZS9saW5lLWVuZGluZy5qcz80OTlkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgZmFjdG9yeVNwYWNlID0gcmVxdWlyZSgnLi9mYWN0b3J5LXNwYWNlLmpzJylcblxudmFyIGxpbmVFbmRpbmcgPSB7XG4gIG5hbWU6ICdsaW5lRW5kaW5nJyxcbiAgdG9rZW5pemU6IHRva2VuaXplTGluZUVuZGluZ1xufVxuXG5mdW5jdGlvbiB0b2tlbml6ZUxpbmVFbmRpbmcoZWZmZWN0cywgb2spIHtcbiAgcmV0dXJuIHN0YXJ0XG5cbiAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgIGVmZmVjdHMuZW50ZXIoJ2xpbmVFbmRpbmcnKVxuICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgIGVmZmVjdHMuZXhpdCgnbGluZUVuZGluZycpXG4gICAgcmV0dXJuIGZhY3RvcnlTcGFjZShlZmZlY3RzLCBvaywgJ2xpbmVQcmVmaXgnKVxuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gbGluZUVuZGluZ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/line-ending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/list.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/list.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar asciiDigit = __webpack_require__(/*! ../character/ascii-digit.js */ \"(ssr)/./node_modules/micromark/dist/character/ascii-digit.js\")\nvar markdownSpace = __webpack_require__(/*! ../character/markdown-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-space.js\")\nvar prefixSize = __webpack_require__(/*! ../util/prefix-size.js */ \"(ssr)/./node_modules/micromark/dist/util/prefix-size.js\")\nvar sizeChunks = __webpack_require__(/*! ../util/size-chunks.js */ \"(ssr)/./node_modules/micromark/dist/util/size-chunks.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\nvar partialBlankLine = __webpack_require__(/*! ./partial-blank-line.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/partial-blank-line.js\")\nvar thematicBreak = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/thematic-break.js\")\n\nvar list = {\n  name: 'list',\n  tokenize: tokenizeListStart,\n  continuation: {\n    tokenize: tokenizeListContinuation\n  },\n  exit: tokenizeListEnd\n}\nvar listItemPrefixWhitespaceConstruct = {\n  tokenize: tokenizeListItemPrefixWhitespace,\n  partial: true\n}\nvar indentConstruct = {\n  tokenize: tokenizeIndent,\n  partial: true\n}\n\nfunction tokenizeListStart(effects, ok, nok) {\n  var self = this\n  var initialSize = prefixSize(self.events, 'linePrefix')\n  var size = 0\n  return start\n\n  function start(code) {\n    var kind =\n      self.containerState.type ||\n      (code === 42 || code === 43 || code === 45\n        ? 'listUnordered'\n        : 'listOrdered')\n\n    if (\n      kind === 'listUnordered'\n        ? !self.containerState.marker || code === self.containerState.marker\n        : asciiDigit(code)\n    ) {\n      if (!self.containerState.type) {\n        self.containerState.type = kind\n        effects.enter(kind, {\n          _container: true\n        })\n      }\n\n      if (kind === 'listUnordered') {\n        effects.enter('listItemPrefix')\n        return code === 42 || code === 45\n          ? effects.check(thematicBreak, nok, atMarker)(code)\n          : atMarker(code)\n      }\n\n      if (!self.interrupt || code === 49) {\n        effects.enter('listItemPrefix')\n        effects.enter('listItemValue')\n        return inside(code)\n      }\n    }\n\n    return nok(code)\n  }\n\n  function inside(code) {\n    if (asciiDigit(code) && ++size < 10) {\n      effects.consume(code)\n      return inside\n    }\n\n    if (\n      (!self.interrupt || size < 2) &&\n      (self.containerState.marker\n        ? code === self.containerState.marker\n        : code === 41 || code === 46)\n    ) {\n      effects.exit('listItemValue')\n      return atMarker(code)\n    }\n\n    return nok(code)\n  }\n\n  function atMarker(code) {\n    effects.enter('listItemMarker')\n    effects.consume(code)\n    effects.exit('listItemMarker')\n    self.containerState.marker = self.containerState.marker || code\n    return effects.check(\n      partialBlankLine, // Can’t be empty when interrupting.\n      self.interrupt ? nok : onBlank,\n      effects.attempt(\n        listItemPrefixWhitespaceConstruct,\n        endOfPrefix,\n        otherPrefix\n      )\n    )\n  }\n\n  function onBlank(code) {\n    self.containerState.initialBlankLine = true\n    initialSize++\n    return endOfPrefix(code)\n  }\n\n  function otherPrefix(code) {\n    if (markdownSpace(code)) {\n      effects.enter('listItemPrefixWhitespace')\n      effects.consume(code)\n      effects.exit('listItemPrefixWhitespace')\n      return endOfPrefix\n    }\n\n    return nok(code)\n  }\n\n  function endOfPrefix(code) {\n    self.containerState.size =\n      initialSize + sizeChunks(self.sliceStream(effects.exit('listItemPrefix')))\n    return ok(code)\n  }\n}\n\nfunction tokenizeListContinuation(effects, ok, nok) {\n  var self = this\n  self.containerState._closeFlow = undefined\n  return effects.check(partialBlankLine, onBlank, notBlank)\n\n  function onBlank(code) {\n    self.containerState.furtherBlankLines =\n      self.containerState.furtherBlankLines ||\n      self.containerState.initialBlankLine // We have a blank line.\n    // Still, try to consume at most the items size.\n\n    return factorySpace(\n      effects,\n      ok,\n      'listItemIndent',\n      self.containerState.size + 1\n    )(code)\n  }\n\n  function notBlank(code) {\n    if (self.containerState.furtherBlankLines || !markdownSpace(code)) {\n      self.containerState.furtherBlankLines = self.containerState.initialBlankLine = undefined\n      return notInCurrentItem(code)\n    }\n\n    self.containerState.furtherBlankLines = self.containerState.initialBlankLine = undefined\n    return effects.attempt(indentConstruct, ok, notInCurrentItem)(code)\n  }\n\n  function notInCurrentItem(code) {\n    // While we do continue, we signal that the flow should be closed.\n    self.containerState._closeFlow = true // As we’re closing flow, we’re no longer interrupting.\n\n    self.interrupt = undefined\n    return factorySpace(\n      effects,\n      effects.attempt(list, ok, nok),\n      'linePrefix',\n      self.parser.constructs.disable.null.indexOf('codeIndented') > -1\n        ? undefined\n        : 4\n    )(code)\n  }\n}\n\nfunction tokenizeIndent(effects, ok, nok) {\n  var self = this\n  return factorySpace(\n    effects,\n    afterPrefix,\n    'listItemIndent',\n    self.containerState.size + 1\n  )\n\n  function afterPrefix(code) {\n    return prefixSize(self.events, 'listItemIndent') ===\n      self.containerState.size\n      ? ok(code)\n      : nok(code)\n  }\n}\n\nfunction tokenizeListEnd(effects) {\n  effects.exit(this.containerState.type)\n}\n\nfunction tokenizeListItemPrefixWhitespace(effects, ok, nok) {\n  var self = this\n  return factorySpace(\n    effects,\n    afterPrefix,\n    'listItemPrefixWhitespace',\n    self.parser.constructs.disable.null.indexOf('codeIndented') > -1\n      ? undefined\n      : 4 + 1\n  )\n\n  function afterPrefix(code) {\n    return markdownSpace(code) ||\n      !prefixSize(self.events, 'listItemPrefixWhitespace')\n      ? nok(code)\n      : ok(code)\n  }\n}\n\nmodule.exports = list\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/partial-blank-line.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/partial-blank-line.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nvar partialBlankLine = {\n  tokenize: tokenizePartialBlankLine,\n  partial: true\n}\n\nfunction tokenizePartialBlankLine(effects, ok, nok) {\n  return factorySpace(effects, afterWhitespace, 'linePrefix')\n\n  function afterWhitespace(code) {\n    return code === null || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n\nmodule.exports = partialBlankLine\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdG9rZW5pemUvcGFydGlhbC1ibGFuay1saW5lLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLHlCQUF5QixtQkFBTyxDQUFDLG1IQUFzQztBQUN2RSxtQkFBbUIsbUJBQU8sQ0FBQyx5RkFBb0I7O0FBRS9DO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L3Rva2VuaXplL3BhcnRpYWwtYmxhbmstbGluZS5qcz8zZWZkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgbWFya2Rvd25MaW5lRW5kaW5nID0gcmVxdWlyZSgnLi4vY2hhcmFjdGVyL21hcmtkb3duLWxpbmUtZW5kaW5nLmpzJylcbnZhciBmYWN0b3J5U3BhY2UgPSByZXF1aXJlKCcuL2ZhY3Rvcnktc3BhY2UuanMnKVxuXG52YXIgcGFydGlhbEJsYW5rTGluZSA9IHtcbiAgdG9rZW5pemU6IHRva2VuaXplUGFydGlhbEJsYW5rTGluZSxcbiAgcGFydGlhbDogdHJ1ZVxufVxuXG5mdW5jdGlvbiB0b2tlbml6ZVBhcnRpYWxCbGFua0xpbmUoZWZmZWN0cywgb2ssIG5vaykge1xuICByZXR1cm4gZmFjdG9yeVNwYWNlKGVmZmVjdHMsIGFmdGVyV2hpdGVzcGFjZSwgJ2xpbmVQcmVmaXgnKVxuXG4gIGZ1bmN0aW9uIGFmdGVyV2hpdGVzcGFjZShjb2RlKSB7XG4gICAgcmV0dXJuIGNvZGUgPT09IG51bGwgfHwgbWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpID8gb2soY29kZSkgOiBub2soY29kZSlcbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHBhcnRpYWxCbGFua0xpbmVcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/partial-blank-line.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/setext-underline.js":
/*!******************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/setext-underline.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar shallow = __webpack_require__(/*! ../util/shallow.js */ \"(ssr)/./node_modules/micromark/dist/util/shallow.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nvar setextUnderline = {\n  name: 'setextUnderline',\n  tokenize: tokenizeSetextUnderline,\n  resolveTo: resolveToSetextUnderline\n}\n\nfunction resolveToSetextUnderline(events, context) {\n  var index = events.length\n  var content\n  var text\n  var definition\n  var heading // Find the opening of the content.\n  // It’ll always exist: we don’t tokenize if it isn’t there.\n\n  while (index--) {\n    if (events[index][0] === 'enter') {\n      if (events[index][1].type === 'content') {\n        content = index\n        break\n      }\n\n      if (events[index][1].type === 'paragraph') {\n        text = index\n      }\n    } // Exit\n    else {\n      if (events[index][1].type === 'content') {\n        // Remove the content end (if needed we’ll add it later)\n        events.splice(index, 1)\n      }\n\n      if (!definition && events[index][1].type === 'definition') {\n        definition = index\n      }\n    }\n  }\n\n  heading = {\n    type: 'setextHeading',\n    start: shallow(events[text][1].start),\n    end: shallow(events[events.length - 1][1].end)\n  } // Change the paragraph to setext heading text.\n\n  events[text][1].type = 'setextHeadingText' // If we have definitions in the content, we’ll keep on having content,\n  // but we need move it.\n\n  if (definition) {\n    events.splice(text, 0, ['enter', heading, context])\n    events.splice(definition + 1, 0, ['exit', events[content][1], context])\n    events[content][1].end = shallow(events[definition][1].end)\n  } else {\n    events[content][1] = heading\n  } // Add the heading exit at the end.\n\n  events.push(['exit', heading, context])\n  return events\n}\n\nfunction tokenizeSetextUnderline(effects, ok, nok) {\n  var self = this\n  var index = self.events.length\n  var marker\n  var paragraph // Find an opening.\n\n  while (index--) {\n    // Skip enter/exit of line ending, line prefix, and content.\n    // We can now either have a definition or a paragraph.\n    if (\n      self.events[index][1].type !== 'lineEnding' &&\n      self.events[index][1].type !== 'linePrefix' &&\n      self.events[index][1].type !== 'content'\n    ) {\n      paragraph = self.events[index][1].type === 'paragraph'\n      break\n    }\n  }\n\n  return start\n\n  function start(code) {\n    if (!self.lazy && (self.interrupt || paragraph)) {\n      effects.enter('setextHeadingLine')\n      effects.enter('setextHeadingLineSequence')\n      marker = code\n      return closingSequence(code)\n    }\n\n    return nok(code)\n  }\n\n  function closingSequence(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return closingSequence\n    }\n\n    effects.exit('setextHeadingLineSequence')\n    return factorySpace(effects, closingSequenceEnd, 'lineSuffix')(code)\n  }\n\n  function closingSequenceEnd(code) {\n    if (code === null || markdownLineEnding(code)) {\n      effects.exit('setextHeadingLine')\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\nmodule.exports = setextUnderline\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdG9rZW5pemUvc2V0ZXh0LXVuZGVybGluZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWix5QkFBeUIsbUJBQU8sQ0FBQyxtSEFBc0M7QUFDdkUsY0FBYyxtQkFBTyxDQUFDLCtFQUFvQjtBQUMxQyxtQkFBbUIsbUJBQU8sQ0FBQyx5RkFBb0I7O0FBRS9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJOztBQUVKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQSxJQUFJOztBQUVKO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC90b2tlbml6ZS9zZXRleHQtdW5kZXJsaW5lLmpzP2U5MzciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBtYXJrZG93bkxpbmVFbmRpbmcgPSByZXF1aXJlKCcuLi9jaGFyYWN0ZXIvbWFya2Rvd24tbGluZS1lbmRpbmcuanMnKVxudmFyIHNoYWxsb3cgPSByZXF1aXJlKCcuLi91dGlsL3NoYWxsb3cuanMnKVxudmFyIGZhY3RvcnlTcGFjZSA9IHJlcXVpcmUoJy4vZmFjdG9yeS1zcGFjZS5qcycpXG5cbnZhciBzZXRleHRVbmRlcmxpbmUgPSB7XG4gIG5hbWU6ICdzZXRleHRVbmRlcmxpbmUnLFxuICB0b2tlbml6ZTogdG9rZW5pemVTZXRleHRVbmRlcmxpbmUsXG4gIHJlc29sdmVUbzogcmVzb2x2ZVRvU2V0ZXh0VW5kZXJsaW5lXG59XG5cbmZ1bmN0aW9uIHJlc29sdmVUb1NldGV4dFVuZGVybGluZShldmVudHMsIGNvbnRleHQpIHtcbiAgdmFyIGluZGV4ID0gZXZlbnRzLmxlbmd0aFxuICB2YXIgY29udGVudFxuICB2YXIgdGV4dFxuICB2YXIgZGVmaW5pdGlvblxuICB2YXIgaGVhZGluZyAvLyBGaW5kIHRoZSBvcGVuaW5nIG9mIHRoZSBjb250ZW50LlxuICAvLyBJdOKAmWxsIGFsd2F5cyBleGlzdDogd2UgZG9u4oCZdCB0b2tlbml6ZSBpZiBpdCBpc27igJl0IHRoZXJlLlxuXG4gIHdoaWxlIChpbmRleC0tKSB7XG4gICAgaWYgKGV2ZW50c1tpbmRleF1bMF0gPT09ICdlbnRlcicpIHtcbiAgICAgIGlmIChldmVudHNbaW5kZXhdWzFdLnR5cGUgPT09ICdjb250ZW50Jykge1xuICAgICAgICBjb250ZW50ID0gaW5kZXhcbiAgICAgICAgYnJlYWtcbiAgICAgIH1cblxuICAgICAgaWYgKGV2ZW50c1tpbmRleF1bMV0udHlwZSA9PT0gJ3BhcmFncmFwaCcpIHtcbiAgICAgICAgdGV4dCA9IGluZGV4XG4gICAgICB9XG4gICAgfSAvLyBFeGl0XG4gICAgZWxzZSB7XG4gICAgICBpZiAoZXZlbnRzW2luZGV4XVsxXS50eXBlID09PSAnY29udGVudCcpIHtcbiAgICAgICAgLy8gUmVtb3ZlIHRoZSBjb250ZW50IGVuZCAoaWYgbmVlZGVkIHdl4oCZbGwgYWRkIGl0IGxhdGVyKVxuICAgICAgICBldmVudHMuc3BsaWNlKGluZGV4LCAxKVxuICAgICAgfVxuXG4gICAgICBpZiAoIWRlZmluaXRpb24gJiYgZXZlbnRzW2luZGV4XVsxXS50eXBlID09PSAnZGVmaW5pdGlvbicpIHtcbiAgICAgICAgZGVmaW5pdGlvbiA9IGluZGV4XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgaGVhZGluZyA9IHtcbiAgICB0eXBlOiAnc2V0ZXh0SGVhZGluZycsXG4gICAgc3RhcnQ6IHNoYWxsb3coZXZlbnRzW3RleHRdWzFdLnN0YXJ0KSxcbiAgICBlbmQ6IHNoYWxsb3coZXZlbnRzW2V2ZW50cy5sZW5ndGggLSAxXVsxXS5lbmQpXG4gIH0gLy8gQ2hhbmdlIHRoZSBwYXJhZ3JhcGggdG8gc2V0ZXh0IGhlYWRpbmcgdGV4dC5cblxuICBldmVudHNbdGV4dF1bMV0udHlwZSA9ICdzZXRleHRIZWFkaW5nVGV4dCcgLy8gSWYgd2UgaGF2ZSBkZWZpbml0aW9ucyBpbiB0aGUgY29udGVudCwgd2XigJlsbCBrZWVwIG9uIGhhdmluZyBjb250ZW50LFxuICAvLyBidXQgd2UgbmVlZCBtb3ZlIGl0LlxuXG4gIGlmIChkZWZpbml0aW9uKSB7XG4gICAgZXZlbnRzLnNwbGljZSh0ZXh0LCAwLCBbJ2VudGVyJywgaGVhZGluZywgY29udGV4dF0pXG4gICAgZXZlbnRzLnNwbGljZShkZWZpbml0aW9uICsgMSwgMCwgWydleGl0JywgZXZlbnRzW2NvbnRlbnRdWzFdLCBjb250ZXh0XSlcbiAgICBldmVudHNbY29udGVudF1bMV0uZW5kID0gc2hhbGxvdyhldmVudHNbZGVmaW5pdGlvbl1bMV0uZW5kKVxuICB9IGVsc2Uge1xuICAgIGV2ZW50c1tjb250ZW50XVsxXSA9IGhlYWRpbmdcbiAgfSAvLyBBZGQgdGhlIGhlYWRpbmcgZXhpdCBhdCB0aGUgZW5kLlxuXG4gIGV2ZW50cy5wdXNoKFsnZXhpdCcsIGhlYWRpbmcsIGNvbnRleHRdKVxuICByZXR1cm4gZXZlbnRzXG59XG5cbmZ1bmN0aW9uIHRva2VuaXplU2V0ZXh0VW5kZXJsaW5lKGVmZmVjdHMsIG9rLCBub2spIHtcbiAgdmFyIHNlbGYgPSB0aGlzXG4gIHZhciBpbmRleCA9IHNlbGYuZXZlbnRzLmxlbmd0aFxuICB2YXIgbWFya2VyXG4gIHZhciBwYXJhZ3JhcGggLy8gRmluZCBhbiBvcGVuaW5nLlxuXG4gIHdoaWxlIChpbmRleC0tKSB7XG4gICAgLy8gU2tpcCBlbnRlci9leGl0IG9mIGxpbmUgZW5kaW5nLCBsaW5lIHByZWZpeCwgYW5kIGNvbnRlbnQuXG4gICAgLy8gV2UgY2FuIG5vdyBlaXRoZXIgaGF2ZSBhIGRlZmluaXRpb24gb3IgYSBwYXJhZ3JhcGguXG4gICAgaWYgKFxuICAgICAgc2VsZi5ldmVudHNbaW5kZXhdWzFdLnR5cGUgIT09ICdsaW5lRW5kaW5nJyAmJlxuICAgICAgc2VsZi5ldmVudHNbaW5kZXhdWzFdLnR5cGUgIT09ICdsaW5lUHJlZml4JyAmJlxuICAgICAgc2VsZi5ldmVudHNbaW5kZXhdWzFdLnR5cGUgIT09ICdjb250ZW50J1xuICAgICkge1xuICAgICAgcGFyYWdyYXBoID0gc2VsZi5ldmVudHNbaW5kZXhdWzFdLnR5cGUgPT09ICdwYXJhZ3JhcGgnXG4gICAgICBicmVha1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBzdGFydFxuXG4gIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICBpZiAoIXNlbGYubGF6eSAmJiAoc2VsZi5pbnRlcnJ1cHQgfHwgcGFyYWdyYXBoKSkge1xuICAgICAgZWZmZWN0cy5lbnRlcignc2V0ZXh0SGVhZGluZ0xpbmUnKVxuICAgICAgZWZmZWN0cy5lbnRlcignc2V0ZXh0SGVhZGluZ0xpbmVTZXF1ZW5jZScpXG4gICAgICBtYXJrZXIgPSBjb2RlXG4gICAgICByZXR1cm4gY2xvc2luZ1NlcXVlbmNlKGNvZGUpXG4gICAgfVxuXG4gICAgcmV0dXJuIG5vayhjb2RlKVxuICB9XG5cbiAgZnVuY3Rpb24gY2xvc2luZ1NlcXVlbmNlKGNvZGUpIHtcbiAgICBpZiAoY29kZSA9PT0gbWFya2VyKSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBjbG9zaW5nU2VxdWVuY2VcbiAgICB9XG5cbiAgICBlZmZlY3RzLmV4aXQoJ3NldGV4dEhlYWRpbmdMaW5lU2VxdWVuY2UnKVxuICAgIHJldHVybiBmYWN0b3J5U3BhY2UoZWZmZWN0cywgY2xvc2luZ1NlcXVlbmNlRW5kLCAnbGluZVN1ZmZpeCcpKGNvZGUpXG4gIH1cblxuICBmdW5jdGlvbiBjbG9zaW5nU2VxdWVuY2VFbmQoY29kZSkge1xuICAgIGlmIChjb2RlID09PSBudWxsIHx8IG1hcmtkb3duTGluZUVuZGluZyhjb2RlKSkge1xuICAgICAgZWZmZWN0cy5leGl0KCdzZXRleHRIZWFkaW5nTGluZScpXG4gICAgICByZXR1cm4gb2soY29kZSlcbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBzZXRleHRVbmRlcmxpbmVcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/setext-underline.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/tokenize/thematic-break.js":
/*!****************************************************************!*\
  !*** ./node_modules/micromark/dist/tokenize/thematic-break.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar markdownSpace = __webpack_require__(/*! ../character/markdown-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-space.js\")\nvar factorySpace = __webpack_require__(/*! ./factory-space.js */ \"(ssr)/./node_modules/micromark/dist/tokenize/factory-space.js\")\n\nvar thematicBreak = {\n  name: 'thematicBreak',\n  tokenize: tokenizeThematicBreak\n}\n\nfunction tokenizeThematicBreak(effects, ok, nok) {\n  var size = 0\n  var marker\n  return start\n\n  function start(code) {\n    effects.enter('thematicBreak')\n    marker = code\n    return atBreak(code)\n  }\n\n  function atBreak(code) {\n    if (code === marker) {\n      effects.enter('thematicBreakSequence')\n      return sequence(code)\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(effects, atBreak, 'whitespace')(code)\n    }\n\n    if (size < 3 || (code !== null && !markdownLineEnding(code))) {\n      return nok(code)\n    }\n\n    effects.exit('thematicBreak')\n    return ok(code)\n  }\n\n  function sequence(code) {\n    if (code === marker) {\n      effects.consume(code)\n      size++\n      return sequence\n    }\n\n    effects.exit('thematicBreakSequence')\n    return atBreak(code)\n  }\n}\n\nmodule.exports = thematicBreak\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/tokenize/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/chunked-push.js":
/*!**********************************************************!*\
  !*** ./node_modules/micromark/dist/util/chunked-push.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar chunkedSplice = __webpack_require__(/*! ./chunked-splice.js */ \"(ssr)/./node_modules/micromark/dist/util/chunked-splice.js\")\n\nfunction chunkedPush(list, items) {\n  if (list.length) {\n    chunkedSplice(list, list.length, 0, items)\n    return list\n  }\n\n  return items\n}\n\nmodule.exports = chunkedPush\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9jaHVua2VkLXB1c2guanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosb0JBQW9CLG1CQUFPLENBQUMsdUZBQXFCOztBQUVqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L3V0aWwvY2h1bmtlZC1wdXNoLmpzPzY4YTkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBjaHVua2VkU3BsaWNlID0gcmVxdWlyZSgnLi9jaHVua2VkLXNwbGljZS5qcycpXG5cbmZ1bmN0aW9uIGNodW5rZWRQdXNoKGxpc3QsIGl0ZW1zKSB7XG4gIGlmIChsaXN0Lmxlbmd0aCkge1xuICAgIGNodW5rZWRTcGxpY2UobGlzdCwgbGlzdC5sZW5ndGgsIDAsIGl0ZW1zKVxuICAgIHJldHVybiBsaXN0XG4gIH1cblxuICByZXR1cm4gaXRlbXNcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBjaHVua2VkUHVzaFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/chunked-push.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/chunked-splice.js":
/*!************************************************************!*\
  !*** ./node_modules/micromark/dist/util/chunked-splice.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar splice = __webpack_require__(/*! ../constant/splice.js */ \"(ssr)/./node_modules/micromark/dist/constant/splice.js\")\n\n// causes a stack overflow in V8 when trying to insert 100k items for instance.\n\nfunction chunkedSplice(list, start, remove, items) {\n  var end = list.length\n  var chunkStart = 0\n  var parameters // Make start between zero and `end` (included).\n\n  if (start < 0) {\n    start = -start > end ? 0 : end + start\n  } else {\n    start = start > end ? end : start\n  }\n\n  remove = remove > 0 ? remove : 0 // No need to chunk the items if there’s only a couple (10k) items.\n\n  if (items.length < 10000) {\n    parameters = Array.from(items)\n    parameters.unshift(start, remove)\n    splice.apply(list, parameters)\n  } else {\n    // Delete `remove` items starting from `start`\n    if (remove) splice.apply(list, [start, remove]) // Insert the items in chunks to not cause stack overflows.\n\n    while (chunkStart < items.length) {\n      parameters = items.slice(chunkStart, chunkStart + 10000)\n      parameters.unshift(start, 0)\n      splice.apply(list, parameters)\n      chunkStart += 10000\n      start += 10000\n    }\n  }\n}\n\nmodule.exports = chunkedSplice\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/chunked-splice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/classify-character.js":
/*!****************************************************************!*\
  !*** ./node_modules/micromark/dist/util/classify-character.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar markdownLineEndingOrSpace = __webpack_require__(/*! ../character/markdown-line-ending-or-space.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending-or-space.js\")\nvar unicodePunctuation = __webpack_require__(/*! ../character/unicode-punctuation.js */ \"(ssr)/./node_modules/micromark/dist/character/unicode-punctuation.js\")\nvar unicodeWhitespace = __webpack_require__(/*! ../character/unicode-whitespace.js */ \"(ssr)/./node_modules/micromark/dist/character/unicode-whitespace.js\")\n\n// Classify whether a character is unicode whitespace, unicode punctuation, or\n// anything else.\n// Used for attention (emphasis, strong), whose sequences can open or close\n// based on the class of surrounding characters.\nfunction classifyCharacter(code) {\n  if (\n    code === null ||\n    markdownLineEndingOrSpace(code) ||\n    unicodeWhitespace(code)\n  ) {\n    return 1\n  }\n\n  if (unicodePunctuation(code)) {\n    return 2\n  }\n}\n\nmodule.exports = classifyCharacter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9jbGFzc2lmeS1jaGFyYWN0ZXIuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZ0NBQWdDLG1CQUFPLENBQUMscUlBQStDO0FBQ3ZGLHlCQUF5QixtQkFBTyxDQUFDLGlIQUFxQztBQUN0RSx3QkFBd0IsbUJBQU8sQ0FBQywrR0FBb0M7O0FBRXBFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9jbGFzc2lmeS1jaGFyYWN0ZXIuanM/OTJkOCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIG1hcmtkb3duTGluZUVuZGluZ09yU3BhY2UgPSByZXF1aXJlKCcuLi9jaGFyYWN0ZXIvbWFya2Rvd24tbGluZS1lbmRpbmctb3Itc3BhY2UuanMnKVxudmFyIHVuaWNvZGVQdW5jdHVhdGlvbiA9IHJlcXVpcmUoJy4uL2NoYXJhY3Rlci91bmljb2RlLXB1bmN0dWF0aW9uLmpzJylcbnZhciB1bmljb2RlV2hpdGVzcGFjZSA9IHJlcXVpcmUoJy4uL2NoYXJhY3Rlci91bmljb2RlLXdoaXRlc3BhY2UuanMnKVxuXG4vLyBDbGFzc2lmeSB3aGV0aGVyIGEgY2hhcmFjdGVyIGlzIHVuaWNvZGUgd2hpdGVzcGFjZSwgdW5pY29kZSBwdW5jdHVhdGlvbiwgb3Jcbi8vIGFueXRoaW5nIGVsc2UuXG4vLyBVc2VkIGZvciBhdHRlbnRpb24gKGVtcGhhc2lzLCBzdHJvbmcpLCB3aG9zZSBzZXF1ZW5jZXMgY2FuIG9wZW4gb3IgY2xvc2Vcbi8vIGJhc2VkIG9uIHRoZSBjbGFzcyBvZiBzdXJyb3VuZGluZyBjaGFyYWN0ZXJzLlxuZnVuY3Rpb24gY2xhc3NpZnlDaGFyYWN0ZXIoY29kZSkge1xuICBpZiAoXG4gICAgY29kZSA9PT0gbnVsbCB8fFxuICAgIG1hcmtkb3duTGluZUVuZGluZ09yU3BhY2UoY29kZSkgfHxcbiAgICB1bmljb2RlV2hpdGVzcGFjZShjb2RlKVxuICApIHtcbiAgICByZXR1cm4gMVxuICB9XG5cbiAgaWYgKHVuaWNvZGVQdW5jdHVhdGlvbihjb2RlKSkge1xuICAgIHJldHVybiAyXG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBjbGFzc2lmeUNoYXJhY3RlclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/classify-character.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/combine-extensions.js":
/*!****************************************************************!*\
  !*** ./node_modules/micromark/dist/util/combine-extensions.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar hasOwnProperty = __webpack_require__(/*! ../constant/has-own-property.js */ \"(ssr)/./node_modules/micromark/dist/constant/has-own-property.js\")\nvar chunkedSplice = __webpack_require__(/*! ./chunked-splice.js */ \"(ssr)/./node_modules/micromark/dist/util/chunked-splice.js\")\nvar miniflat = __webpack_require__(/*! ./miniflat.js */ \"(ssr)/./node_modules/micromark/dist/util/miniflat.js\")\n\nfunction combineExtensions(extensions) {\n  var all = {}\n  var index = -1\n\n  while (++index < extensions.length) {\n    extension(all, extensions[index])\n  }\n\n  return all\n}\n\nfunction extension(all, extension) {\n  var hook\n  var left\n  var right\n  var code\n\n  for (hook in extension) {\n    left = hasOwnProperty.call(all, hook) ? all[hook] : (all[hook] = {})\n    right = extension[hook]\n\n    for (code in right) {\n      left[code] = constructs(\n        miniflat(right[code]),\n        hasOwnProperty.call(left, code) ? left[code] : []\n      )\n    }\n  }\n}\n\nfunction constructs(list, existing) {\n  var index = -1\n  var before = []\n\n  while (++index < list.length) {\n    ;(list[index].add === 'after' ? existing : before).push(list[index])\n  }\n\n  chunkedSplice(existing, 0, 0, before)\n  return existing\n}\n\nmodule.exports = combineExtensions\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/combine-extensions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/create-tokenizer.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark/dist/util/create-tokenizer.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar assign = __webpack_require__(/*! ../constant/assign.js */ \"(ssr)/./node_modules/micromark/dist/constant/assign.js\")\nvar markdownLineEnding = __webpack_require__(/*! ../character/markdown-line-ending.js */ \"(ssr)/./node_modules/micromark/dist/character/markdown-line-ending.js\")\nvar chunkedPush = __webpack_require__(/*! ./chunked-push.js */ \"(ssr)/./node_modules/micromark/dist/util/chunked-push.js\")\nvar chunkedSplice = __webpack_require__(/*! ./chunked-splice.js */ \"(ssr)/./node_modules/micromark/dist/util/chunked-splice.js\")\nvar miniflat = __webpack_require__(/*! ./miniflat.js */ \"(ssr)/./node_modules/micromark/dist/util/miniflat.js\")\nvar resolveAll = __webpack_require__(/*! ./resolve-all.js */ \"(ssr)/./node_modules/micromark/dist/util/resolve-all.js\")\nvar serializeChunks = __webpack_require__(/*! ./serialize-chunks.js */ \"(ssr)/./node_modules/micromark/dist/util/serialize-chunks.js\")\nvar shallow = __webpack_require__(/*! ./shallow.js */ \"(ssr)/./node_modules/micromark/dist/util/shallow.js\")\nvar sliceChunks = __webpack_require__(/*! ./slice-chunks.js */ \"(ssr)/./node_modules/micromark/dist/util/slice-chunks.js\")\n\n// Create a tokenizer.\n// Tokenizers deal with one type of data (e.g., containers, flow, text).\n// The parser is the object dealing with it all.\n// `initialize` works like other constructs, except that only its `tokenize`\n// function is used, in which case it doesn’t receive an `ok` or `nok`.\n// `from` can be given to set the point before the first character, although\n// when further lines are indented, they must be set with `defineSkip`.\nfunction createTokenizer(parser, initialize, from) {\n  var point = from\n    ? shallow(from)\n    : {\n        line: 1,\n        column: 1,\n        offset: 0\n      }\n  var columnStart = {}\n  var resolveAllConstructs = []\n  var chunks = []\n  var stack = []\n\n  var effects = {\n    consume: consume,\n    enter: enter,\n    exit: exit,\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    interrupt: constructFactory(onsuccessfulcheck, {\n      interrupt: true\n    }),\n    lazy: constructFactory(onsuccessfulcheck, {\n      lazy: true\n    })\n  } // State and tools for resolving and serializing.\n\n  var context = {\n    previous: null,\n    events: [],\n    parser: parser,\n    sliceStream: sliceStream,\n    sliceSerialize: sliceSerialize,\n    now: now,\n    defineSkip: skip,\n    write: write\n  } // The state function.\n\n  var state = initialize.tokenize.call(context, effects) // Track which character we expect to be consumed, to catch bugs.\n\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize)\n  } // Store where we are in the input stream.\n\n  point._index = 0\n  point._bufferIndex = -1\n  return context\n\n  function write(slice) {\n    chunks = chunkedPush(chunks, slice)\n    main() // Exit if we’re not done, resolve might change stuff.\n\n    if (chunks[chunks.length - 1] !== null) {\n      return []\n    }\n\n    addResult(initialize, 0) // Otherwise, resolve, and exit.\n\n    context.events = resolveAll(resolveAllConstructs, context.events, context)\n    return context.events\n  } //\n  // Tools.\n  //\n\n  function sliceSerialize(token) {\n    return serializeChunks(sliceStream(token))\n  }\n\n  function sliceStream(token) {\n    return sliceChunks(chunks, token)\n  }\n\n  function now() {\n    return shallow(point)\n  }\n\n  function skip(value) {\n    columnStart[value.line] = value.column\n    accountForPotentialSkip()\n  } //\n  // State management.\n  //\n  // Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n  // `consume`).\n  // Here is where we walk through the chunks, which either include strings of\n  // several characters, or numerical character codes.\n  // The reason to do this in a loop instead of a call is so the stack can\n  // drain.\n\n  function main() {\n    var chunkIndex\n    var chunk\n\n    while (point._index < chunks.length) {\n      chunk = chunks[point._index] // If we’re in a buffer chunk, loop through it.\n\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index\n\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0\n        }\n\n        while (\n          point._index === chunkIndex &&\n          point._bufferIndex < chunk.length\n        ) {\n          go(chunk.charCodeAt(point._bufferIndex))\n        }\n      } else {\n        go(chunk)\n      }\n    }\n  } // Deal with one code.\n\n  function go(code) {\n    state = state(code)\n  } // Move a character forward.\n\n  function consume(code) {\n    if (markdownLineEnding(code)) {\n      point.line++\n      point.column = 1\n      point.offset += code === -3 ? 2 : 1\n      accountForPotentialSkip()\n    } else if (code !== -1) {\n      point.column++\n      point.offset++\n    } // Not in a string chunk.\n\n    if (point._bufferIndex < 0) {\n      point._index++\n    } else {\n      point._bufferIndex++ // At end of string chunk.\n\n      if (point._bufferIndex === chunks[point._index].length) {\n        point._bufferIndex = -1\n        point._index++\n      }\n    } // Expose the previous character.\n\n    context.previous = code // Mark as consumed.\n  } // Start a token.\n\n  function enter(type, fields) {\n    var token = fields || {}\n    token.type = type\n    token.start = now()\n    context.events.push(['enter', token, context])\n    stack.push(token)\n    return token\n  } // Stop a token.\n\n  function exit(type) {\n    var token = stack.pop()\n    token.end = now()\n    context.events.push(['exit', token, context])\n    return token\n  } // Use results.\n\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from)\n  } // Discard results.\n\n  function onsuccessfulcheck(construct, info) {\n    info.restore()\n  } // Factory to attempt/check/interrupt.\n\n  function constructFactory(onreturn, fields) {\n    return hook // Handle either an object mapping codes to constructs, a list of\n    // constructs, or a single construct.\n\n    function hook(constructs, returnState, bogusState) {\n      var listOfConstructs\n      var constructIndex\n      var currentConstruct\n      var info\n      return constructs.tokenize || 'length' in constructs\n        ? handleListOfConstructs(miniflat(constructs))\n        : handleMapOfConstructs\n\n      function handleMapOfConstructs(code) {\n        if (code in constructs || null in constructs) {\n          return handleListOfConstructs(\n            constructs.null\n              ? /* c8 ignore next */\n                miniflat(constructs[code]).concat(miniflat(constructs.null))\n              : constructs[code]\n          )(code)\n        }\n\n        return bogusState(code)\n      }\n\n      function handleListOfConstructs(list) {\n        listOfConstructs = list\n        constructIndex = 0\n        return handleConstruct(list[constructIndex])\n      }\n\n      function handleConstruct(construct) {\n        return start\n\n        function start(code) {\n          // To do: not nede to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store()\n          currentConstruct = construct\n\n          if (!construct.partial) {\n            context.currentConstruct = construct\n          }\n\n          if (\n            construct.name &&\n            context.parser.constructs.disable.null.indexOf(construct.name) > -1\n          ) {\n            return nok()\n          }\n\n          return construct.tokenize.call(\n            fields ? assign({}, context, fields) : context,\n            effects,\n            ok,\n            nok\n          )(code)\n        }\n      }\n\n      function ok(code) {\n        onreturn(currentConstruct, info)\n        return returnState\n      }\n\n      function nok(code) {\n        info.restore()\n\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex])\n        }\n\n        return bogusState\n      }\n    }\n  }\n\n  function addResult(construct, from) {\n    if (construct.resolveAll && resolveAllConstructs.indexOf(construct) < 0) {\n      resolveAllConstructs.push(construct)\n    }\n\n    if (construct.resolve) {\n      chunkedSplice(\n        context.events,\n        from,\n        context.events.length - from,\n        construct.resolve(context.events.slice(from), context)\n      )\n    }\n\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context)\n    }\n  }\n\n  function store() {\n    var startPoint = now()\n    var startPrevious = context.previous\n    var startCurrentConstruct = context.currentConstruct\n    var startEventsIndex = context.events.length\n    var startStack = Array.from(stack)\n    return {\n      restore: restore,\n      from: startEventsIndex\n    }\n\n    function restore() {\n      point = startPoint\n      context.previous = startPrevious\n      context.currentConstruct = startCurrentConstruct\n      context.events.length = startEventsIndex\n      stack = startStack\n      accountForPotentialSkip()\n    }\n  }\n\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line]\n      point.offset += columnStart[point.line] - 1\n    }\n  }\n}\n\nmodule.exports = createTokenizer\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9jcmVhdGUtdG9rZW5pemVyLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGFBQWEsbUJBQU8sQ0FBQyxxRkFBdUI7QUFDNUMseUJBQXlCLG1CQUFPLENBQUMsbUhBQXNDO0FBQ3ZFLGtCQUFrQixtQkFBTyxDQUFDLG1GQUFtQjtBQUM3QyxvQkFBb0IsbUJBQU8sQ0FBQyx1RkFBcUI7QUFDakQsZUFBZSxtQkFBTyxDQUFDLDJFQUFlO0FBQ3RDLGlCQUFpQixtQkFBTyxDQUFDLGlGQUFrQjtBQUMzQyxzQkFBc0IsbUJBQU8sQ0FBQywyRkFBdUI7QUFDckQsY0FBYyxtQkFBTyxDQUFDLHlFQUFjO0FBQ3BDLGtCQUFrQixtQkFBTyxDQUFDLG1GQUFtQjs7QUFFN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMLElBQUk7O0FBRUo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTs7QUFFSjs7QUFFQTtBQUNBO0FBQ0EsSUFBSTs7QUFFSjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLElBQUk7O0FBRUo7QUFDQTtBQUNBLElBQUk7O0FBRUo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxNQUFNOztBQUVOO0FBQ0E7QUFDQSxNQUFNO0FBQ047O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNOztBQUVOO0FBQ0EsSUFBSTs7QUFFSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7O0FBRUo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7O0FBRUo7QUFDQTtBQUNBLElBQUk7O0FBRUo7QUFDQTtBQUNBLElBQUk7O0FBRUo7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC91dGlsL2NyZWF0ZS10b2tlbml6ZXIuanM/OGQ5NCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIGFzc2lnbiA9IHJlcXVpcmUoJy4uL2NvbnN0YW50L2Fzc2lnbi5qcycpXG52YXIgbWFya2Rvd25MaW5lRW5kaW5nID0gcmVxdWlyZSgnLi4vY2hhcmFjdGVyL21hcmtkb3duLWxpbmUtZW5kaW5nLmpzJylcbnZhciBjaHVua2VkUHVzaCA9IHJlcXVpcmUoJy4vY2h1bmtlZC1wdXNoLmpzJylcbnZhciBjaHVua2VkU3BsaWNlID0gcmVxdWlyZSgnLi9jaHVua2VkLXNwbGljZS5qcycpXG52YXIgbWluaWZsYXQgPSByZXF1aXJlKCcuL21pbmlmbGF0LmpzJylcbnZhciByZXNvbHZlQWxsID0gcmVxdWlyZSgnLi9yZXNvbHZlLWFsbC5qcycpXG52YXIgc2VyaWFsaXplQ2h1bmtzID0gcmVxdWlyZSgnLi9zZXJpYWxpemUtY2h1bmtzLmpzJylcbnZhciBzaGFsbG93ID0gcmVxdWlyZSgnLi9zaGFsbG93LmpzJylcbnZhciBzbGljZUNodW5rcyA9IHJlcXVpcmUoJy4vc2xpY2UtY2h1bmtzLmpzJylcblxuLy8gQ3JlYXRlIGEgdG9rZW5pemVyLlxuLy8gVG9rZW5pemVycyBkZWFsIHdpdGggb25lIHR5cGUgb2YgZGF0YSAoZS5nLiwgY29udGFpbmVycywgZmxvdywgdGV4dCkuXG4vLyBUaGUgcGFyc2VyIGlzIHRoZSBvYmplY3QgZGVhbGluZyB3aXRoIGl0IGFsbC5cbi8vIGBpbml0aWFsaXplYCB3b3JrcyBsaWtlIG90aGVyIGNvbnN0cnVjdHMsIGV4Y2VwdCB0aGF0IG9ubHkgaXRzIGB0b2tlbml6ZWBcbi8vIGZ1bmN0aW9uIGlzIHVzZWQsIGluIHdoaWNoIGNhc2UgaXQgZG9lc27igJl0IHJlY2VpdmUgYW4gYG9rYCBvciBgbm9rYC5cbi8vIGBmcm9tYCBjYW4gYmUgZ2l2ZW4gdG8gc2V0IHRoZSBwb2ludCBiZWZvcmUgdGhlIGZpcnN0IGNoYXJhY3RlciwgYWx0aG91Z2hcbi8vIHdoZW4gZnVydGhlciBsaW5lcyBhcmUgaW5kZW50ZWQsIHRoZXkgbXVzdCBiZSBzZXQgd2l0aCBgZGVmaW5lU2tpcGAuXG5mdW5jdGlvbiBjcmVhdGVUb2tlbml6ZXIocGFyc2VyLCBpbml0aWFsaXplLCBmcm9tKSB7XG4gIHZhciBwb2ludCA9IGZyb21cbiAgICA/IHNoYWxsb3coZnJvbSlcbiAgICA6IHtcbiAgICAgICAgbGluZTogMSxcbiAgICAgICAgY29sdW1uOiAxLFxuICAgICAgICBvZmZzZXQ6IDBcbiAgICAgIH1cbiAgdmFyIGNvbHVtblN0YXJ0ID0ge31cbiAgdmFyIHJlc29sdmVBbGxDb25zdHJ1Y3RzID0gW11cbiAgdmFyIGNodW5rcyA9IFtdXG4gIHZhciBzdGFjayA9IFtdXG5cbiAgdmFyIGVmZmVjdHMgPSB7XG4gICAgY29uc3VtZTogY29uc3VtZSxcbiAgICBlbnRlcjogZW50ZXIsXG4gICAgZXhpdDogZXhpdCxcbiAgICBhdHRlbXB0OiBjb25zdHJ1Y3RGYWN0b3J5KG9uc3VjY2Vzc2Z1bGNvbnN0cnVjdCksXG4gICAgY2hlY2s6IGNvbnN0cnVjdEZhY3Rvcnkob25zdWNjZXNzZnVsY2hlY2spLFxuICAgIGludGVycnVwdDogY29uc3RydWN0RmFjdG9yeShvbnN1Y2Nlc3NmdWxjaGVjaywge1xuICAgICAgaW50ZXJydXB0OiB0cnVlXG4gICAgfSksXG4gICAgbGF6eTogY29uc3RydWN0RmFjdG9yeShvbnN1Y2Nlc3NmdWxjaGVjaywge1xuICAgICAgbGF6eTogdHJ1ZVxuICAgIH0pXG4gIH0gLy8gU3RhdGUgYW5kIHRvb2xzIGZvciByZXNvbHZpbmcgYW5kIHNlcmlhbGl6aW5nLlxuXG4gIHZhciBjb250ZXh0ID0ge1xuICAgIHByZXZpb3VzOiBudWxsLFxuICAgIGV2ZW50czogW10sXG4gICAgcGFyc2VyOiBwYXJzZXIsXG4gICAgc2xpY2VTdHJlYW06IHNsaWNlU3RyZWFtLFxuICAgIHNsaWNlU2VyaWFsaXplOiBzbGljZVNlcmlhbGl6ZSxcbiAgICBub3c6IG5vdyxcbiAgICBkZWZpbmVTa2lwOiBza2lwLFxuICAgIHdyaXRlOiB3cml0ZVxuICB9IC8vIFRoZSBzdGF0ZSBmdW5jdGlvbi5cblxuICB2YXIgc3RhdGUgPSBpbml0aWFsaXplLnRva2VuaXplLmNhbGwoY29udGV4dCwgZWZmZWN0cykgLy8gVHJhY2sgd2hpY2ggY2hhcmFjdGVyIHdlIGV4cGVjdCB0byBiZSBjb25zdW1lZCwgdG8gY2F0Y2ggYnVncy5cblxuICBpZiAoaW5pdGlhbGl6ZS5yZXNvbHZlQWxsKSB7XG4gICAgcmVzb2x2ZUFsbENvbnN0cnVjdHMucHVzaChpbml0aWFsaXplKVxuICB9IC8vIFN0b3JlIHdoZXJlIHdlIGFyZSBpbiB0aGUgaW5wdXQgc3RyZWFtLlxuXG4gIHBvaW50Ll9pbmRleCA9IDBcbiAgcG9pbnQuX2J1ZmZlckluZGV4ID0gLTFcbiAgcmV0dXJuIGNvbnRleHRcblxuICBmdW5jdGlvbiB3cml0ZShzbGljZSkge1xuICAgIGNodW5rcyA9IGNodW5rZWRQdXNoKGNodW5rcywgc2xpY2UpXG4gICAgbWFpbigpIC8vIEV4aXQgaWYgd2XigJlyZSBub3QgZG9uZSwgcmVzb2x2ZSBtaWdodCBjaGFuZ2Ugc3R1ZmYuXG5cbiAgICBpZiAoY2h1bmtzW2NodW5rcy5sZW5ndGggLSAxXSAhPT0gbnVsbCkge1xuICAgICAgcmV0dXJuIFtdXG4gICAgfVxuXG4gICAgYWRkUmVzdWx0KGluaXRpYWxpemUsIDApIC8vIE90aGVyd2lzZSwgcmVzb2x2ZSwgYW5kIGV4aXQuXG5cbiAgICBjb250ZXh0LmV2ZW50cyA9IHJlc29sdmVBbGwocmVzb2x2ZUFsbENvbnN0cnVjdHMsIGNvbnRleHQuZXZlbnRzLCBjb250ZXh0KVxuICAgIHJldHVybiBjb250ZXh0LmV2ZW50c1xuICB9IC8vXG4gIC8vIFRvb2xzLlxuICAvL1xuXG4gIGZ1bmN0aW9uIHNsaWNlU2VyaWFsaXplKHRva2VuKSB7XG4gICAgcmV0dXJuIHNlcmlhbGl6ZUNodW5rcyhzbGljZVN0cmVhbSh0b2tlbikpXG4gIH1cblxuICBmdW5jdGlvbiBzbGljZVN0cmVhbSh0b2tlbikge1xuICAgIHJldHVybiBzbGljZUNodW5rcyhjaHVua3MsIHRva2VuKVxuICB9XG5cbiAgZnVuY3Rpb24gbm93KCkge1xuICAgIHJldHVybiBzaGFsbG93KHBvaW50KVxuICB9XG5cbiAgZnVuY3Rpb24gc2tpcCh2YWx1ZSkge1xuICAgIGNvbHVtblN0YXJ0W3ZhbHVlLmxpbmVdID0gdmFsdWUuY29sdW1uXG4gICAgYWNjb3VudEZvclBvdGVudGlhbFNraXAoKVxuICB9IC8vXG4gIC8vIFN0YXRlIG1hbmFnZW1lbnQuXG4gIC8vXG4gIC8vIE1haW4gbG9vcCAobm90ZSB0aGF0IGBfaW5kZXhgIGFuZCBgX2J1ZmZlckluZGV4YCBpbiBgcG9pbnRgIGFyZSBtb2RpZmllZCBieVxuICAvLyBgY29uc3VtZWApLlxuICAvLyBIZXJlIGlzIHdoZXJlIHdlIHdhbGsgdGhyb3VnaCB0aGUgY2h1bmtzLCB3aGljaCBlaXRoZXIgaW5jbHVkZSBzdHJpbmdzIG9mXG4gIC8vIHNldmVyYWwgY2hhcmFjdGVycywgb3IgbnVtZXJpY2FsIGNoYXJhY3RlciBjb2Rlcy5cbiAgLy8gVGhlIHJlYXNvbiB0byBkbyB0aGlzIGluIGEgbG9vcCBpbnN0ZWFkIG9mIGEgY2FsbCBpcyBzbyB0aGUgc3RhY2sgY2FuXG4gIC8vIGRyYWluLlxuXG4gIGZ1bmN0aW9uIG1haW4oKSB7XG4gICAgdmFyIGNodW5rSW5kZXhcbiAgICB2YXIgY2h1bmtcblxuICAgIHdoaWxlIChwb2ludC5faW5kZXggPCBjaHVua3MubGVuZ3RoKSB7XG4gICAgICBjaHVuayA9IGNodW5rc1twb2ludC5faW5kZXhdIC8vIElmIHdl4oCZcmUgaW4gYSBidWZmZXIgY2h1bmssIGxvb3AgdGhyb3VnaCBpdC5cblxuICAgICAgaWYgKHR5cGVvZiBjaHVuayA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgY2h1bmtJbmRleCA9IHBvaW50Ll9pbmRleFxuXG4gICAgICAgIGlmIChwb2ludC5fYnVmZmVySW5kZXggPCAwKSB7XG4gICAgICAgICAgcG9pbnQuX2J1ZmZlckluZGV4ID0gMFxuICAgICAgICB9XG5cbiAgICAgICAgd2hpbGUgKFxuICAgICAgICAgIHBvaW50Ll9pbmRleCA9PT0gY2h1bmtJbmRleCAmJlxuICAgICAgICAgIHBvaW50Ll9idWZmZXJJbmRleCA8IGNodW5rLmxlbmd0aFxuICAgICAgICApIHtcbiAgICAgICAgICBnbyhjaHVuay5jaGFyQ29kZUF0KHBvaW50Ll9idWZmZXJJbmRleCkpXG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGdvKGNodW5rKVxuICAgICAgfVxuICAgIH1cbiAgfSAvLyBEZWFsIHdpdGggb25lIGNvZGUuXG5cbiAgZnVuY3Rpb24gZ28oY29kZSkge1xuICAgIHN0YXRlID0gc3RhdGUoY29kZSlcbiAgfSAvLyBNb3ZlIGEgY2hhcmFjdGVyIGZvcndhcmQuXG5cbiAgZnVuY3Rpb24gY29uc3VtZShjb2RlKSB7XG4gICAgaWYgKG1hcmtkb3duTGluZUVuZGluZyhjb2RlKSkge1xuICAgICAgcG9pbnQubGluZSsrXG4gICAgICBwb2ludC5jb2x1bW4gPSAxXG4gICAgICBwb2ludC5vZmZzZXQgKz0gY29kZSA9PT0gLTMgPyAyIDogMVxuICAgICAgYWNjb3VudEZvclBvdGVudGlhbFNraXAoKVxuICAgIH0gZWxzZSBpZiAoY29kZSAhPT0gLTEpIHtcbiAgICAgIHBvaW50LmNvbHVtbisrXG4gICAgICBwb2ludC5vZmZzZXQrK1xuICAgIH0gLy8gTm90IGluIGEgc3RyaW5nIGNodW5rLlxuXG4gICAgaWYgKHBvaW50Ll9idWZmZXJJbmRleCA8IDApIHtcbiAgICAgIHBvaW50Ll9pbmRleCsrXG4gICAgfSBlbHNlIHtcbiAgICAgIHBvaW50Ll9idWZmZXJJbmRleCsrIC8vIEF0IGVuZCBvZiBzdHJpbmcgY2h1bmsuXG5cbiAgICAgIGlmIChwb2ludC5fYnVmZmVySW5kZXggPT09IGNodW5rc1twb2ludC5faW5kZXhdLmxlbmd0aCkge1xuICAgICAgICBwb2ludC5fYnVmZmVySW5kZXggPSAtMVxuICAgICAgICBwb2ludC5faW5kZXgrK1xuICAgICAgfVxuICAgIH0gLy8gRXhwb3NlIHRoZSBwcmV2aW91cyBjaGFyYWN0ZXIuXG5cbiAgICBjb250ZXh0LnByZXZpb3VzID0gY29kZSAvLyBNYXJrIGFzIGNvbnN1bWVkLlxuICB9IC8vIFN0YXJ0IGEgdG9rZW4uXG5cbiAgZnVuY3Rpb24gZW50ZXIodHlwZSwgZmllbGRzKSB7XG4gICAgdmFyIHRva2VuID0gZmllbGRzIHx8IHt9XG4gICAgdG9rZW4udHlwZSA9IHR5cGVcbiAgICB0b2tlbi5zdGFydCA9IG5vdygpXG4gICAgY29udGV4dC5ldmVudHMucHVzaChbJ2VudGVyJywgdG9rZW4sIGNvbnRleHRdKVxuICAgIHN0YWNrLnB1c2godG9rZW4pXG4gICAgcmV0dXJuIHRva2VuXG4gIH0gLy8gU3RvcCBhIHRva2VuLlxuXG4gIGZ1bmN0aW9uIGV4aXQodHlwZSkge1xuICAgIHZhciB0b2tlbiA9IHN0YWNrLnBvcCgpXG4gICAgdG9rZW4uZW5kID0gbm93KClcbiAgICBjb250ZXh0LmV2ZW50cy5wdXNoKFsnZXhpdCcsIHRva2VuLCBjb250ZXh0XSlcbiAgICByZXR1cm4gdG9rZW5cbiAgfSAvLyBVc2UgcmVzdWx0cy5cblxuICBmdW5jdGlvbiBvbnN1Y2Nlc3NmdWxjb25zdHJ1Y3QoY29uc3RydWN0LCBpbmZvKSB7XG4gICAgYWRkUmVzdWx0KGNvbnN0cnVjdCwgaW5mby5mcm9tKVxuICB9IC8vIERpc2NhcmQgcmVzdWx0cy5cblxuICBmdW5jdGlvbiBvbnN1Y2Nlc3NmdWxjaGVjayhjb25zdHJ1Y3QsIGluZm8pIHtcbiAgICBpbmZvLnJlc3RvcmUoKVxuICB9IC8vIEZhY3RvcnkgdG8gYXR0ZW1wdC9jaGVjay9pbnRlcnJ1cHQuXG5cbiAgZnVuY3Rpb24gY29uc3RydWN0RmFjdG9yeShvbnJldHVybiwgZmllbGRzKSB7XG4gICAgcmV0dXJuIGhvb2sgLy8gSGFuZGxlIGVpdGhlciBhbiBvYmplY3QgbWFwcGluZyBjb2RlcyB0byBjb25zdHJ1Y3RzLCBhIGxpc3Qgb2ZcbiAgICAvLyBjb25zdHJ1Y3RzLCBvciBhIHNpbmdsZSBjb25zdHJ1Y3QuXG5cbiAgICBmdW5jdGlvbiBob29rKGNvbnN0cnVjdHMsIHJldHVyblN0YXRlLCBib2d1c1N0YXRlKSB7XG4gICAgICB2YXIgbGlzdE9mQ29uc3RydWN0c1xuICAgICAgdmFyIGNvbnN0cnVjdEluZGV4XG4gICAgICB2YXIgY3VycmVudENvbnN0cnVjdFxuICAgICAgdmFyIGluZm9cbiAgICAgIHJldHVybiBjb25zdHJ1Y3RzLnRva2VuaXplIHx8ICdsZW5ndGgnIGluIGNvbnN0cnVjdHNcbiAgICAgICAgPyBoYW5kbGVMaXN0T2ZDb25zdHJ1Y3RzKG1pbmlmbGF0KGNvbnN0cnVjdHMpKVxuICAgICAgICA6IGhhbmRsZU1hcE9mQ29uc3RydWN0c1xuXG4gICAgICBmdW5jdGlvbiBoYW5kbGVNYXBPZkNvbnN0cnVjdHMoY29kZSkge1xuICAgICAgICBpZiAoY29kZSBpbiBjb25zdHJ1Y3RzIHx8IG51bGwgaW4gY29uc3RydWN0cykge1xuICAgICAgICAgIHJldHVybiBoYW5kbGVMaXN0T2ZDb25zdHJ1Y3RzKFxuICAgICAgICAgICAgY29uc3RydWN0cy5udWxsXG4gICAgICAgICAgICAgID8gLyogYzggaWdub3JlIG5leHQgKi9cbiAgICAgICAgICAgICAgICBtaW5pZmxhdChjb25zdHJ1Y3RzW2NvZGVdKS5jb25jYXQobWluaWZsYXQoY29uc3RydWN0cy5udWxsKSlcbiAgICAgICAgICAgICAgOiBjb25zdHJ1Y3RzW2NvZGVdXG4gICAgICAgICAgKShjb2RlKVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGJvZ3VzU3RhdGUoY29kZSlcbiAgICAgIH1cblxuICAgICAgZnVuY3Rpb24gaGFuZGxlTGlzdE9mQ29uc3RydWN0cyhsaXN0KSB7XG4gICAgICAgIGxpc3RPZkNvbnN0cnVjdHMgPSBsaXN0XG4gICAgICAgIGNvbnN0cnVjdEluZGV4ID0gMFxuICAgICAgICByZXR1cm4gaGFuZGxlQ29uc3RydWN0KGxpc3RbY29uc3RydWN0SW5kZXhdKVxuICAgICAgfVxuXG4gICAgICBmdW5jdGlvbiBoYW5kbGVDb25zdHJ1Y3QoY29uc3RydWN0KSB7XG4gICAgICAgIHJldHVybiBzdGFydFxuXG4gICAgICAgIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICAgICAgICAvLyBUbyBkbzogbm90IG5lZGUgdG8gc3RvcmUgaWYgdGhlcmUgaXMgbm8gYm9ndXMgc3RhdGUsIHByb2JhYmx5P1xuICAgICAgICAgIC8vIEN1cnJlbnRseSBkb2VzbuKAmXQgd29yayBiZWNhdXNlIGBpbnNwZWN0YCBpbiBkb2N1bWVudCBkb2VzIGEgY2hlY2tcbiAgICAgICAgICAvLyB3L28gYSBib2d1cywgd2hpY2ggZG9lc27igJl0IG1ha2Ugc2Vuc2UuIEJ1dCBpdCBkb2VzIHNlZW0gdG8gaGVscCBwZXJmXG4gICAgICAgICAgLy8gYnkgbm90IHN0b3JpbmcuXG4gICAgICAgICAgaW5mbyA9IHN0b3JlKClcbiAgICAgICAgICBjdXJyZW50Q29uc3RydWN0ID0gY29uc3RydWN0XG5cbiAgICAgICAgICBpZiAoIWNvbnN0cnVjdC5wYXJ0aWFsKSB7XG4gICAgICAgICAgICBjb250ZXh0LmN1cnJlbnRDb25zdHJ1Y3QgPSBjb25zdHJ1Y3RcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBpZiAoXG4gICAgICAgICAgICBjb25zdHJ1Y3QubmFtZSAmJlxuICAgICAgICAgICAgY29udGV4dC5wYXJzZXIuY29uc3RydWN0cy5kaXNhYmxlLm51bGwuaW5kZXhPZihjb25zdHJ1Y3QubmFtZSkgPiAtMVxuICAgICAgICAgICkge1xuICAgICAgICAgICAgcmV0dXJuIG5vaygpXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgcmV0dXJuIGNvbnN0cnVjdC50b2tlbml6ZS5jYWxsKFxuICAgICAgICAgICAgZmllbGRzID8gYXNzaWduKHt9LCBjb250ZXh0LCBmaWVsZHMpIDogY29udGV4dCxcbiAgICAgICAgICAgIGVmZmVjdHMsXG4gICAgICAgICAgICBvayxcbiAgICAgICAgICAgIG5va1xuICAgICAgICAgICkoY29kZSlcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBmdW5jdGlvbiBvayhjb2RlKSB7XG4gICAgICAgIG9ucmV0dXJuKGN1cnJlbnRDb25zdHJ1Y3QsIGluZm8pXG4gICAgICAgIHJldHVybiByZXR1cm5TdGF0ZVxuICAgICAgfVxuXG4gICAgICBmdW5jdGlvbiBub2soY29kZSkge1xuICAgICAgICBpbmZvLnJlc3RvcmUoKVxuXG4gICAgICAgIGlmICgrK2NvbnN0cnVjdEluZGV4IDwgbGlzdE9mQ29uc3RydWN0cy5sZW5ndGgpIHtcbiAgICAgICAgICByZXR1cm4gaGFuZGxlQ29uc3RydWN0KGxpc3RPZkNvbnN0cnVjdHNbY29uc3RydWN0SW5kZXhdKVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGJvZ3VzU3RhdGVcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBmdW5jdGlvbiBhZGRSZXN1bHQoY29uc3RydWN0LCBmcm9tKSB7XG4gICAgaWYgKGNvbnN0cnVjdC5yZXNvbHZlQWxsICYmIHJlc29sdmVBbGxDb25zdHJ1Y3RzLmluZGV4T2YoY29uc3RydWN0KSA8IDApIHtcbiAgICAgIHJlc29sdmVBbGxDb25zdHJ1Y3RzLnB1c2goY29uc3RydWN0KVxuICAgIH1cblxuICAgIGlmIChjb25zdHJ1Y3QucmVzb2x2ZSkge1xuICAgICAgY2h1bmtlZFNwbGljZShcbiAgICAgICAgY29udGV4dC5ldmVudHMsXG4gICAgICAgIGZyb20sXG4gICAgICAgIGNvbnRleHQuZXZlbnRzLmxlbmd0aCAtIGZyb20sXG4gICAgICAgIGNvbnN0cnVjdC5yZXNvbHZlKGNvbnRleHQuZXZlbnRzLnNsaWNlKGZyb20pLCBjb250ZXh0KVxuICAgICAgKVxuICAgIH1cblxuICAgIGlmIChjb25zdHJ1Y3QucmVzb2x2ZVRvKSB7XG4gICAgICBjb250ZXh0LmV2ZW50cyA9IGNvbnN0cnVjdC5yZXNvbHZlVG8oY29udGV4dC5ldmVudHMsIGNvbnRleHQpXG4gICAgfVxuICB9XG5cbiAgZnVuY3Rpb24gc3RvcmUoKSB7XG4gICAgdmFyIHN0YXJ0UG9pbnQgPSBub3coKVxuICAgIHZhciBzdGFydFByZXZpb3VzID0gY29udGV4dC5wcmV2aW91c1xuICAgIHZhciBzdGFydEN1cnJlbnRDb25zdHJ1Y3QgPSBjb250ZXh0LmN1cnJlbnRDb25zdHJ1Y3RcbiAgICB2YXIgc3RhcnRFdmVudHNJbmRleCA9IGNvbnRleHQuZXZlbnRzLmxlbmd0aFxuICAgIHZhciBzdGFydFN0YWNrID0gQXJyYXkuZnJvbShzdGFjaylcbiAgICByZXR1cm4ge1xuICAgICAgcmVzdG9yZTogcmVzdG9yZSxcbiAgICAgIGZyb206IHN0YXJ0RXZlbnRzSW5kZXhcbiAgICB9XG5cbiAgICBmdW5jdGlvbiByZXN0b3JlKCkge1xuICAgICAgcG9pbnQgPSBzdGFydFBvaW50XG4gICAgICBjb250ZXh0LnByZXZpb3VzID0gc3RhcnRQcmV2aW91c1xuICAgICAgY29udGV4dC5jdXJyZW50Q29uc3RydWN0ID0gc3RhcnRDdXJyZW50Q29uc3RydWN0XG4gICAgICBjb250ZXh0LmV2ZW50cy5sZW5ndGggPSBzdGFydEV2ZW50c0luZGV4XG4gICAgICBzdGFjayA9IHN0YXJ0U3RhY2tcbiAgICAgIGFjY291bnRGb3JQb3RlbnRpYWxTa2lwKClcbiAgICB9XG4gIH1cblxuICBmdW5jdGlvbiBhY2NvdW50Rm9yUG90ZW50aWFsU2tpcCgpIHtcbiAgICBpZiAocG9pbnQubGluZSBpbiBjb2x1bW5TdGFydCAmJiBwb2ludC5jb2x1bW4gPCAyKSB7XG4gICAgICBwb2ludC5jb2x1bW4gPSBjb2x1bW5TdGFydFtwb2ludC5saW5lXVxuICAgICAgcG9pbnQub2Zmc2V0ICs9IGNvbHVtblN0YXJ0W3BvaW50LmxpbmVdIC0gMVxuICAgIH1cbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IGNyZWF0ZVRva2VuaXplclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/create-tokenizer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/miniflat.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dist/util/miniflat.js ***!
  \******************************************************/
/***/ ((module) => {

eval("\n\nfunction miniflat(value) {\n  return value === null || value === undefined\n    ? []\n    : 'length' in value\n    ? value\n    : [value]\n}\n\nmodule.exports = miniflat\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9taW5pZmxhdC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9taW5pZmxhdC5qcz8zNDI0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5mdW5jdGlvbiBtaW5pZmxhdCh2YWx1ZSkge1xuICByZXR1cm4gdmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZFxuICAgID8gW11cbiAgICA6ICdsZW5ndGgnIGluIHZhbHVlXG4gICAgPyB2YWx1ZVxuICAgIDogW3ZhbHVlXVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IG1pbmlmbGF0XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/miniflat.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/move-point.js":
/*!********************************************************!*\
  !*** ./node_modules/micromark/dist/util/move-point.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\n\n// chunks (replacement characters, tabs, or line endings).\n\nfunction movePoint(point, offset) {\n  point.column += offset\n  point.offset += offset\n  point._bufferIndex += offset\n  return point\n}\n\nmodule.exports = movePoint\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9tb3ZlLXBvaW50LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9tb3ZlLXBvaW50LmpzPzY1ZjQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbi8vIGNodW5rcyAocmVwbGFjZW1lbnQgY2hhcmFjdGVycywgdGFicywgb3IgbGluZSBlbmRpbmdzKS5cblxuZnVuY3Rpb24gbW92ZVBvaW50KHBvaW50LCBvZmZzZXQpIHtcbiAgcG9pbnQuY29sdW1uICs9IG9mZnNldFxuICBwb2ludC5vZmZzZXQgKz0gb2Zmc2V0XG4gIHBvaW50Ll9idWZmZXJJbmRleCArPSBvZmZzZXRcbiAgcmV0dXJuIHBvaW50XG59XG5cbm1vZHVsZS5leHBvcnRzID0gbW92ZVBvaW50XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/move-point.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/normalize-identifier.js":
/*!******************************************************************!*\
  !*** ./node_modules/micromark/dist/util/normalize-identifier.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("\n\nfunction normalizeIdentifier(value) {\n  return (\n    value // Collapse Markdown whitespace.\n      .replace(/[\\t\\n\\r ]+/g, ' ') // Trim.\n      .replace(/^ | $/g, '') // Some characters are considered “uppercase”, but if their lowercase\n      // counterpart is uppercased will result in a different uppercase\n      // character.\n      // Hence, to get that form, we perform both lower- and uppercase.\n      // Upper case makes sure keys will not interact with default prototypal\n      // methods: no object method is uppercase.\n      .toLowerCase()\n      .toUpperCase()\n  )\n}\n\nmodule.exports = normalizeIdentifier\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9ub3JtYWxpemUtaWRlbnRpZmllci5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC91dGlsL25vcm1hbGl6ZS1pZGVudGlmaWVyLmpzPzk1ZTQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmZ1bmN0aW9uIG5vcm1hbGl6ZUlkZW50aWZpZXIodmFsdWUpIHtcbiAgcmV0dXJuIChcbiAgICB2YWx1ZSAvLyBDb2xsYXBzZSBNYXJrZG93biB3aGl0ZXNwYWNlLlxuICAgICAgLnJlcGxhY2UoL1tcXHRcXG5cXHIgXSsvZywgJyAnKSAvLyBUcmltLlxuICAgICAgLnJlcGxhY2UoL14gfCAkL2csICcnKSAvLyBTb21lIGNoYXJhY3RlcnMgYXJlIGNvbnNpZGVyZWQg4oCcdXBwZXJjYXNl4oCdLCBidXQgaWYgdGhlaXIgbG93ZXJjYXNlXG4gICAgICAvLyBjb3VudGVycGFydCBpcyB1cHBlcmNhc2VkIHdpbGwgcmVzdWx0IGluIGEgZGlmZmVyZW50IHVwcGVyY2FzZVxuICAgICAgLy8gY2hhcmFjdGVyLlxuICAgICAgLy8gSGVuY2UsIHRvIGdldCB0aGF0IGZvcm0sIHdlIHBlcmZvcm0gYm90aCBsb3dlci0gYW5kIHVwcGVyY2FzZS5cbiAgICAgIC8vIFVwcGVyIGNhc2UgbWFrZXMgc3VyZSBrZXlzIHdpbGwgbm90IGludGVyYWN0IHdpdGggZGVmYXVsdCBwcm90b3R5cGFsXG4gICAgICAvLyBtZXRob2RzOiBubyBvYmplY3QgbWV0aG9kIGlzIHVwcGVyY2FzZS5cbiAgICAgIC50b0xvd2VyQ2FzZSgpXG4gICAgICAudG9VcHBlckNhc2UoKVxuICApXG59XG5cbm1vZHVsZS5leHBvcnRzID0gbm9ybWFsaXplSWRlbnRpZmllclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/normalize-identifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/prefix-size.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark/dist/util/prefix-size.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar sizeChunks = __webpack_require__(/*! ./size-chunks.js */ \"(ssr)/./node_modules/micromark/dist/util/size-chunks.js\")\n\nfunction prefixSize(events, type) {\n  var tail = events[events.length - 1]\n  if (!tail || tail[1].type !== type) return 0\n  return sizeChunks(tail[2].sliceStream(tail[1]))\n}\n\nmodule.exports = prefixSize\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9wcmVmaXgtc2l6ZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixpQkFBaUIsbUJBQU8sQ0FBQyxpRkFBa0I7O0FBRTNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L3V0aWwvcHJlZml4LXNpemUuanM/MDYyMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIHNpemVDaHVua3MgPSByZXF1aXJlKCcuL3NpemUtY2h1bmtzLmpzJylcblxuZnVuY3Rpb24gcHJlZml4U2l6ZShldmVudHMsIHR5cGUpIHtcbiAgdmFyIHRhaWwgPSBldmVudHNbZXZlbnRzLmxlbmd0aCAtIDFdXG4gIGlmICghdGFpbCB8fCB0YWlsWzFdLnR5cGUgIT09IHR5cGUpIHJldHVybiAwXG4gIHJldHVybiBzaXplQ2h1bmtzKHRhaWxbMl0uc2xpY2VTdHJlYW0odGFpbFsxXSkpXG59XG5cbm1vZHVsZS5leHBvcnRzID0gcHJlZml4U2l6ZVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/prefix-size.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/regex-check.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark/dist/util/regex-check.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar fromCharCode = __webpack_require__(/*! ../constant/from-char-code.js */ \"(ssr)/./node_modules/micromark/dist/constant/from-char-code.js\")\n\nfunction regexCheck(regex) {\n  return check\n\n  function check(code) {\n    return regex.test(fromCharCode(code))\n  }\n}\n\nmodule.exports = regexCheck\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9yZWdleC1jaGVjay5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixtQkFBbUIsbUJBQU8sQ0FBQyxxR0FBK0I7O0FBRTFEO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L3V0aWwvcmVnZXgtY2hlY2suanM/Zjg2NCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIGZyb21DaGFyQ29kZSA9IHJlcXVpcmUoJy4uL2NvbnN0YW50L2Zyb20tY2hhci1jb2RlLmpzJylcblxuZnVuY3Rpb24gcmVnZXhDaGVjayhyZWdleCkge1xuICByZXR1cm4gY2hlY2tcblxuICBmdW5jdGlvbiBjaGVjayhjb2RlKSB7XG4gICAgcmV0dXJuIHJlZ2V4LnRlc3QoZnJvbUNoYXJDb2RlKGNvZGUpKVxuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gcmVnZXhDaGVja1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/regex-check.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/resolve-all.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark/dist/util/resolve-all.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\nfunction resolveAll(constructs, events, context) {\n  var called = []\n  var index = -1\n  var resolve\n\n  while (++index < constructs.length) {\n    resolve = constructs[index].resolveAll\n\n    if (resolve && called.indexOf(resolve) < 0) {\n      events = resolve(events, context)\n      called.push(resolve)\n    }\n  }\n\n  return events\n}\n\nmodule.exports = resolveAll\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9yZXNvbHZlLWFsbC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9yZXNvbHZlLWFsbC5qcz9lY2JiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5mdW5jdGlvbiByZXNvbHZlQWxsKGNvbnN0cnVjdHMsIGV2ZW50cywgY29udGV4dCkge1xuICB2YXIgY2FsbGVkID0gW11cbiAgdmFyIGluZGV4ID0gLTFcbiAgdmFyIHJlc29sdmVcblxuICB3aGlsZSAoKytpbmRleCA8IGNvbnN0cnVjdHMubGVuZ3RoKSB7XG4gICAgcmVzb2x2ZSA9IGNvbnN0cnVjdHNbaW5kZXhdLnJlc29sdmVBbGxcblxuICAgIGlmIChyZXNvbHZlICYmIGNhbGxlZC5pbmRleE9mKHJlc29sdmUpIDwgMCkge1xuICAgICAgZXZlbnRzID0gcmVzb2x2ZShldmVudHMsIGNvbnRleHQpXG4gICAgICBjYWxsZWQucHVzaChyZXNvbHZlKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBldmVudHNcbn1cblxubW9kdWxlLmV4cG9ydHMgPSByZXNvbHZlQWxsXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/resolve-all.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/safe-from-int.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dist/util/safe-from-int.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar fromCharCode = __webpack_require__(/*! ../constant/from-char-code.js */ \"(ssr)/./node_modules/micromark/dist/constant/from-char-code.js\")\n\nfunction safeFromInt(value, base) {\n  var code = parseInt(value, base)\n\n  if (\n    // C0 except for HT, LF, FF, CR, space\n    code < 9 ||\n    code === 11 ||\n    (code > 13 && code < 32) || // Control character (DEL) of the basic block and C1 controls.\n    (code > 126 && code < 160) || // Lone high surrogates and low surrogates.\n    (code > 55295 && code < 57344) || // Noncharacters.\n    (code > 64975 && code < 65008) ||\n    (code & 65535) === 65535 ||\n    (code & 65535) === 65534 || // Out of range\n    code > 1114111\n  ) {\n    return '\\uFFFD'\n  }\n\n  return fromCharCode(code)\n}\n\nmodule.exports = safeFromInt\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9zYWZlLWZyb20taW50LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLG1CQUFtQixtQkFBTyxDQUFDLHFHQUErQjs7QUFFMUQ7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L3V0aWwvc2FmZS1mcm9tLWludC5qcz9iNGFiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgZnJvbUNoYXJDb2RlID0gcmVxdWlyZSgnLi4vY29uc3RhbnQvZnJvbS1jaGFyLWNvZGUuanMnKVxuXG5mdW5jdGlvbiBzYWZlRnJvbUludCh2YWx1ZSwgYmFzZSkge1xuICB2YXIgY29kZSA9IHBhcnNlSW50KHZhbHVlLCBiYXNlKVxuXG4gIGlmIChcbiAgICAvLyBDMCBleGNlcHQgZm9yIEhULCBMRiwgRkYsIENSLCBzcGFjZVxuICAgIGNvZGUgPCA5IHx8XG4gICAgY29kZSA9PT0gMTEgfHxcbiAgICAoY29kZSA+IDEzICYmIGNvZGUgPCAzMikgfHwgLy8gQ29udHJvbCBjaGFyYWN0ZXIgKERFTCkgb2YgdGhlIGJhc2ljIGJsb2NrIGFuZCBDMSBjb250cm9scy5cbiAgICAoY29kZSA+IDEyNiAmJiBjb2RlIDwgMTYwKSB8fCAvLyBMb25lIGhpZ2ggc3Vycm9nYXRlcyBhbmQgbG93IHN1cnJvZ2F0ZXMuXG4gICAgKGNvZGUgPiA1NTI5NSAmJiBjb2RlIDwgNTczNDQpIHx8IC8vIE5vbmNoYXJhY3RlcnMuXG4gICAgKGNvZGUgPiA2NDk3NSAmJiBjb2RlIDwgNjUwMDgpIHx8XG4gICAgKGNvZGUgJiA2NTUzNSkgPT09IDY1NTM1IHx8XG4gICAgKGNvZGUgJiA2NTUzNSkgPT09IDY1NTM0IHx8IC8vIE91dCBvZiByYW5nZVxuICAgIGNvZGUgPiAxMTE0MTExXG4gICkge1xuICAgIHJldHVybiAnXFx1RkZGRCdcbiAgfVxuXG4gIHJldHVybiBmcm9tQ2hhckNvZGUoY29kZSlcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBzYWZlRnJvbUludFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/safe-from-int.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/serialize-chunks.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark/dist/util/serialize-chunks.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar fromCharCode = __webpack_require__(/*! ../constant/from-char-code.js */ \"(ssr)/./node_modules/micromark/dist/constant/from-char-code.js\")\n\nfunction serializeChunks(chunks) {\n  var index = -1\n  var result = []\n  var chunk\n  var value\n  var atTab\n\n  while (++index < chunks.length) {\n    chunk = chunks[index]\n\n    if (typeof chunk === 'string') {\n      value = chunk\n    } else if (chunk === -5) {\n      value = '\\r'\n    } else if (chunk === -4) {\n      value = '\\n'\n    } else if (chunk === -3) {\n      value = '\\r' + '\\n'\n    } else if (chunk === -2) {\n      value = '\\t'\n    } else if (chunk === -1) {\n      if (atTab) continue\n      value = ' '\n    } else {\n      // Currently only replacement character.\n      value = fromCharCode(chunk)\n    }\n\n    atTab = chunk === -2\n    result.push(value)\n  }\n\n  return result.join('')\n}\n\nmodule.exports = serializeChunks\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9zZXJpYWxpemUtY2h1bmtzLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLG1CQUFtQixtQkFBTyxDQUFDLHFHQUErQjs7QUFFMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9zZXJpYWxpemUtY2h1bmtzLmpzP2YyMTYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBmcm9tQ2hhckNvZGUgPSByZXF1aXJlKCcuLi9jb25zdGFudC9mcm9tLWNoYXItY29kZS5qcycpXG5cbmZ1bmN0aW9uIHNlcmlhbGl6ZUNodW5rcyhjaHVua3MpIHtcbiAgdmFyIGluZGV4ID0gLTFcbiAgdmFyIHJlc3VsdCA9IFtdXG4gIHZhciBjaHVua1xuICB2YXIgdmFsdWVcbiAgdmFyIGF0VGFiXG5cbiAgd2hpbGUgKCsraW5kZXggPCBjaHVua3MubGVuZ3RoKSB7XG4gICAgY2h1bmsgPSBjaHVua3NbaW5kZXhdXG5cbiAgICBpZiAodHlwZW9mIGNodW5rID09PSAnc3RyaW5nJykge1xuICAgICAgdmFsdWUgPSBjaHVua1xuICAgIH0gZWxzZSBpZiAoY2h1bmsgPT09IC01KSB7XG4gICAgICB2YWx1ZSA9ICdcXHInXG4gICAgfSBlbHNlIGlmIChjaHVuayA9PT0gLTQpIHtcbiAgICAgIHZhbHVlID0gJ1xcbidcbiAgICB9IGVsc2UgaWYgKGNodW5rID09PSAtMykge1xuICAgICAgdmFsdWUgPSAnXFxyJyArICdcXG4nXG4gICAgfSBlbHNlIGlmIChjaHVuayA9PT0gLTIpIHtcbiAgICAgIHZhbHVlID0gJ1xcdCdcbiAgICB9IGVsc2UgaWYgKGNodW5rID09PSAtMSkge1xuICAgICAgaWYgKGF0VGFiKSBjb250aW51ZVxuICAgICAgdmFsdWUgPSAnICdcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gQ3VycmVudGx5IG9ubHkgcmVwbGFjZW1lbnQgY2hhcmFjdGVyLlxuICAgICAgdmFsdWUgPSBmcm9tQ2hhckNvZGUoY2h1bmspXG4gICAgfVxuXG4gICAgYXRUYWIgPSBjaHVuayA9PT0gLTJcbiAgICByZXN1bHQucHVzaCh2YWx1ZSlcbiAgfVxuXG4gIHJldHVybiByZXN1bHQuam9pbignJylcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBzZXJpYWxpemVDaHVua3NcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/serialize-chunks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/shallow.js":
/*!*****************************************************!*\
  !*** ./node_modules/micromark/dist/util/shallow.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar assign = __webpack_require__(/*! ../constant/assign.js */ \"(ssr)/./node_modules/micromark/dist/constant/assign.js\")\n\nfunction shallow(object) {\n  return assign({}, object)\n}\n\nmodule.exports = shallow\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9zaGFsbG93LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGFBQWEsbUJBQU8sQ0FBQyxxRkFBdUI7O0FBRTVDO0FBQ0Esa0JBQWtCO0FBQ2xCOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGlzdC91dGlsL3NoYWxsb3cuanM/OGQwYiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIGFzc2lnbiA9IHJlcXVpcmUoJy4uL2NvbnN0YW50L2Fzc2lnbi5qcycpXG5cbmZ1bmN0aW9uIHNoYWxsb3cob2JqZWN0KSB7XG4gIHJldHVybiBhc3NpZ24oe30sIG9iamVjdClcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBzaGFsbG93XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/shallow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/size-chunks.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark/dist/util/size-chunks.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\n// Counts tabs based on their expanded size, and CR+LF as one character.\n\nfunction sizeChunks(chunks) {\n  var index = -1\n  var size = 0\n\n  while (++index < chunks.length) {\n    size += typeof chunks[index] === 'string' ? chunks[index].length : 1\n  }\n\n  return size\n}\n\nmodule.exports = sizeChunks\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9zaXplLWNodW5rcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L3V0aWwvc2l6ZS1jaHVua3MuanM/ODA2NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuLy8gQ291bnRzIHRhYnMgYmFzZWQgb24gdGhlaXIgZXhwYW5kZWQgc2l6ZSwgYW5kIENSK0xGIGFzIG9uZSBjaGFyYWN0ZXIuXG5cbmZ1bmN0aW9uIHNpemVDaHVua3MoY2h1bmtzKSB7XG4gIHZhciBpbmRleCA9IC0xXG4gIHZhciBzaXplID0gMFxuXG4gIHdoaWxlICgrK2luZGV4IDwgY2h1bmtzLmxlbmd0aCkge1xuICAgIHNpemUgKz0gdHlwZW9mIGNodW5rc1tpbmRleF0gPT09ICdzdHJpbmcnID8gY2h1bmtzW2luZGV4XS5sZW5ndGggOiAxXG4gIH1cblxuICByZXR1cm4gc2l6ZVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHNpemVDaHVua3NcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/size-chunks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/slice-chunks.js":
/*!**********************************************************!*\
  !*** ./node_modules/micromark/dist/util/slice-chunks.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("\n\nfunction sliceChunks(chunks, token) {\n  var startIndex = token.start._index\n  var startBufferIndex = token.start._bufferIndex\n  var endIndex = token.end._index\n  var endBufferIndex = token.end._bufferIndex\n  var view\n\n  if (startIndex === endIndex) {\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)]\n  } else {\n    view = chunks.slice(startIndex, endIndex)\n\n    if (startBufferIndex > -1) {\n      view[0] = view[0].slice(startBufferIndex)\n    }\n\n    if (endBufferIndex > 0) {\n      view.push(chunks[endIndex].slice(0, endBufferIndex))\n    }\n  }\n\n  return view\n}\n\nmodule.exports = sliceChunks\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rpc3QvdXRpbC9zbGljZS1jaHVua3MuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kaXN0L3V0aWwvc2xpY2UtY2h1bmtzLmpzP2UzZjQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmZ1bmN0aW9uIHNsaWNlQ2h1bmtzKGNodW5rcywgdG9rZW4pIHtcbiAgdmFyIHN0YXJ0SW5kZXggPSB0b2tlbi5zdGFydC5faW5kZXhcbiAgdmFyIHN0YXJ0QnVmZmVySW5kZXggPSB0b2tlbi5zdGFydC5fYnVmZmVySW5kZXhcbiAgdmFyIGVuZEluZGV4ID0gdG9rZW4uZW5kLl9pbmRleFxuICB2YXIgZW5kQnVmZmVySW5kZXggPSB0b2tlbi5lbmQuX2J1ZmZlckluZGV4XG4gIHZhciB2aWV3XG5cbiAgaWYgKHN0YXJ0SW5kZXggPT09IGVuZEluZGV4KSB7XG4gICAgdmlldyA9IFtjaHVua3Nbc3RhcnRJbmRleF0uc2xpY2Uoc3RhcnRCdWZmZXJJbmRleCwgZW5kQnVmZmVySW5kZXgpXVxuICB9IGVsc2Uge1xuICAgIHZpZXcgPSBjaHVua3Muc2xpY2Uoc3RhcnRJbmRleCwgZW5kSW5kZXgpXG5cbiAgICBpZiAoc3RhcnRCdWZmZXJJbmRleCA+IC0xKSB7XG4gICAgICB2aWV3WzBdID0gdmlld1swXS5zbGljZShzdGFydEJ1ZmZlckluZGV4KVxuICAgIH1cblxuICAgIGlmIChlbmRCdWZmZXJJbmRleCA+IDApIHtcbiAgICAgIHZpZXcucHVzaChjaHVua3NbZW5kSW5kZXhdLnNsaWNlKDAsIGVuZEJ1ZmZlckluZGV4KSlcbiAgICB9XG4gIH1cblxuICByZXR1cm4gdmlld1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHNsaWNlQ2h1bmtzXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/slice-chunks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dist/util/subtokenize.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark/dist/util/subtokenize.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar assign = __webpack_require__(/*! ../constant/assign.js */ \"(ssr)/./node_modules/micromark/dist/constant/assign.js\")\nvar chunkedSplice = __webpack_require__(/*! ./chunked-splice.js */ \"(ssr)/./node_modules/micromark/dist/util/chunked-splice.js\")\nvar shallow = __webpack_require__(/*! ./shallow.js */ \"(ssr)/./node_modules/micromark/dist/util/shallow.js\")\n\nfunction subtokenize(events) {\n  var jumps = {}\n  var index = -1\n  var event\n  var lineIndex\n  var otherIndex\n  var otherEvent\n  var parameters\n  var subevents\n  var more\n\n  while (++index < events.length) {\n    while (index in jumps) {\n      index = jumps[index]\n    }\n\n    event = events[index] // Add a hook for the GFM tasklist extension, which needs to know if text\n    // is in the first content of a list item.\n\n    if (\n      index &&\n      event[1].type === 'chunkFlow' &&\n      events[index - 1][1].type === 'listItemPrefix'\n    ) {\n      subevents = event[1]._tokenizer.events\n      otherIndex = 0\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === 'lineEndingBlank'\n      ) {\n        otherIndex += 2\n      }\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === 'content'\n      ) {\n        while (++otherIndex < subevents.length) {\n          if (subevents[otherIndex][1].type === 'content') {\n            break\n          }\n\n          if (subevents[otherIndex][1].type === 'chunkText') {\n            subevents[otherIndex][1].isInFirstContentOfListItem = true\n            otherIndex++\n          }\n        }\n      }\n    } // Enter.\n\n    if (event[0] === 'enter') {\n      if (event[1].contentType) {\n        assign(jumps, subcontent(events, index))\n        index = jumps[index]\n        more = true\n      }\n    } // Exit.\n    else if (event[1]._container || event[1]._movePreviousLineEndings) {\n      otherIndex = index\n      lineIndex = undefined\n\n      while (otherIndex--) {\n        otherEvent = events[otherIndex]\n\n        if (\n          otherEvent[1].type === 'lineEnding' ||\n          otherEvent[1].type === 'lineEndingBlank'\n        ) {\n          if (otherEvent[0] === 'enter') {\n            if (lineIndex) {\n              events[lineIndex][1].type = 'lineEndingBlank'\n            }\n\n            otherEvent[1].type = 'lineEnding'\n            lineIndex = otherIndex\n          }\n        } else {\n          break\n        }\n      }\n\n      if (lineIndex) {\n        // Fix position.\n        event[1].end = shallow(events[lineIndex][1].start) // Switch container exit w/ line endings.\n\n        parameters = events.slice(lineIndex, index)\n        parameters.unshift(event)\n        chunkedSplice(events, lineIndex, index - lineIndex + 1, parameters)\n      }\n    }\n  }\n\n  return !more\n}\n\nfunction subcontent(events, eventIndex) {\n  var token = events[eventIndex][1]\n  var context = events[eventIndex][2]\n  var startPosition = eventIndex - 1\n  var startPositions = []\n  var tokenizer =\n    token._tokenizer || context.parser[token.contentType](token.start)\n  var childEvents = tokenizer.events\n  var jumps = []\n  var gaps = {}\n  var stream\n  var previous\n  var index\n  var entered\n  var end\n  var adjust // Loop forward through the linked tokens to pass them in order to the\n  // subtokenizer.\n\n  while (token) {\n    // Find the position of the event for this token.\n    while (events[++startPosition][1] !== token) {\n      // Empty.\n    }\n\n    startPositions.push(startPosition)\n\n    if (!token._tokenizer) {\n      stream = context.sliceStream(token)\n\n      if (!token.next) {\n        stream.push(null)\n      }\n\n      if (previous) {\n        tokenizer.defineSkip(token.start)\n      }\n\n      if (token.isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = true\n      }\n\n      tokenizer.write(stream)\n\n      if (token.isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = undefined\n      }\n    } // Unravel the next token.\n\n    previous = token\n    token = token.next\n  } // Now, loop back through all events (and linked tokens), to figure out which\n  // parts belong where.\n\n  token = previous\n  index = childEvents.length\n\n  while (index--) {\n    // Make sure we’ve at least seen something (final eol is part of the last\n    // token).\n    if (childEvents[index][0] === 'enter') {\n      entered = true\n    } else if (\n      // Find a void token that includes a break.\n      entered &&\n      childEvents[index][1].type === childEvents[index - 1][1].type &&\n      childEvents[index][1].start.line !== childEvents[index][1].end.line\n    ) {\n      add(childEvents.slice(index + 1, end))\n      // Help GC.\n      token._tokenizer = token.next = undefined\n      token = token.previous\n      end = index + 1\n    }\n  }\n\n  // Help GC.\n  tokenizer.events = token._tokenizer = token.next = undefined // Do head:\n\n  add(childEvents.slice(0, end))\n  index = -1\n  adjust = 0\n\n  while (++index < jumps.length) {\n    gaps[adjust + jumps[index][0]] = adjust + jumps[index][1]\n    adjust += jumps[index][1] - jumps[index][0] - 1\n  }\n\n  return gaps\n\n  function add(slice) {\n    var start = startPositions.pop()\n    jumps.unshift([start, start + slice.length - 1])\n    chunkedSplice(events, start, 2, slice)\n  }\n}\n\nmodule.exports = subtokenize\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dist/util/subtokenize.js\n");

/***/ })

};
;