/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-strikethrough";
exports.ids = ["vendor-chunks/micromark-extension-gfm-strikethrough"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-strikethrough/index.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-strikethrough/index.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = create\n\nvar classifyCharacter = __webpack_require__(/*! micromark/dist/util/classify-character */ \"(ssr)/./node_modules/micromark/dist/util/classify-character.js\")\nvar chunkedSplice = __webpack_require__(/*! micromark/dist/util/chunked-splice */ \"(ssr)/./node_modules/micromark/dist/util/chunked-splice.js\")\nvar resolveAll = __webpack_require__(/*! micromark/dist/util/resolve-all */ \"(ssr)/./node_modules/micromark/dist/util/resolve-all.js\")\nvar shallow = __webpack_require__(/*! micromark/dist/util/shallow */ \"(ssr)/./node_modules/micromark/dist/util/shallow.js\")\n\nfunction create(options) {\n  var settings = options || {}\n  var single = settings.singleTilde\n  var tokenizer = {\n    tokenize: tokenizeStrikethrough,\n    resolveAll: resolveAllStrikethrough\n  }\n\n  if (single === null || single === undefined) {\n    single = true\n  }\n\n  return {text: {126: tokenizer}, insideSpan: {null: tokenizer}}\n\n  // Take events and resolve strikethrough.\n  function resolveAllStrikethrough(events, context) {\n    var index = -1\n    var strikethrough\n    var text\n    var open\n    var nextEvents\n\n    // Walk through all events.\n    while (++index < events.length) {\n      // Find a token that can close.\n      if (\n        events[index][0] === 'enter' &&\n        events[index][1].type === 'strikethroughSequenceTemporary' &&\n        events[index][1]._close\n      ) {\n        open = index\n\n        // Now walk back to find an opener.\n        while (open--) {\n          // Find a token that can open the closer.\n          if (\n            events[open][0] === 'exit' &&\n            events[open][1].type === 'strikethroughSequenceTemporary' &&\n            events[open][1]._open &&\n            // If the sizes are the same:\n            events[index][1].end.offset - events[index][1].start.offset ===\n              events[open][1].end.offset - events[open][1].start.offset\n          ) {\n            events[index][1].type = 'strikethroughSequence'\n            events[open][1].type = 'strikethroughSequence'\n\n            strikethrough = {\n              type: 'strikethrough',\n              start: shallow(events[open][1].start),\n              end: shallow(events[index][1].end)\n            }\n\n            text = {\n              type: 'strikethroughText',\n              start: shallow(events[open][1].end),\n              end: shallow(events[index][1].start)\n            }\n\n            // Opening.\n            nextEvents = [\n              ['enter', strikethrough, context],\n              ['enter', events[open][1], context],\n              ['exit', events[open][1], context],\n              ['enter', text, context]\n            ]\n\n            // Between.\n            chunkedSplice(\n              nextEvents,\n              nextEvents.length,\n              0,\n              resolveAll(\n                context.parser.constructs.insideSpan.null,\n                events.slice(open + 1, index),\n                context\n              )\n            )\n\n            // Closing.\n            chunkedSplice(nextEvents, nextEvents.length, 0, [\n              ['exit', text, context],\n              ['enter', events[index][1], context],\n              ['exit', events[index][1], context],\n              ['exit', strikethrough, context]\n            ])\n\n            chunkedSplice(events, open - 1, index - open + 3, nextEvents)\n\n            index = open + nextEvents.length - 2\n            break\n          }\n        }\n      }\n    }\n\n    return removeRemainingSequences(events)\n  }\n\n  function removeRemainingSequences(events) {\n    var index = -1\n    var length = events.length\n\n    while (++index < length) {\n      if (events[index][1].type === 'strikethroughSequenceTemporary') {\n        events[index][1].type = 'data'\n      }\n    }\n\n    return events\n  }\n\n  function tokenizeStrikethrough(effects, ok, nok) {\n    var previous = this.previous\n    var events = this.events\n    var size = 0\n\n    return start\n\n    function start(code) {\n      if (\n        code !== 126 ||\n        (previous === 126 &&\n          events[events.length - 1][1].type !== 'characterEscape')\n      ) {\n        return nok(code)\n      }\n\n      effects.enter('strikethroughSequenceTemporary')\n      return more(code)\n    }\n\n    function more(code) {\n      var before = classifyCharacter(previous)\n      var token\n      var after\n\n      if (code === 126) {\n        // If this is the third marker, exit.\n        if (size > 1) return nok(code)\n        effects.consume(code)\n        size++\n        return more\n      }\n\n      if (size < 2 && !single) return nok(code)\n      token = effects.exit('strikethroughSequenceTemporary')\n      after = classifyCharacter(code)\n      token._open = !after || (after === 2 && before)\n      token._close = !before || (before === 2 && after)\n      return ok(code)\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-strikethrough/index.js\n");

/***/ })

};
;