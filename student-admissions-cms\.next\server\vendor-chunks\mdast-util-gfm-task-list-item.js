/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-task-list-item";
exports.ids = ["vendor-chunks/mdast-util-gfm-task-list-item"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-task-list-item/from-markdown.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-task-list-item/from-markdown.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("exports.exit = {\n  taskListCheckValueChecked: exitCheck,\n  taskListCheckValueUnchecked: exitCheck,\n  paragraph: exitParagraphWithTaskListItem\n}\n\nfunction exitCheck(token) {\n  // We’re always in a paragraph, in a list item.\n  this.stack[this.stack.length - 2].checked =\n    token.type === 'taskListCheckValueChecked'\n}\n\nfunction exitParagraphWithTaskListItem(token) {\n  var parent = this.stack[this.stack.length - 2]\n  var node = this.stack[this.stack.length - 1]\n  var siblings = parent.children\n  var head = node.children[0]\n  var index = -1\n  var firstParaghraph\n\n  if (\n    parent &&\n    parent.type === 'listItem' &&\n    typeof parent.checked === 'boolean' &&\n    head &&\n    head.type === 'text'\n  ) {\n    while (++index < siblings.length) {\n      if (siblings[index].type === 'paragraph') {\n        firstParaghraph = siblings[index]\n        break\n      }\n    }\n\n    if (firstParaghraph === node) {\n      // Must start with a space or a tab.\n      head.value = head.value.slice(1)\n\n      if (head.value.length === 0) {\n        node.children.shift()\n      } else {\n        head.position.start.column++\n        head.position.start.offset++\n        node.position.start = Object.assign({}, head.position.start)\n      }\n    }\n  }\n\n  this.exit(token)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-task-list-item/from-markdown.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm-task-list-item/to-markdown.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-task-list-item/to-markdown.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var defaultListItem = __webpack_require__(/*! mdast-util-to-markdown/lib/handle/list-item */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\")\n\nexports.unsafe = [{atBreak: true, character: '-', after: '[:|-]'}]\n\nexports.handlers = {\n  listItem: listItemWithTaskListItem\n}\n\nfunction listItemWithTaskListItem(node, parent, context) {\n  var value = defaultListItem(node, parent, context)\n  var head = node.children[0]\n\n  if (typeof node.checked === 'boolean' && head && head.type === 'paragraph') {\n    value = value.replace(/^(?:[*+-]|\\d+\\.)([\\r\\n]| {1,3})/, check)\n  }\n\n  return value\n\n  function check($0) {\n    return $0 + '[' + (node.checked ? 'x' : ' ') + '] '\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1nZm0tdGFzay1saXN0LWl0ZW0vdG8tbWFya2Rvd24uanMiLCJtYXBwaW5ncyI6IkFBQUEsc0JBQXNCLG1CQUFPLENBQUMsd0hBQTZDOztBQUUzRSxjQUFjLEtBQUssOENBQThDOztBQUVqRSxnQkFBZ0I7QUFDaEI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxxREFBcUQsSUFBSTtBQUN6RDs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1nZm0tdGFzay1saXN0LWl0ZW0vdG8tbWFya2Rvd24uanM/M2I4YyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgZGVmYXVsdExpc3RJdGVtID0gcmVxdWlyZSgnbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2xpc3QtaXRlbScpXG5cbmV4cG9ydHMudW5zYWZlID0gW3thdEJyZWFrOiB0cnVlLCBjaGFyYWN0ZXI6ICctJywgYWZ0ZXI6ICdbOnwtXSd9XVxuXG5leHBvcnRzLmhhbmRsZXJzID0ge1xuICBsaXN0SXRlbTogbGlzdEl0ZW1XaXRoVGFza0xpc3RJdGVtXG59XG5cbmZ1bmN0aW9uIGxpc3RJdGVtV2l0aFRhc2tMaXN0SXRlbShub2RlLCBwYXJlbnQsIGNvbnRleHQpIHtcbiAgdmFyIHZhbHVlID0gZGVmYXVsdExpc3RJdGVtKG5vZGUsIHBhcmVudCwgY29udGV4dClcbiAgdmFyIGhlYWQgPSBub2RlLmNoaWxkcmVuWzBdXG5cbiAgaWYgKHR5cGVvZiBub2RlLmNoZWNrZWQgPT09ICdib29sZWFuJyAmJiBoZWFkICYmIGhlYWQudHlwZSA9PT0gJ3BhcmFncmFwaCcpIHtcbiAgICB2YWx1ZSA9IHZhbHVlLnJlcGxhY2UoL14oPzpbKistXXxcXGQrXFwuKShbXFxyXFxuXXwgezEsM30pLywgY2hlY2spXG4gIH1cblxuICByZXR1cm4gdmFsdWVcblxuICBmdW5jdGlvbiBjaGVjaygkMCkge1xuICAgIHJldHVybiAkMCArICdbJyArIChub2RlLmNoZWNrZWQgPyAneCcgOiAnICcpICsgJ10gJ1xuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-task-list-item/to-markdown.js\n");

/***/ })

};
;