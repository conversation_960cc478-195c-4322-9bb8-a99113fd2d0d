'use client';

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
} from '@mui/material';
import {
  School as SchoolIcon,
  Add as AddIcon,
  Assignment as OfferIcon,
  CheckCircle as CheckIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';
import { mockData } from '@/components/DummyDataProvider';

const SimpleEnrollmentsPage: React.FC = () => {
  const enrollments = mockData.enrollments;

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      enrolled: 'success',
      deferred: 'warning',
      cancelled: 'error',
      graduated: 'info',
      transferred: 'secondary',
      withdrawn: 'default',
    };
    return colors[status] || 'default';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Enrollment Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage student enrollments and track academic progress.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => alert('Enroll new student functionality would be implemented here')}
        >
          Enroll Student
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SchoolIcon color="primary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="primary">
                    {enrollments.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Enrollments
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <CheckIcon color="success" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="success.main">
                    {enrollments.filter(e => e.status === 'enrolled').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Students
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <MoneyIcon color="info" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="info.main">
                    ${enrollments.reduce((sum, e) => sum + e.fees_paid, 0).toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Fees Collected
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <OfferIcon color="warning" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="warning.main">
                    {mockData.applications.filter(a => a.status === 'offered').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Pending Offers
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Generate Offers
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Send offer letters to qualified applicants
              </Typography>
              <Button
                variant="contained"
                startIcon={<OfferIcon />}
                fullWidth
                onClick={() => alert('Generate offer letters functionality would be implemented here')}
              >
                Send Offer Letters
              </Button>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Process Enrollments
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Enroll students who accepted offers
              </Typography>
              <Button
                variant="contained"
                startIcon={<SchoolIcon />}
                fullWidth
                onClick={() => alert('Process enrollments functionality would be implemented here')}
              >
                Process Enrollments
              </Button>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Generate Reports
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Create enrollment and financial reports
              </Typography>
              <Button
                variant="outlined"
                fullWidth
                onClick={() => alert('Generate reports functionality would be implemented here')}
              >
                Generate Reports
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Enrollments Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Current Enrollments
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Student ID</TableCell>
                  <TableCell>Student Name</TableCell>
                  <TableCell>Program</TableCell>
                  <TableCell>Class</TableCell>
                  <TableCell>Enrollment Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Fees</TableCell>
                  <TableCell>Academic Year</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {enrollments.map((enrollment) => (
                  <TableRow key={enrollment.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold" color="primary">
                        {enrollment.student_id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {enrollment.applications.first_name} {enrollment.applications.last_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {enrollment.classes.academic_programs.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {enrollment.classes.class_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {enrollment.enrollment_date}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={enrollment.status.toUpperCase()}
                        color={getStatusColor(enrollment.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          ${enrollment.fees_paid.toLocaleString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          of ${enrollment.total_fees.toLocaleString()}
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={(enrollment.fees_paid / enrollment.total_fees) * 100}
                          sx={{ mt: 0.5, height: 4 }}
                        />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {enrollment.academic_years.year_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => alert(`View enrollment details for ${enrollment.student_id}`)}
                        >
                          View
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => alert(`Edit enrollment for ${enrollment.student_id}`)}
                        >
                          Edit
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Demo Notice */}
      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
        <Typography variant="body2">
          <strong>Demo Mode:</strong> This page displays dummy data for demonstration purposes. 
          In a real implementation, this would include:
        </Typography>
        <ul>
          <li>Automated offer letter generation and delivery</li>
          <li>Online acceptance and enrollment forms</li>
          <li>Fee payment integration and tracking</li>
          <li>Student ID generation and management</li>
          <li>Class assignment and capacity management</li>
          <li>Academic calendar integration</li>
          <li>Student portal access provisioning</li>
        </ul>
      </Box>
    </Box>
  );
};

export default SimpleEnrollmentsPage;
