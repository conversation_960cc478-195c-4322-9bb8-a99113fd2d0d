/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-markdown";
exports.ids = ["vendor-chunks/react-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-markdown/node_modules/react-is/cjs/react-is.development.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/react-markdown/node_modules/react-is/cjs/react-is.development.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/** @license React v17.0.2\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar REACT_ELEMENT_TYPE = 0xeac7;\nvar REACT_PORTAL_TYPE = 0xeaca;\nvar REACT_FRAGMENT_TYPE = 0xeacb;\nvar REACT_STRICT_MODE_TYPE = 0xeacc;\nvar REACT_PROFILER_TYPE = 0xead2;\nvar REACT_PROVIDER_TYPE = 0xeacd;\nvar REACT_CONTEXT_TYPE = 0xeace;\nvar REACT_FORWARD_REF_TYPE = 0xead0;\nvar REACT_SUSPENSE_TYPE = 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = 0xead8;\nvar REACT_MEMO_TYPE = 0xead3;\nvar REACT_LAZY_TYPE = 0xead4;\nvar REACT_BLOCK_TYPE = 0xead9;\nvar REACT_SERVER_BLOCK_TYPE = 0xeada;\nvar REACT_FUNDAMENTAL_TYPE = 0xead5;\nvar REACT_SCOPE_TYPE = 0xead7;\nvar REACT_OPAQUE_ID_TYPE = 0xeae0;\nvar REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\nvar REACT_OFFSCREEN_TYPE = 0xeae2;\nvar REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n\nif (typeof Symbol === 'function' && Symbol.for) {\n  var symbolFor = Symbol.for;\n  REACT_ELEMENT_TYPE = symbolFor('react.element');\n  REACT_PORTAL_TYPE = symbolFor('react.portal');\n  REACT_FRAGMENT_TYPE = symbolFor('react.fragment');\n  REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n  REACT_PROFILER_TYPE = symbolFor('react.profiler');\n  REACT_PROVIDER_TYPE = symbolFor('react.provider');\n  REACT_CONTEXT_TYPE = symbolFor('react.context');\n  REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n  REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n  REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n  REACT_MEMO_TYPE = symbolFor('react.memo');\n  REACT_LAZY_TYPE = symbolFor('react.lazy');\n  REACT_BLOCK_TYPE = symbolFor('react.block');\n  REACT_SERVER_BLOCK_TYPE = symbolFor('react.server.block');\n  REACT_FUNDAMENTAL_TYPE = symbolFor('react.fundamental');\n  REACT_SCOPE_TYPE = symbolFor('react.scope');\n  REACT_OPAQUE_ID_TYPE = symbolFor('react.opaque.id');\n  REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n  REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n  REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n}\n\n// Filter certain DOM attributes (e.g. src, href) if their values are empty strings.\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_DEBUG_TRACING_MODE_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_LEGACY_HIDDEN_TYPE || enableScopeAPI ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_BLOCK_TYPE || type[0] === REACT_SERVER_BLOCK_TYPE) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n          case REACT_SUSPENSE_LIST_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n}\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false;\nvar hasWarnedAboutDeprecatedIsConcurrentMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 18+.');\n    }\n  }\n\n  return false;\n}\nfunction isConcurrentMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsConcurrentMode) {\n      hasWarnedAboutDeprecatedIsConcurrentMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isConcurrentMode() alias has been deprecated, ' + 'and will be removed in React 18+.');\n    }\n  }\n\n  return false;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/node_modules/react-is/cjs/react-is.development.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-markdown/node_modules/react-is/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-markdown/node_modules/react-is/index.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-is.development.js */ \"(ssr)/./node_modules/react-markdown/node_modules/react-is/cjs/react-is.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFya2Rvd24vbm9kZV9tb2R1bGVzL3JlYWN0LWlzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSxrS0FBeUQ7QUFDM0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL3JlYWN0LW1hcmtkb3duL25vZGVfbW9kdWxlcy9yZWFjdC1pcy9pbmRleC5qcz8yOTM1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1pcy5wcm9kdWN0aW9uLm1pbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1pcy5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/node_modules/react-is/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-markdown/src/ast-to-react.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-markdown/src/ast-to-react.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nconst React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\")\nconst ReactIs = __webpack_require__(/*! react-is */ \"(ssr)/./node_modules/react-markdown/node_modules/react-is/index.js\")\nconst svg = __webpack_require__(/*! property-information/svg */ \"(ssr)/./node_modules/property-information/svg.js\")\nconst find = __webpack_require__(/*! property-information/find */ \"(ssr)/./node_modules/property-information/find.js\")\nconst hastToReact = __webpack_require__(/*! property-information/hast-to-react.json */ \"(ssr)/./node_modules/property-information/hast-to-react.json\")\nconst spaces = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\")\nconst commas = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\")\nconst style = __webpack_require__(/*! style-to-object */ \"(ssr)/./node_modules/style-to-object/index.js\")\n\nexports.hastToReact = toReact\nexports.hastChildrenToReact = childrenToReact\n\n/**\n * @typedef {JSX.IntrinsicElements} IntrinsicElements\n * @typedef {import('react').ReactNode} ReactNode\n * @typedef {import('unist').Position} Position\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').DocType} Doctype\n */\n\n/**\n * @typedef Info\n * @property {string?} space\n * @property {string?} attribute\n * @property {string?} property\n * @property {boolean} boolean\n * @property {boolean} booleanish\n * @property {boolean} overloadedBoolean\n * @property {boolean} number\n * @property {boolean} commaSeparated\n * @property {boolean} spaceSeparated\n * @property {boolean} commaOrSpaceSeparated\n * @property {boolean} mustUseProperty\n * @property {boolean} defined\n *\n * @typedef Schema\n * @property {Object.<string, Info>} property\n * @property {Object.<string, string>} normal\n * @property {string?} space\n *\n * @typedef Raw\n * @property {'raw'} type\n * @property {string} value\n *\n * @typedef Context\n * @property {TransformOptions} options\n * @property {Schema} schema\n * @property {number} listDepth\n *\n * @callback TransformLink\n * @param {string} href\n * @param {Array.<Comment|Element|Text>} children\n * @param {string?} title\n * @returns {string}\n *\n * @callback TransformImage\n * @param {string} src\n * @param {string} alt\n * @param {string?} title\n * @returns {string}\n *\n * @callback TransformLinkTarget\n * @param {string} href\n * @param {Array.<Comment|Element|Text>} children\n * @param {string?} title\n * @returns {string|undefined}\n *\n * @typedef {keyof IntrinsicElements} ReactMarkdownNames\n *\n * To do: is `data-sourcepos` typeable?\n *\n * @typedef ReactMarkdownProps\n * @property {Element} node\n * @property {string} key\n * @property {ReactNode[]} children\n * @property {Position?} [sourcePosition] Passed when `options.rawSourcePos` is given\n * @property {number} [index] Passed when `options.includeElementIndex` is given\n * @property {number} [siblingCount] Passed when `options.includeElementIndex` is given\n *\n * @callback CodeComponent\n * @param {JSX.IntrinsicElements['code'] & ReactMarkdownProps & {inline?: boolean}} props\n * @returns {ReactNode}\n *\n * @callback HeadingComponent\n * @param {JSX.IntrinsicElements['h1'] & ReactMarkdownProps & {level: number}} props\n * @returns {ReactNode}\n *\n * @callback LiComponent\n * @param {JSX.IntrinsicElements['li'] & ReactMarkdownProps & {checked: boolean|null, index: number, ordered: boolean}} props\n * @returns {ReactNode}\n *\n * @callback OrderedListComponent\n * @param {JSX.IntrinsicElements['ol'] & ReactMarkdownProps & {depth: number, ordered: true}} props\n * @returns {ReactNode}\n *\n * @callback TableCellComponent\n * @param {JSX.IntrinsicElements['table'] & ReactMarkdownProps & {style?: Object.<string, unknown>, isHeader: boolean}} props\n * @returns {ReactNode}\n *\n * @callback TableRowComponent\n * @param {JSX.IntrinsicElements['tr'] & ReactMarkdownProps & {isHeader: boolean}} props\n * @returns {ReactNode}\n *\n * @callback UnorderedListComponent\n * @param {JSX.IntrinsicElements['ul'] & ReactMarkdownProps & {depth: number, ordered: false}} props\n * @returns {ReactNode}\n *\n * @typedef SpecialComponents\n * @property {CodeComponent|ReactMarkdownNames} code\n * @property {HeadingComponent|ReactMarkdownNames} h1\n * @property {HeadingComponent|ReactMarkdownNames} h2\n * @property {HeadingComponent|ReactMarkdownNames} h3\n * @property {HeadingComponent|ReactMarkdownNames} h4\n * @property {HeadingComponent|ReactMarkdownNames} h5\n * @property {HeadingComponent|ReactMarkdownNames} h6\n * @property {LiComponent|ReactMarkdownNames} li\n * @property {OrderedListComponent|ReactMarkdownNames} ol\n * @property {TableCellComponent|ReactMarkdownNames} td\n * @property {TableCellComponent|ReactMarkdownNames} th\n * @property {TableRowComponent|ReactMarkdownNames} tr\n * @property {UnorderedListComponent|ReactMarkdownNames} ul\n *\n * @typedef {{[TagName in keyof IntrinsicElements]: TagName | ((props: IntrinsicElements[TagName] & ReactMarkdownProps) => ReactNode)}} NormalComponents\n * @typedef {Partial<Omit<NormalComponents, keyof SpecialComponents> & SpecialComponents>} Components\n */\n\n/**\n * @typedef TransformOptions\n * @property {boolean} [sourcePos=false]\n * @property {boolean} [rawSourcePos=false]\n * @property {boolean} [skipHtml=false]\n * @property {boolean} [includeElementIndex=false]\n * @property {null|false|TransformLink} [transformLinkUri]\n * @property {TransformImage} [transformImageUri]\n * @property {string|TransformLinkTarget} [linkTarget]\n * @property {Components} [components]\n */\n\nconst own = {}.hasOwnProperty\n\n// The table-related elements that must not contain whitespace text according\n// to React.\nconst tableElements = new Set(['table', 'thead', 'tbody', 'tfoot', 'tr'])\n\n/**\n * @param {Context} context\n * @param {Element|Root} node\n */\nfunction childrenToReact(context, node) {\n  /** @type {Array.<ReactNode>} */\n  const children = []\n  let childIndex = -1\n  /** @type {Comment|Doctype|Element|Raw|Text} */\n  let child\n\n  while (++childIndex < node.children.length) {\n    child = node.children[childIndex]\n\n    if (child.type === 'element') {\n      children.push(toReact(context, child, childIndex, node))\n    } else if (child.type === 'text') {\n      // React does not permit whitespace text elements as children of table:\n      // cf. https://github.com/remarkjs/react-markdown/issues/576\n      if (\n        node.type !== 'element' ||\n        !tableElements.has(node.tagName) ||\n        child.value !== '\\n'\n      ) {\n        children.push(child.value)\n      }\n    }\n    // @ts-expect-error `raw` nodes are non-standard\n    else if (child.type === 'raw' && !context.options.skipHtml) {\n      // Default behavior is to show (encoded) HTML.\n      // @ts-expect-error `raw` nodes are non-standard\n      children.push(child.value)\n    }\n  }\n\n  return children\n}\n\n/**\n * @param {Context} context\n * @param {Element} node\n * @param {number} index\n * @param {Element|Root} parent\n */\nfunction toReact(context, node, index, parent) {\n  const options = context.options\n  const parentSchema = context.schema\n  /** @type {ReactMarkdownNames} */\n  // @ts-expect-error assume a known HTML/SVG element.\n  const name = node.tagName\n  /** @type {Object.<string, unknown>} */\n  const properties = {}\n  let schema = parentSchema\n  /** @type {string} */\n  let property\n\n  if (parentSchema.space === 'html' && name === 'svg') {\n    schema = svg\n    context.schema = schema\n  }\n\n  /* istanbul ignore else - types say they’re optional. */\n  if (node.properties) {\n    for (property in node.properties) {\n      /* istanbul ignore else - prototype polution. */\n      if (own.call(node.properties, property)) {\n        addProperty(properties, property, node.properties[property], context)\n      }\n    }\n  }\n\n  if (name === 'ol' || name === 'ul') {\n    context.listDepth++\n  }\n\n  const children = childrenToReact(context, node)\n\n  if (name === 'ol' || name === 'ul') {\n    context.listDepth--\n  }\n\n  // Restore parent schema.\n  context.schema = parentSchema\n\n  // Nodes created by plugins do not have positional info, in which case we use\n  // an object that matches the positon interface.\n  const position = node.position || {\n    start: {line: null, column: null, offset: null},\n    end: {line: null, column: null, offset: null}\n  }\n  const component =\n    options.components && own.call(options.components, name)\n      ? options.components[name]\n      : name\n  const basic = typeof component === 'string' || component === React.Fragment\n\n  if (!ReactIs.isValidElementType(component)) {\n    throw new TypeError(\n      `Component for name \\`${name}\\` not defined or is not renderable`\n    )\n  }\n\n  properties.key = [\n    name,\n    position.start.line,\n    position.start.column,\n    index\n  ].join('-')\n\n  if (name === 'a' && options.linkTarget) {\n    properties.target =\n      typeof options.linkTarget === 'function'\n        ? // @ts-expect-error assume `href` is a string\n          options.linkTarget(properties.href, node.children, properties.title)\n        : options.linkTarget\n  }\n\n  if (name === 'a' && options.transformLinkUri) {\n    properties.href = options.transformLinkUri(\n      // @ts-expect-error assume `href` is a string\n      properties.href,\n      node.children,\n      properties.title\n    )\n  }\n\n  if (\n    !basic &&\n    name === 'code' &&\n    parent.type === 'element' &&\n    parent.tagName !== 'pre'\n  ) {\n    properties.inline = true\n  }\n\n  if (\n    !basic &&\n    (name === 'h1' ||\n      name === 'h2' ||\n      name === 'h3' ||\n      name === 'h4' ||\n      name === 'h5' ||\n      name === 'h6')\n  ) {\n    properties.level = parseInt(name.charAt(1), 10)\n  }\n\n  if (name === 'img' && options.transformImageUri) {\n    properties.src = options.transformImageUri(\n      // @ts-expect-error assume `src` is a string\n      properties.src,\n      properties.alt,\n      properties.title\n    )\n  }\n\n  if (!basic && name === 'li' && parent.type === 'element') {\n    const input = getInputElement(node)\n    properties.checked =\n      input && input.properties ? Boolean(input.properties.checked) : null\n    properties.index = getElementsBeforeCount(parent, node)\n    properties.ordered = parent.tagName === 'ol'\n  }\n\n  if (!basic && (name === 'ol' || name === 'ul')) {\n    properties.ordered = name === 'ol'\n    properties.depth = context.listDepth\n  }\n\n  if (name === 'td' || name === 'th') {\n    if (properties.align) {\n      if (!properties.style) properties.style = {}\n      // @ts-expect-error assume `style` is an object\n      properties.style.textAlign = properties.align\n      delete properties.align\n    }\n\n    if (!basic) {\n      properties.isHeader = name === 'th'\n    }\n  }\n\n  if (!basic && name === 'tr' && parent.type === 'element') {\n    properties.isHeader = Boolean(parent.tagName === 'thead')\n  }\n\n  // If `sourcePos` is given, pass source information (line/column info from markdown source).\n  if (options.sourcePos) {\n    properties['data-sourcepos'] = flattenPosition(position)\n  }\n\n  if (!basic && options.rawSourcePos) {\n    properties.sourcePosition = node.position\n  }\n\n  // If `includeElementIndex` is given, pass node index info to components.\n  if (!basic && options.includeElementIndex) {\n    properties.index = getElementsBeforeCount(parent, node)\n    properties.siblingCount = getElementsBeforeCount(parent)\n  }\n\n  if (!basic) {\n    properties.node = node\n  }\n\n  // Ensure no React warnings are emitted for void elements w/ children.\n  return children.length > 0\n    ? React.createElement(component, properties, children)\n    : React.createElement(component, properties)\n}\n\n/**\n * @param {Element|Root} node\n * @returns {Element?}\n */\nfunction getInputElement(node) {\n  let index = -1\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if (child.type === 'element' && child.tagName === 'input') {\n      return child\n    }\n  }\n\n  return null\n}\n\n/**\n * @param {Element|Root} parent\n * @param {Element} [node]\n * @returns {number}\n */\nfunction getElementsBeforeCount(parent, node) {\n  let index = -1\n  let count = 0\n\n  while (++index < parent.children.length) {\n    if (parent.children[index] === node) break\n    if (parent.children[index].type === 'element') count++\n  }\n\n  return count\n}\n\n/**\n * @param {Object.<string, unknown>} props\n * @param {string} prop\n * @param {unknown} value\n * @param {Context} ctx\n */\nfunction addProperty(props, prop, value, ctx) {\n  /** @type {Info} */\n  const info = find(ctx.schema, prop)\n  let result = value\n\n  // Ignore nullish and `NaN` values.\n  // eslint-disable-next-line no-self-compare\n  if (result === null || result === undefined || result !== result) {\n    return\n  }\n\n  // Accept `array`.\n  // Most props are space-separated.\n  if (result && typeof result === 'object' && 'length' in result) {\n    // type-coverage:ignore-next-line remove when typed.\n    result = (info.commaSeparated ? commas : spaces).stringify(result)\n  }\n\n  if (info.property === 'style' && typeof result === 'string') {\n    result = parseStyle(result)\n  }\n\n  /* istanbul ignore else - types say they’re optional. */\n  if (info.space && info.property) {\n    props[\n      own.call(hastToReact, info.property)\n        ? hastToReact[info.property]\n        : info.property\n    ] = result\n  } else if (info.attribute) {\n    props[info.attribute] = result\n  }\n}\n\n/**\n * @param {string} value\n * @returns {Object.<string, string>}\n */\nfunction parseStyle(value) {\n  /** @type {Object.<string, string>} */\n  const result = {}\n\n  try {\n    style(value, iterator)\n  } catch (/** @type {unknown} */ _) {\n    // Silent.\n  }\n\n  return result\n\n  /**\n   * @param {string} name\n   * @param {string} v\n   */\n  function iterator(name, v) {\n    const k = name.slice(0, 4) === '-ms-' ? `ms-${name.slice(4)}` : name\n    result[k.replace(/-([a-z])/g, styleReplacer)] = v\n  }\n}\n\n/**\n * @param {unknown} _\n * @param {string} $1\n */\nfunction styleReplacer(_, $1) {\n  return $1.toUpperCase()\n}\n\n/**\n * @param {Position|{start: {line: null, column: null, offset: null}, end: {line: null, column: null, offset: null}}} pos\n * @returns {string}\n */\nfunction flattenPosition(pos) {\n  return [\n    pos.start.line,\n    ':',\n    pos.start.column,\n    '-',\n    pos.end.line,\n    ':',\n    pos.end.column\n  ]\n    .map((d) => String(d))\n    .join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/src/ast-to-react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-markdown/src/react-markdown.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-markdown/src/react-markdown.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\")\nconst vfile = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/index.js\")\nconst unified = __webpack_require__(/*! unified */ \"(ssr)/./node_modules/unified/index.js\")\nconst parse = __webpack_require__(/*! remark-parse */ \"(ssr)/./node_modules/remark-parse/index.js\")\nconst remarkRehype = __webpack_require__(/*! remark-rehype */ \"(ssr)/./node_modules/remark-rehype/index.js\")\nconst PropTypes = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\")\nconst html = __webpack_require__(/*! property-information/html */ \"(ssr)/./node_modules/property-information/html.js\")\nconst filter = __webpack_require__(/*! ./rehype-filter.js */ \"(ssr)/./node_modules/react-markdown/src/rehype-filter.js\")\nconst uriTransformer = __webpack_require__(/*! ./uri-transformer.js */ \"(ssr)/./node_modules/react-markdown/src/uri-transformer.js\")\nconst childrenToReact = (__webpack_require__(/*! ./ast-to-react.js */ \"(ssr)/./node_modules/react-markdown/src/ast-to-react.js\").hastChildrenToReact)\n\n/**\n * @typedef {import('react').ReactNode} ReactNode\n * @typedef {import('react').ReactElement<{}>} ReactElement\n * @typedef {import('unified').PluggableList} PluggableList\n * @typedef {import('hast').Root} Root\n * @typedef {import('./rehype-filter.js').RehypeFilterOptions} FilterOptions\n * @typedef {import('./ast-to-react.js').TransformOptions} TransformOptions\n *\n * @typedef CoreOptions\n * @property {string} children\n *\n * @typedef PluginOptions\n * @property {PluggableList} [plugins=[]] **deprecated**: use `remarkPlugins` instead\n * @property {PluggableList} [remarkPlugins=[]]\n * @property {PluggableList} [rehypePlugins=[]]\n *\n * @typedef LayoutOptions\n * @property {string} [className]\n *\n * @typedef {CoreOptions & PluginOptions & LayoutOptions & FilterOptions & TransformOptions} ReactMarkdownOptions\n */\n\nmodule.exports = ReactMarkdown\n\nconst own = {}.hasOwnProperty\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/**\n * @typedef Deprecation\n * @property {string} id\n * @property {string} [to]\n */\n\n/**\n * @type {Object.<string, Deprecation>}\n */\nconst deprecated = {\n  renderers: {to: 'components', id: 'change-renderers-to-components'},\n  astPlugins: {id: 'remove-buggy-html-in-markdown-parser'},\n  allowDangerousHtml: {id: 'remove-buggy-html-in-markdown-parser'},\n  escapeHtml: {id: 'remove-buggy-html-in-markdown-parser'},\n  source: {to: 'children', id: 'change-source-to-children'},\n  allowNode: {\n    to: 'allowElement',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  allowedTypes: {\n    to: 'allowedElements',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  disallowedTypes: {\n    to: 'disallowedElements',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  includeNodeIndex: {\n    to: 'includeElementIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  }\n}\n\n/**\n * @param {ReactMarkdownOptions} options\n * @returns {ReactElement}\n */\nfunction ReactMarkdown(options) {\n  for (const key in deprecated) {\n    if (own.call(deprecated, key) && own.call(options, key)) {\n      /** @type {Deprecation} */\n      const deprecation = deprecated[key]\n      console.warn(\n        `[react-markdown] Warning: please ${\n          deprecation.to ? `use \\`${deprecation.to}\\` instead of` : 'remove'\n        } \\`${key}\\` (see <${changelog}#${deprecation.id}> for more info)`\n      )\n      delete deprecated[key]\n    }\n  }\n\n  const processor = unified()\n    .use(parse)\n    // TODO: deprecate `plugins` in v7.0.0.\n    .use(options.remarkPlugins || options.plugins || [])\n    .use(remarkRehype, {allowDangerousHtml: true})\n    .use(options.rehypePlugins || [])\n    .use(filter, options)\n\n  /** @type {vfile} */\n  let file\n\n  if (typeof options.children === 'string') {\n    file = vfile(options.children)\n  } else {\n    if (options.children !== undefined && options.children !== null) {\n      console.warn(\n        `[react-markdown] Warning: please pass a string as \\`children\\` (not: \\`${options.children}\\`)`\n      )\n    }\n\n    file = vfile()\n  }\n\n  /** @type {Root} */\n  // @ts-expect-error we’ll throw if it isn’t a root next.\n  const hastNode = processor.runSync(processor.parse(file), file)\n\n  if (hastNode.type !== 'root') {\n    throw new TypeError('Expected a `root` node')\n  }\n\n  /** @type {ReactElement} */\n  let result = React.createElement(\n    React.Fragment,\n    {},\n    childrenToReact({options: options, schema: html, listDepth: 0}, hastNode)\n  )\n\n  if (options.className) {\n    result = React.createElement('div', {className: options.className}, result)\n  }\n\n  return result\n}\n\nReactMarkdown.defaultProps = {transformLinkUri: uriTransformer}\n\nReactMarkdown.propTypes = {\n  // Core options:\n  children: PropTypes.string,\n  // Layout options:\n  className: PropTypes.string,\n  // Filter options:\n  allowElement: PropTypes.func,\n  allowedElements: PropTypes.arrayOf(PropTypes.string),\n  disallowedElements: PropTypes.arrayOf(PropTypes.string),\n  unwrapDisallowed: PropTypes.bool,\n  // Plugin options:\n  // type-coverage:ignore-next-line\n  remarkPlugins: PropTypes.arrayOf(\n    PropTypes.oneOfType([\n      PropTypes.object,\n      PropTypes.func,\n      PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.object, PropTypes.func]))\n    ])\n  ),\n  // type-coverage:ignore-next-line\n  rehypePlugins: PropTypes.arrayOf(\n    PropTypes.oneOfType([\n      PropTypes.object,\n      PropTypes.func,\n      PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.object, PropTypes.func]))\n    ])\n  ),\n  // Transform options:\n  sourcePos: PropTypes.bool,\n  rawSourcePos: PropTypes.bool,\n  skipHtml: PropTypes.bool,\n  includeElementIndex: PropTypes.bool,\n  transformLinkUri: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  linkTarget: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n  transformImageUri: PropTypes.func,\n  components: PropTypes.object\n}\n\nReactMarkdown.uriTransformer = uriTransformer\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/src/react-markdown.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-markdown/src/rehype-filter.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-markdown/src/rehype-filter.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const visit = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/index.js\")\n\nmodule.exports = rehypeFilter\n\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Element} Element\n *\n * @callback AllowElement\n * @param {Element} element\n * @param {number} index\n * @param {Element|Root} parent\n * @returns {boolean|undefined}\n *\n * @typedef RehypeFilterOptions\n * @property {Array.<string>} [allowedElements]\n * @property {Array.<string>} [disallowedElements=[]]\n * @property {AllowElement} [allowElement]\n * @property {boolean} [unwrapDisallowed=false]\n */\n\n/**\n * @type {import('unified').Plugin<[RehypeFilterOptions]>}\n */\nfunction rehypeFilter(options) {\n  if (options.allowedElements && options.disallowedElements) {\n    throw new TypeError(\n      'Only one of `allowedElements` and `disallowedElements` should be defined'\n    )\n  }\n\n  if (\n    options.allowedElements ||\n    options.disallowedElements ||\n    options.allowElement\n  ) {\n    return (tree) => {\n      const node = /** @type {Root} */ (tree)\n      visit(node, 'element', onelement)\n    }\n  }\n\n  /**\n   * @param {Node} node_\n   * @param {number|null|undefined} index\n   * @param {Node|null|undefined} parent_\n   * @returns {number|void}\n   */\n  function onelement(node_, index, parent_) {\n    const node = /** @type {Element} */ (node_)\n    const parent = /** @type {Element|Root} */ (parent_)\n    /** @type {boolean|undefined} */\n    let remove\n\n    if (options.allowedElements) {\n      remove = !options.allowedElements.includes(node.tagName)\n    } else if (options.disallowedElements) {\n      remove = options.disallowedElements.includes(node.tagName)\n    }\n\n    if (!remove && options.allowElement && typeof index === 'number') {\n      remove = !options.allowElement(node, index, parent)\n    }\n\n    if (remove && typeof index === 'number') {\n      if (options.unwrapDisallowed && node.children) {\n        parent.children.splice(index, 1, ...node.children)\n      } else {\n        parent.children.splice(index, 1)\n      }\n\n      return index\n    }\n\n    return undefined\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/src/rehype-filter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-markdown/src/uri-transformer.js":
/*!************************************************************!*\
  !*** ./node_modules/react-markdown/src/uri-transformer.js ***!
  \************************************************************/
/***/ ((module) => {

eval("const protocols = ['http', 'https', 'mailto', 'tel']\n\nmodule.exports = uriTransformer\n\n/**\n * @param {string} uri\n * @returns {string}\n */\nfunction uriTransformer(uri) {\n  const url = (uri || '').trim()\n  const first = url.charAt(0)\n\n  if (first === '#' || first === '/') {\n    return url\n  }\n\n  const colon = url.indexOf(':')\n  if (colon === -1) {\n    return url\n  }\n\n  let index = -1\n\n  while (++index < protocols.length) {\n    const protocol = protocols[index]\n\n    if (\n      colon === protocol.length &&\n      url.slice(0, protocol.length).toLowerCase() === protocol\n    ) {\n      return url\n    }\n  }\n\n  index = url.indexOf('?')\n  if (index !== -1 && colon > index) {\n    return url\n  }\n\n  index = url.indexOf('#')\n  if (index !== -1 && colon > index) {\n    return url\n  }\n\n  // eslint-disable-next-line no-script-url\n  return 'javascript:void(0)'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFya2Rvd24vc3JjL3VyaS10cmFuc2Zvcm1lci5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7QUFFQTs7QUFFQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9yZWFjdC1tYXJrZG93bi9zcmMvdXJpLXRyYW5zZm9ybWVyLmpzPzQyYTQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgcHJvdG9jb2xzID0gWydodHRwJywgJ2h0dHBzJywgJ21haWx0bycsICd0ZWwnXVxuXG5tb2R1bGUuZXhwb3J0cyA9IHVyaVRyYW5zZm9ybWVyXG5cbi8qKlxuICogQHBhcmFtIHtzdHJpbmd9IHVyaVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZnVuY3Rpb24gdXJpVHJhbnNmb3JtZXIodXJpKSB7XG4gIGNvbnN0IHVybCA9ICh1cmkgfHwgJycpLnRyaW0oKVxuICBjb25zdCBmaXJzdCA9IHVybC5jaGFyQXQoMClcblxuICBpZiAoZmlyc3QgPT09ICcjJyB8fCBmaXJzdCA9PT0gJy8nKSB7XG4gICAgcmV0dXJuIHVybFxuICB9XG5cbiAgY29uc3QgY29sb24gPSB1cmwuaW5kZXhPZignOicpXG4gIGlmIChjb2xvbiA9PT0gLTEpIHtcbiAgICByZXR1cm4gdXJsXG4gIH1cblxuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgcHJvdG9jb2xzLmxlbmd0aCkge1xuICAgIGNvbnN0IHByb3RvY29sID0gcHJvdG9jb2xzW2luZGV4XVxuXG4gICAgaWYgKFxuICAgICAgY29sb24gPT09IHByb3RvY29sLmxlbmd0aCAmJlxuICAgICAgdXJsLnNsaWNlKDAsIHByb3RvY29sLmxlbmd0aCkudG9Mb3dlckNhc2UoKSA9PT0gcHJvdG9jb2xcbiAgICApIHtcbiAgICAgIHJldHVybiB1cmxcbiAgICB9XG4gIH1cblxuICBpbmRleCA9IHVybC5pbmRleE9mKCc/JylcbiAgaWYgKGluZGV4ICE9PSAtMSAmJiBjb2xvbiA+IGluZGV4KSB7XG4gICAgcmV0dXJuIHVybFxuICB9XG5cbiAgaW5kZXggPSB1cmwuaW5kZXhPZignIycpXG4gIGlmIChpbmRleCAhPT0gLTEgJiYgY29sb24gPiBpbmRleCkge1xuICAgIHJldHVybiB1cmxcbiAgfVxuXG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1zY3JpcHQtdXJsXG4gIHJldHVybiAnamF2YXNjcmlwdDp2b2lkKDApJ1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/src/uri-transformer.js\n");

/***/ })

};
;