"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-hast";
exports.ids = ["vendor-chunks/mdast-util-to-hast"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-hast/index.js":
/*!**************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/index.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nmodule.exports = __webpack_require__(/*! ./lib */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/index.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFZO0FBQ1oseUdBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvaW5kZXguanM/MThmOCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcbm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9saWInKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/all.js":
/*!****************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/all.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = all\n\nvar one = __webpack_require__(/*! ./one */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/one.js\")\n\nfunction all(h, parent) {\n  var nodes = parent.children || []\n  var length = nodes.length\n  var values = []\n  var index = -1\n  var result\n  var head\n\n  while (++index < length) {\n    result = one(h, nodes[index], parent)\n\n    if (result) {\n      if (index && nodes[index - 1].type === 'break') {\n        if (result.value) {\n          result.value = result.value.replace(/^\\s+/, '')\n        }\n\n        head = result.children && result.children[0]\n\n        if (head && head.value) {\n          head.value = head.value.replace(/^\\s+/, '')\n        }\n      }\n\n      values = values.concat(result)\n    }\n  }\n\n  return values\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9hbGwuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUEsVUFBVSxtQkFBTyxDQUFDLGlFQUFPOztBQUV6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvYWxsLmpzP2Y4MGUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gYWxsXG5cbnZhciBvbmUgPSByZXF1aXJlKCcuL29uZScpXG5cbmZ1bmN0aW9uIGFsbChoLCBwYXJlbnQpIHtcbiAgdmFyIG5vZGVzID0gcGFyZW50LmNoaWxkcmVuIHx8IFtdXG4gIHZhciBsZW5ndGggPSBub2Rlcy5sZW5ndGhcbiAgdmFyIHZhbHVlcyA9IFtdXG4gIHZhciBpbmRleCA9IC0xXG4gIHZhciByZXN1bHRcbiAgdmFyIGhlYWRcblxuICB3aGlsZSAoKytpbmRleCA8IGxlbmd0aCkge1xuICAgIHJlc3VsdCA9IG9uZShoLCBub2Rlc1tpbmRleF0sIHBhcmVudClcblxuICAgIGlmIChyZXN1bHQpIHtcbiAgICAgIGlmIChpbmRleCAmJiBub2Rlc1tpbmRleCAtIDFdLnR5cGUgPT09ICdicmVhaycpIHtcbiAgICAgICAgaWYgKHJlc3VsdC52YWx1ZSkge1xuICAgICAgICAgIHJlc3VsdC52YWx1ZSA9IHJlc3VsdC52YWx1ZS5yZXBsYWNlKC9eXFxzKy8sICcnKVxuICAgICAgICB9XG5cbiAgICAgICAgaGVhZCA9IHJlc3VsdC5jaGlsZHJlbiAmJiByZXN1bHQuY2hpbGRyZW5bMF1cblxuICAgICAgICBpZiAoaGVhZCAmJiBoZWFkLnZhbHVlKSB7XG4gICAgICAgICAgaGVhZC52YWx1ZSA9IGhlYWQudmFsdWUucmVwbGFjZSgvXlxccysvLCAnJylcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICB2YWx1ZXMgPSB2YWx1ZXMuY29uY2F0KHJlc3VsdClcbiAgICB9XG4gIH1cblxuICByZXR1cm4gdmFsdWVzXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js":
/*!*******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/footer.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = generateFootnotes\n\nvar thematicBreak = __webpack_require__(/*! ./handlers/thematic-break */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\")\nvar list = __webpack_require__(/*! ./handlers/list */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js\")\nvar wrap = __webpack_require__(/*! ./wrap */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/wrap.js\")\n\nfunction generateFootnotes(h) {\n  var footnoteById = h.footnoteById\n  var footnoteOrder = h.footnoteOrder\n  var length = footnoteOrder.length\n  var index = -1\n  var listItems = []\n  var def\n  var backReference\n  var content\n  var tail\n\n  while (++index < length) {\n    def = footnoteById[footnoteOrder[index].toUpperCase()]\n\n    if (!def) {\n      continue\n    }\n\n    content = def.children.concat()\n    tail = content[content.length - 1]\n    backReference = {\n      type: 'link',\n      url: '#fnref-' + def.identifier,\n      data: {hProperties: {className: ['footnote-backref']}},\n      children: [{type: 'text', value: '↩'}]\n    }\n\n    if (!tail || tail.type !== 'paragraph') {\n      tail = {type: 'paragraph', children: []}\n      content.push(tail)\n    }\n\n    tail.children.push(backReference)\n\n    listItems.push({\n      type: 'listItem',\n      data: {hProperties: {id: 'fn-' + def.identifier}},\n      children: content,\n      position: def.position\n    })\n  }\n\n  if (listItems.length === 0) {\n    return null\n  }\n\n  return h(\n    null,\n    'div',\n    {className: ['footnotes']},\n    wrap(\n      [\n        thematicBreak(h),\n        list(h, {type: 'list', ordered: true, children: listItems})\n      ],\n      true\n    )\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = blockquote\n\nvar wrap = __webpack_require__(/*! ../wrap */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/wrap.js\")\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nfunction blockquote(h, node) {\n  return h(node, 'blockquote', wrap(all(h, node), true))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBLFdBQVcsbUJBQU8sQ0FBQyxvRUFBUztBQUM1QixVQUFVLG1CQUFPLENBQUMsa0VBQVE7O0FBRTFCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzP2U2YzAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gYmxvY2txdW90ZVxuXG52YXIgd3JhcCA9IHJlcXVpcmUoJy4uL3dyYXAnKVxudmFyIGFsbCA9IHJlcXVpcmUoJy4uL2FsbCcpXG5cbmZ1bmN0aW9uIGJsb2NrcXVvdGUoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnYmxvY2txdW90ZScsIHdyYXAoYWxsKGgsIG5vZGUpLCB0cnVlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/break.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = hardBreak\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\n\nfunction hardBreak(h, node) {\n  return [h(node, 'br'), u('text', '\\n')]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9icmVhay5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQSxRQUFRLG1CQUFPLENBQUMsa0VBQWU7O0FBRS9CO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9icmVhay5qcz9kMDA3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IGhhcmRCcmVha1xuXG52YXIgdSA9IHJlcXVpcmUoJ3VuaXN0LWJ1aWxkZXInKVxuXG5mdW5jdGlvbiBoYXJkQnJlYWsoaCwgbm9kZSkge1xuICByZXR1cm4gW2gobm9kZSwgJ2JyJyksIHUoJ3RleHQnLCAnXFxuJyldXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/code.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = code\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\n\nfunction code(h, node) {\n  var value = node.value ? node.value + '\\n' : ''\n  // To do: next major, use `node.lang` w/o regex, the splitting’s been going\n  // on for years in remark now.\n  var lang = node.lang && node.lang.match(/^[^ \\t]+(?=[ \\t]|$)/)\n  var props = {}\n  var code\n\n  if (lang) {\n    props.className = ['language-' + lang]\n  }\n\n  code = h(node, 'code', props, [u('text', value)])\n\n  if (node.meta) {\n    code.data = {meta: node.meta}\n  }\n\n  return h(node.position, 'pre', [code])\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9jb2RlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBLFFBQVEsbUJBQU8sQ0FBQyxrRUFBZTs7QUFFL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsaUJBQWlCO0FBQ2pCOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvY29kZS5qcz9lNzUyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IGNvZGVcblxudmFyIHUgPSByZXF1aXJlKCd1bmlzdC1idWlsZGVyJylcblxuZnVuY3Rpb24gY29kZShoLCBub2RlKSB7XG4gIHZhciB2YWx1ZSA9IG5vZGUudmFsdWUgPyBub2RlLnZhbHVlICsgJ1xcbicgOiAnJ1xuICAvLyBUbyBkbzogbmV4dCBtYWpvciwgdXNlIGBub2RlLmxhbmdgIHcvbyByZWdleCwgdGhlIHNwbGl0dGluZ+KAmXMgYmVlbiBnb2luZ1xuICAvLyBvbiBmb3IgeWVhcnMgaW4gcmVtYXJrIG5vdy5cbiAgdmFyIGxhbmcgPSBub2RlLmxhbmcgJiYgbm9kZS5sYW5nLm1hdGNoKC9eW14gXFx0XSsoPz1bIFxcdF18JCkvKVxuICB2YXIgcHJvcHMgPSB7fVxuICB2YXIgY29kZVxuXG4gIGlmIChsYW5nKSB7XG4gICAgcHJvcHMuY2xhc3NOYW1lID0gWydsYW5ndWFnZS0nICsgbGFuZ11cbiAgfVxuXG4gIGNvZGUgPSBoKG5vZGUsICdjb2RlJywgcHJvcHMsIFt1KCd0ZXh0JywgdmFsdWUpXSlcblxuICBpZiAobm9kZS5tZXRhKSB7XG4gICAgY29kZS5kYXRhID0ge21ldGE6IG5vZGUubWV0YX1cbiAgfVxuXG4gIHJldHVybiBoKG5vZGUucG9zaXRpb24sICdwcmUnLCBbY29kZV0pXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/delete.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = strikethrough\n\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nfunction strikethrough(h, node) {\n  return h(node, 'del', all(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9kZWxldGUuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUEsVUFBVSxtQkFBTyxDQUFDLGtFQUFROztBQUUxQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvZGVsZXRlLmpzP2UzYWYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gc3RyaWtldGhyb3VnaFxuXG52YXIgYWxsID0gcmVxdWlyZSgnLi4vYWxsJylcblxuZnVuY3Rpb24gc3RyaWtldGhyb3VnaChoLCBub2RlKSB7XG4gIHJldHVybiBoKG5vZGUsICdkZWwnLCBhbGwoaCwgbm9kZSkpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = emphasis\n\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nfunction emphasis(h, node) {\n  return h(node, 'em', all(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQSxVQUFVLG1CQUFPLENBQUMsa0VBQVE7O0FBRTFCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9lbXBoYXNpcy5qcz80YTQxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IGVtcGhhc2lzXG5cbnZhciBhbGwgPSByZXF1aXJlKCcuLi9hbGwnKVxuXG5mdW5jdGlvbiBlbXBoYXNpcyhoLCBub2RlKSB7XG4gIHJldHVybiBoKG5vZGUsICdlbScsIGFsbChoLCBub2RlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = footnoteReference\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\n\nfunction footnoteReference(h, node) {\n  var footnoteOrder = h.footnoteOrder\n  var identifier = String(node.identifier)\n\n  if (footnoteOrder.indexOf(identifier) === -1) {\n    footnoteOrder.push(identifier)\n  }\n\n  return h(node.position, 'sup', {id: 'fnref-' + identifier}, [\n    h(node, 'a', {href: '#fn-' + identifier, className: ['footnote-ref']}, [\n      u('text', node.label || identifier)\n    ])\n  ])\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9mb290bm90ZS1yZWZlcmVuY2UuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUEsUUFBUSxtQkFBTyxDQUFDLGtFQUFlOztBQUUvQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGtDQUFrQywwQkFBMEI7QUFDNUQsa0JBQWtCLHVEQUF1RDtBQUN6RTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9mb290bm90ZS1yZWZlcmVuY2UuanM/YmQ4OSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBmb290bm90ZVJlZmVyZW5jZVxuXG52YXIgdSA9IHJlcXVpcmUoJ3VuaXN0LWJ1aWxkZXInKVxuXG5mdW5jdGlvbiBmb290bm90ZVJlZmVyZW5jZShoLCBub2RlKSB7XG4gIHZhciBmb290bm90ZU9yZGVyID0gaC5mb290bm90ZU9yZGVyXG4gIHZhciBpZGVudGlmaWVyID0gU3RyaW5nKG5vZGUuaWRlbnRpZmllcilcblxuICBpZiAoZm9vdG5vdGVPcmRlci5pbmRleE9mKGlkZW50aWZpZXIpID09PSAtMSkge1xuICAgIGZvb3Rub3RlT3JkZXIucHVzaChpZGVudGlmaWVyKVxuICB9XG5cbiAgcmV0dXJuIGgobm9kZS5wb3NpdGlvbiwgJ3N1cCcsIHtpZDogJ2ZucmVmLScgKyBpZGVudGlmaWVyfSwgW1xuICAgIGgobm9kZSwgJ2EnLCB7aHJlZjogJyNmbi0nICsgaWRlbnRpZmllciwgY2xhc3NOYW1lOiBbJ2Zvb3Rub3RlLXJlZiddfSwgW1xuICAgICAgdSgndGV4dCcsIG5vZGUubGFiZWwgfHwgaWRlbnRpZmllcilcbiAgICBdKVxuICBdKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/footnote.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = footnote\n\nvar footnoteReference = __webpack_require__(/*! ./footnote-reference */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\")\n\nfunction footnote(h, node) {\n  var footnoteById = h.footnoteById\n  var footnoteOrder = h.footnoteOrder\n  var identifier = 1\n\n  while (identifier in footnoteById) {\n    identifier++\n  }\n\n  identifier = String(identifier)\n\n  // No need to check if `identifier` exists in `footnoteOrder`, it’s guaranteed\n  // to not exist because we just generated it.\n  footnoteOrder.push(identifier)\n\n  footnoteById[identifier] = {\n    type: 'footnoteDefinition',\n    identifier: identifier,\n    children: [{type: 'paragraph', children: node.children}],\n    position: node.position\n  }\n\n  return footnoteReference(h, {\n    type: 'footnoteReference',\n    identifier: identifier,\n    position: node.position\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9mb290bm90ZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQSx3QkFBd0IsbUJBQU8sQ0FBQyx3R0FBc0I7O0FBRXREO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDJDQUEyQztBQUMzRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2Zvb3Rub3RlLmpzPzQ1ZTIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gZm9vdG5vdGVcblxudmFyIGZvb3Rub3RlUmVmZXJlbmNlID0gcmVxdWlyZSgnLi9mb290bm90ZS1yZWZlcmVuY2UnKVxuXG5mdW5jdGlvbiBmb290bm90ZShoLCBub2RlKSB7XG4gIHZhciBmb290bm90ZUJ5SWQgPSBoLmZvb3Rub3RlQnlJZFxuICB2YXIgZm9vdG5vdGVPcmRlciA9IGguZm9vdG5vdGVPcmRlclxuICB2YXIgaWRlbnRpZmllciA9IDFcblxuICB3aGlsZSAoaWRlbnRpZmllciBpbiBmb290bm90ZUJ5SWQpIHtcbiAgICBpZGVudGlmaWVyKytcbiAgfVxuXG4gIGlkZW50aWZpZXIgPSBTdHJpbmcoaWRlbnRpZmllcilcblxuICAvLyBObyBuZWVkIHRvIGNoZWNrIGlmIGBpZGVudGlmaWVyYCBleGlzdHMgaW4gYGZvb3Rub3RlT3JkZXJgLCBpdOKAmXMgZ3VhcmFudGVlZFxuICAvLyB0byBub3QgZXhpc3QgYmVjYXVzZSB3ZSBqdXN0IGdlbmVyYXRlZCBpdC5cbiAgZm9vdG5vdGVPcmRlci5wdXNoKGlkZW50aWZpZXIpXG5cbiAgZm9vdG5vdGVCeUlkW2lkZW50aWZpZXJdID0ge1xuICAgIHR5cGU6ICdmb290bm90ZURlZmluaXRpb24nLFxuICAgIGlkZW50aWZpZXI6IGlkZW50aWZpZXIsXG4gICAgY2hpbGRyZW46IFt7dHlwZTogJ3BhcmFncmFwaCcsIGNoaWxkcmVuOiBub2RlLmNoaWxkcmVufV0sXG4gICAgcG9zaXRpb246IG5vZGUucG9zaXRpb25cbiAgfVxuXG4gIHJldHVybiBmb290bm90ZVJlZmVyZW5jZShoLCB7XG4gICAgdHlwZTogJ2Zvb3Rub3RlUmVmZXJlbmNlJyxcbiAgICBpZGVudGlmaWVyOiBpZGVudGlmaWVyLFxuICAgIHBvc2l0aW9uOiBub2RlLnBvc2l0aW9uXG4gIH0pXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/heading.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = heading\n\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nfunction heading(h, node) {\n  return h(node, 'h' + node.depth, all(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9oZWFkaW5nLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBLFVBQVUsbUJBQU8sQ0FBQyxrRUFBUTs7QUFFMUI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2hlYWRpbmcuanM/YWFkNSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBoZWFkaW5nXG5cbnZhciBhbGwgPSByZXF1aXJlKCcuLi9hbGwnKVxuXG5mdW5jdGlvbiBoZWFkaW5nKGgsIG5vZGUpIHtcbiAgcmV0dXJuIGgobm9kZSwgJ2gnICsgbm9kZS5kZXB0aCwgYWxsKGgsIG5vZGUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/html.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = html\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\n\n// Return either a `raw` node in dangerous mode, otherwise nothing.\nfunction html(h, node) {\n  return h.dangerous ? h.augment(node, u('raw', node.value)) : null\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9odG1sLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBLFFBQVEsbUJBQU8sQ0FBQyxrRUFBZTs7QUFFL0I7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvaHRtbC5qcz8wNzFmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IGh0bWxcblxudmFyIHUgPSByZXF1aXJlKCd1bmlzdC1idWlsZGVyJylcblxuLy8gUmV0dXJuIGVpdGhlciBhIGByYXdgIG5vZGUgaW4gZGFuZ2Vyb3VzIG1vZGUsIG90aGVyd2lzZSBub3RoaW5nLlxuZnVuY3Rpb24gaHRtbChoLCBub2RlKSB7XG4gIHJldHVybiBoLmRhbmdlcm91cyA/IGguYXVnbWVudChub2RlLCB1KCdyYXcnLCBub2RlLnZhbHVlKSkgOiBudWxsXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js":
/*!*************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = imageReference\n\nvar normalize = __webpack_require__(/*! mdurl/encode */ \"(ssr)/./node_modules/mdurl/encode.js\")\nvar revert = __webpack_require__(/*! ../revert */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\")\n\nfunction imageReference(h, node) {\n  var def = h.definition(node.identifier)\n  var props\n\n  if (!def) {\n    return revert(h, node)\n  }\n\n  props = {src: normalize(def.url || ''), alt: node.alt}\n\n  if (def.title !== null && def.title !== undefined) {\n    props.title = def.title\n  }\n\n  return h(node, 'img', props)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9pbWFnZS1yZWZlcmVuY2UuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUEsZ0JBQWdCLG1CQUFPLENBQUMsMERBQWM7QUFDdEMsYUFBYSxtQkFBTyxDQUFDLHdFQUFXOztBQUVoQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLFdBQVc7O0FBRVg7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvaW1hZ2UtcmVmZXJlbmNlLmpzPzEwNWIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gaW1hZ2VSZWZlcmVuY2VcblxudmFyIG5vcm1hbGl6ZSA9IHJlcXVpcmUoJ21kdXJsL2VuY29kZScpXG52YXIgcmV2ZXJ0ID0gcmVxdWlyZSgnLi4vcmV2ZXJ0JylcblxuZnVuY3Rpb24gaW1hZ2VSZWZlcmVuY2UoaCwgbm9kZSkge1xuICB2YXIgZGVmID0gaC5kZWZpbml0aW9uKG5vZGUuaWRlbnRpZmllcilcbiAgdmFyIHByb3BzXG5cbiAgaWYgKCFkZWYpIHtcbiAgICByZXR1cm4gcmV2ZXJ0KGgsIG5vZGUpXG4gIH1cblxuICBwcm9wcyA9IHtzcmM6IG5vcm1hbGl6ZShkZWYudXJsIHx8ICcnKSwgYWx0OiBub2RlLmFsdH1cblxuICBpZiAoZGVmLnRpdGxlICE9PSBudWxsICYmIGRlZi50aXRsZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgcHJvcHMudGl0bGUgPSBkZWYudGl0bGVcbiAgfVxuXG4gIHJldHVybiBoKG5vZGUsICdpbWcnLCBwcm9wcylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/image.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar normalize = __webpack_require__(/*! mdurl/encode */ \"(ssr)/./node_modules/mdurl/encode.js\")\n\nmodule.exports = image\n\nfunction image(h, node) {\n  var props = {src: normalize(node.url), alt: node.alt}\n\n  if (node.title !== null && node.title !== undefined) {\n    props.title = node.title\n  }\n\n  return h(node, 'img', props)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9pbWFnZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixnQkFBZ0IsbUJBQU8sQ0FBQywwREFBYzs7QUFFdEM7O0FBRUE7QUFDQSxlQUFlOztBQUVmO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2ltYWdlLmpzPzUzZmIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBub3JtYWxpemUgPSByZXF1aXJlKCdtZHVybC9lbmNvZGUnKVxuXG5tb2R1bGUuZXhwb3J0cyA9IGltYWdlXG5cbmZ1bmN0aW9uIGltYWdlKGgsIG5vZGUpIHtcbiAgdmFyIHByb3BzID0ge3NyYzogbm9ybWFsaXplKG5vZGUudXJsKSwgYWx0OiBub2RlLmFsdH1cblxuICBpZiAobm9kZS50aXRsZSAhPT0gbnVsbCAmJiBub2RlLnRpdGxlICE9PSB1bmRlZmluZWQpIHtcbiAgICBwcm9wcy50aXRsZSA9IG5vZGUudGl0bGVcbiAgfVxuXG4gIHJldHVybiBoKG5vZGUsICdpbWcnLCBwcm9wcylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/index.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = {\n  blockquote: __webpack_require__(/*! ./blockquote */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\"),\n  break: __webpack_require__(/*! ./break */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js\"),\n  code: __webpack_require__(/*! ./code */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js\"),\n  delete: __webpack_require__(/*! ./delete */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js\"),\n  emphasis: __webpack_require__(/*! ./emphasis */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\"),\n  footnoteReference: __webpack_require__(/*! ./footnote-reference */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\"),\n  footnote: __webpack_require__(/*! ./footnote */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote.js\"),\n  heading: __webpack_require__(/*! ./heading */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js\"),\n  html: __webpack_require__(/*! ./html */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js\"),\n  imageReference: __webpack_require__(/*! ./image-reference */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\"),\n  image: __webpack_require__(/*! ./image */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js\"),\n  inlineCode: __webpack_require__(/*! ./inline-code */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\"),\n  linkReference: __webpack_require__(/*! ./link-reference */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\"),\n  link: __webpack_require__(/*! ./link */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js\"),\n  listItem: __webpack_require__(/*! ./list-item */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js\"),\n  list: __webpack_require__(/*! ./list */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js\"),\n  paragraph: __webpack_require__(/*! ./paragraph */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\"),\n  root: __webpack_require__(/*! ./root */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js\"),\n  strong: __webpack_require__(/*! ./strong */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js\"),\n  table: __webpack_require__(/*! ./table */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js\"),\n  text: __webpack_require__(/*! ./text */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js\"),\n  thematicBreak: __webpack_require__(/*! ./thematic-break */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\"),\n  toml: ignore,\n  yaml: ignore,\n  definition: ignore,\n  footnoteDefinition: ignore\n}\n\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n  return null\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = inlineCode\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\n\nfunction inlineCode(h, node) {\n  var value = node.value.replace(/\\r?\\n|\\r/g, ' ')\n  return h(node, 'code', [u('text', value)])\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9pbmxpbmUtY29kZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQSxRQUFRLG1CQUFPLENBQUMsa0VBQWU7O0FBRS9CO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2lubGluZS1jb2RlLmpzP2VlMzAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gaW5saW5lQ29kZVxuXG52YXIgdSA9IHJlcXVpcmUoJ3VuaXN0LWJ1aWxkZXInKVxuXG5mdW5jdGlvbiBpbmxpbmVDb2RlKGgsIG5vZGUpIHtcbiAgdmFyIHZhbHVlID0gbm9kZS52YWx1ZS5yZXBsYWNlKC9cXHI/XFxufFxcci9nLCAnICcpXG4gIHJldHVybiBoKG5vZGUsICdjb2RlJywgW3UoJ3RleHQnLCB2YWx1ZSldKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = linkReference\n\nvar normalize = __webpack_require__(/*! mdurl/encode */ \"(ssr)/./node_modules/mdurl/encode.js\")\nvar revert = __webpack_require__(/*! ../revert */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\")\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nfunction linkReference(h, node) {\n  var def = h.definition(node.identifier)\n  var props\n\n  if (!def) {\n    return revert(h, node)\n  }\n\n  props = {href: normalize(def.url || '')}\n\n  if (def.title !== null && def.title !== undefined) {\n    props.title = def.title\n  }\n\n  return h(node, 'a', props, all(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9saW5rLXJlZmVyZW5jZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQSxnQkFBZ0IsbUJBQU8sQ0FBQywwREFBYztBQUN0QyxhQUFhLG1CQUFPLENBQUMsd0VBQVc7QUFDaEMsVUFBVSxtQkFBTyxDQUFDLGtFQUFROztBQUUxQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLFdBQVc7O0FBRVg7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvbGluay1yZWZlcmVuY2UuanM/YzE3MSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBsaW5rUmVmZXJlbmNlXG5cbnZhciBub3JtYWxpemUgPSByZXF1aXJlKCdtZHVybC9lbmNvZGUnKVxudmFyIHJldmVydCA9IHJlcXVpcmUoJy4uL3JldmVydCcpXG52YXIgYWxsID0gcmVxdWlyZSgnLi4vYWxsJylcblxuZnVuY3Rpb24gbGlua1JlZmVyZW5jZShoLCBub2RlKSB7XG4gIHZhciBkZWYgPSBoLmRlZmluaXRpb24obm9kZS5pZGVudGlmaWVyKVxuICB2YXIgcHJvcHNcblxuICBpZiAoIWRlZikge1xuICAgIHJldHVybiByZXZlcnQoaCwgbm9kZSlcbiAgfVxuXG4gIHByb3BzID0ge2hyZWY6IG5vcm1hbGl6ZShkZWYudXJsIHx8ICcnKX1cblxuICBpZiAoZGVmLnRpdGxlICE9PSBudWxsICYmIGRlZi50aXRsZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgcHJvcHMudGl0bGUgPSBkZWYudGl0bGVcbiAgfVxuXG4gIHJldHVybiBoKG5vZGUsICdhJywgcHJvcHMsIGFsbChoLCBub2RlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/link.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar normalize = __webpack_require__(/*! mdurl/encode */ \"(ssr)/./node_modules/mdurl/encode.js\")\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nmodule.exports = link\n\nfunction link(h, node) {\n  var props = {href: normalize(node.url)}\n\n  if (node.title !== null && node.title !== undefined) {\n    props.title = node.title\n  }\n\n  return h(node, 'a', props, all(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9saW5rLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLDBEQUFjO0FBQ3RDLFVBQVUsbUJBQU8sQ0FBQyxrRUFBUTs7QUFFMUI7O0FBRUE7QUFDQSxlQUFlOztBQUVmO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2xpbmsuanM/NmVjNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIG5vcm1hbGl6ZSA9IHJlcXVpcmUoJ21kdXJsL2VuY29kZScpXG52YXIgYWxsID0gcmVxdWlyZSgnLi4vYWxsJylcblxubW9kdWxlLmV4cG9ydHMgPSBsaW5rXG5cbmZ1bmN0aW9uIGxpbmsoaCwgbm9kZSkge1xuICB2YXIgcHJvcHMgPSB7aHJlZjogbm9ybWFsaXplKG5vZGUudXJsKX1cblxuICBpZiAobm9kZS50aXRsZSAhPT0gbnVsbCAmJiBub2RlLnRpdGxlICE9PSB1bmRlZmluZWQpIHtcbiAgICBwcm9wcy50aXRsZSA9IG5vZGUudGl0bGVcbiAgfVxuXG4gIHJldHVybiBoKG5vZGUsICdhJywgcHJvcHMsIGFsbChoLCBub2RlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/list-item.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = listItem\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nfunction listItem(h, node, parent) {\n  var result = all(h, node)\n  var head = result[0]\n  var loose = parent ? listLoose(parent) : listItemLoose(node)\n  var props = {}\n  var wrapped = []\n  var length\n  var index\n  var child\n\n  if (typeof node.checked === 'boolean') {\n    if (!head || head.tagName !== 'p') {\n      head = h(null, 'p', [])\n      result.unshift(head)\n    }\n\n    if (head.children.length > 0) {\n      head.children.unshift(u('text', ' '))\n    }\n\n    head.children.unshift(\n      h(null, 'input', {\n        type: 'checkbox',\n        checked: node.checked,\n        disabled: true\n      })\n    )\n\n    // According to github-markdown-css, this class hides bullet.\n    // See: <https://github.com/sindresorhus/github-markdown-css>.\n    props.className = ['task-list-item']\n  }\n\n  length = result.length\n  index = -1\n\n  while (++index < length) {\n    child = result[index]\n\n    // Add eols before nodes, except if this is a loose, first paragraph.\n    if (loose || index !== 0 || child.tagName !== 'p') {\n      wrapped.push(u('text', '\\n'))\n    }\n\n    if (child.tagName === 'p' && !loose) {\n      wrapped = wrapped.concat(child.children)\n    } else {\n      wrapped.push(child)\n    }\n  }\n\n  // Add a final eol.\n  if (length && (loose || child.tagName !== 'p')) {\n    wrapped.push(u('text', '\\n'))\n  }\n\n  return h(node, 'li', props, wrapped)\n}\n\nfunction listLoose(node) {\n  var loose = node.spread\n  var children = node.children\n  var length = children.length\n  var index = -1\n\n  while (!loose && ++index < length) {\n    loose = listItemLoose(children[index])\n  }\n\n  return loose\n}\n\nfunction listItemLoose(node) {\n  var spread = node.spread\n\n  return spread === undefined || spread === null\n    ? node.children.length > 1\n    : spread\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/list.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = list\n\nvar wrap = __webpack_require__(/*! ../wrap */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/wrap.js\")\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nfunction list(h, node) {\n  var props = {}\n  var name = node.ordered ? 'ol' : 'ul'\n  var items\n  var index = -1\n  var length\n\n  if (typeof node.start === 'number' && node.start !== 1) {\n    props.start = node.start\n  }\n\n  items = all(h, node)\n  length = items.length\n\n  // Like GitHub, add a class for custom styling.\n  while (++index < length) {\n    if (\n      items[index].properties.className &&\n      items[index].properties.className.indexOf('task-list-item') !== -1\n    ) {\n      props.className = ['contains-task-list']\n      break\n    }\n  }\n\n  return h(node, name, props, wrap(items, true))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9saXN0LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBLFdBQVcsbUJBQU8sQ0FBQyxvRUFBUztBQUM1QixVQUFVLG1CQUFPLENBQUMsa0VBQVE7O0FBRTFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2xpc3QuanM/NjFmOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBsaXN0XG5cbnZhciB3cmFwID0gcmVxdWlyZSgnLi4vd3JhcCcpXG52YXIgYWxsID0gcmVxdWlyZSgnLi4vYWxsJylcblxuZnVuY3Rpb24gbGlzdChoLCBub2RlKSB7XG4gIHZhciBwcm9wcyA9IHt9XG4gIHZhciBuYW1lID0gbm9kZS5vcmRlcmVkID8gJ29sJyA6ICd1bCdcbiAgdmFyIGl0ZW1zXG4gIHZhciBpbmRleCA9IC0xXG4gIHZhciBsZW5ndGhcblxuICBpZiAodHlwZW9mIG5vZGUuc3RhcnQgPT09ICdudW1iZXInICYmIG5vZGUuc3RhcnQgIT09IDEpIHtcbiAgICBwcm9wcy5zdGFydCA9IG5vZGUuc3RhcnRcbiAgfVxuXG4gIGl0ZW1zID0gYWxsKGgsIG5vZGUpXG4gIGxlbmd0aCA9IGl0ZW1zLmxlbmd0aFxuXG4gIC8vIExpa2UgR2l0SHViLCBhZGQgYSBjbGFzcyBmb3IgY3VzdG9tIHN0eWxpbmcuXG4gIHdoaWxlICgrK2luZGV4IDwgbGVuZ3RoKSB7XG4gICAgaWYgKFxuICAgICAgaXRlbXNbaW5kZXhdLnByb3BlcnRpZXMuY2xhc3NOYW1lICYmXG4gICAgICBpdGVtc1tpbmRleF0ucHJvcGVydGllcy5jbGFzc05hbWUuaW5kZXhPZigndGFzay1saXN0LWl0ZW0nKSAhPT0gLTFcbiAgICApIHtcbiAgICAgIHByb3BzLmNsYXNzTmFtZSA9IFsnY29udGFpbnMtdGFzay1saXN0J11cbiAgICAgIGJyZWFrXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGgobm9kZSwgbmFtZSwgcHJvcHMsIHdyYXAoaXRlbXMsIHRydWUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = paragraph\n\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nfunction paragraph(h, node) {\n  return h(node, 'p', all(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9wYXJhZ3JhcGguanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUEsVUFBVSxtQkFBTyxDQUFDLGtFQUFROztBQUUxQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvcGFyYWdyYXBoLmpzPzE5YzQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gcGFyYWdyYXBoXG5cbnZhciBhbGwgPSByZXF1aXJlKCcuLi9hbGwnKVxuXG5mdW5jdGlvbiBwYXJhZ3JhcGgoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAncCcsIGFsbChoLCBub2RlKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/root.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = root\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\nvar wrap = __webpack_require__(/*! ../wrap */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/wrap.js\")\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nfunction root(h, node) {\n  return h.augment(node, u('root', wrap(all(h, node))))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9yb290LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBLFFBQVEsbUJBQU8sQ0FBQyxrRUFBZTtBQUMvQixXQUFXLG1CQUFPLENBQUMsb0VBQVM7QUFDNUIsVUFBVSxtQkFBTyxDQUFDLGtFQUFROztBQUUxQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvcm9vdC5qcz8yMjEyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IHJvb3RcblxudmFyIHUgPSByZXF1aXJlKCd1bmlzdC1idWlsZGVyJylcbnZhciB3cmFwID0gcmVxdWlyZSgnLi4vd3JhcCcpXG52YXIgYWxsID0gcmVxdWlyZSgnLi4vYWxsJylcblxuZnVuY3Rpb24gcm9vdChoLCBub2RlKSB7XG4gIHJldHVybiBoLmF1Z21lbnQobm9kZSwgdSgncm9vdCcsIHdyYXAoYWxsKGgsIG5vZGUpKSkpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/strong.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = strong\n\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nfunction strong(h, node) {\n  return h(node, 'strong', all(h, node))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9zdHJvbmcuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUEsVUFBVSxtQkFBTyxDQUFDLGtFQUFROztBQUUxQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvc3Ryb25nLmpzP2M4NzIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gc3Ryb25nXG5cbnZhciBhbGwgPSByZXF1aXJlKCcuLi9hbGwnKVxuXG5mdW5jdGlvbiBzdHJvbmcoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnc3Ryb25nJywgYWxsKGgsIG5vZGUpKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = table\n\nvar position = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/index.js\")\nvar wrap = __webpack_require__(/*! ../wrap */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/wrap.js\")\nvar all = __webpack_require__(/*! ../all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nfunction table(h, node) {\n  var rows = node.children\n  var index = rows.length\n  var align = node.align || []\n  var alignLength = align.length\n  var result = []\n  var pos\n  var row\n  var out\n  var name\n  var cell\n\n  while (index--) {\n    row = rows[index].children\n    name = index === 0 ? 'th' : 'td'\n    pos = alignLength || row.length\n    out = []\n\n    while (pos--) {\n      cell = row[pos]\n      out[pos] = h(cell, name, {align: align[pos]}, cell ? all(h, cell) : [])\n    }\n\n    result[index] = h(rows[index], 'tr', wrap(out, true))\n  }\n\n  return h(\n    node,\n    'table',\n    wrap(\n      [h(result[0].position, 'thead', wrap([result[0]], true))].concat(\n        result[1]\n          ? h(\n              {\n                start: position.start(result[1]),\n                end: position.end(result[result.length - 1])\n              },\n              'tbody',\n              wrap(result.slice(1), true)\n            )\n          : []\n      ),\n      true\n    )\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/text.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = text\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\n\nfunction text(h, node) {\n  return h.augment(\n    node,\n    u('text', String(node.value).replace(/[ \\t]*(\\r?\\n|\\r)[ \\t]*/g, '$1'))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBLFFBQVEsbUJBQU8sQ0FBQyxrRUFBZTs7QUFFL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL3RleHQuanM/ZjA2YyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSB0ZXh0XG5cbnZhciB1ID0gcmVxdWlyZSgndW5pc3QtYnVpbGRlcicpXG5cbmZ1bmN0aW9uIHRleHQoaCwgbm9kZSkge1xuICByZXR1cm4gaC5hdWdtZW50KFxuICAgIG5vZGUsXG4gICAgdSgndGV4dCcsIFN0cmluZyhub2RlLnZhbHVlKS5yZXBsYWNlKC9bIFxcdF0qKFxccj9cXG58XFxyKVsgXFx0XSovZywgJyQxJykpXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js ***!
  \************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = thematicBreak\n\nfunction thematicBreak(h, node) {\n  return h(node, 'hr')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90aGVtYXRpYy1icmVhay5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvdGhlbWF0aWMtYnJlYWsuanM/ZmVhNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSB0aGVtYXRpY0JyZWFrXG5cbmZ1bmN0aW9uIHRoZW1hdGljQnJlYWsoaCwgbm9kZSkge1xuICByZXR1cm4gaChub2RlLCAnaHInKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/index.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = toHast\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\nvar visit = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/index.js\")\nvar position = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/index.js\")\nvar generated = __webpack_require__(/*! unist-util-generated */ \"(ssr)/./node_modules/unist-util-generated/index.js\")\nvar definitions = __webpack_require__(/*! mdast-util-definitions */ \"(ssr)/./node_modules/mdast-util-definitions/index.js\")\nvar one = __webpack_require__(/*! ./one */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/one.js\")\nvar footer = __webpack_require__(/*! ./footer */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js\")\nvar handlers = __webpack_require__(/*! ./handlers */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js\")\n\nvar own = {}.hasOwnProperty\n\nvar deprecationWarningIssued = false\n\n// Factory to transform.\nfunction factory(tree, options) {\n  var settings = options || {}\n\n  // Issue a warning if the deprecated tag 'allowDangerousHTML' is used\n  if (settings.allowDangerousHTML !== undefined && !deprecationWarningIssued) {\n    deprecationWarningIssued = true\n    console.warn(\n      'mdast-util-to-hast: deprecation: `allowDangerousHTML` is nonstandard, use `allowDangerousHtml` instead'\n    )\n  }\n\n  var dangerous = settings.allowDangerousHtml || settings.allowDangerousHTML\n  var footnoteById = {}\n\n  h.dangerous = dangerous\n  h.definition = definitions(tree)\n  h.footnoteById = footnoteById\n  h.footnoteOrder = []\n  h.augment = augment\n  h.handlers = Object.assign({}, handlers, settings.handlers)\n  h.unknownHandler = settings.unknownHandler\n  h.passThrough = settings.passThrough\n\n  visit(tree, 'footnoteDefinition', onfootnotedefinition)\n\n  return h\n\n  // Finalise the created `right`, a hast node, from `left`, an mdast node.\n  function augment(left, right) {\n    var data\n    var ctx\n\n    // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n    if (left && left.data) {\n      data = left.data\n\n      if (data.hName) {\n        if (right.type !== 'element') {\n          right = {\n            type: 'element',\n            tagName: '',\n            properties: {},\n            children: []\n          }\n        }\n\n        right.tagName = data.hName\n      }\n\n      if (right.type === 'element' && data.hProperties) {\n        right.properties = Object.assign({}, right.properties, data.hProperties)\n      }\n\n      if (right.children && data.hChildren) {\n        right.children = data.hChildren\n      }\n    }\n\n    ctx = left && left.position ? left : {position: left}\n\n    if (!generated(ctx)) {\n      right.position = {\n        start: position.start(ctx),\n        end: position.end(ctx)\n      }\n    }\n\n    return right\n  }\n\n  // Create an element for `node`.\n  function h(node, tagName, props, children) {\n    if (\n      (children === undefined || children === null) &&\n      typeof props === 'object' &&\n      'length' in props\n    ) {\n      children = props\n      props = {}\n    }\n\n    return augment(node, {\n      type: 'element',\n      tagName: tagName,\n      properties: props || {},\n      children: children || []\n    })\n  }\n\n  function onfootnotedefinition(definition) {\n    var id = String(definition.identifier).toUpperCase()\n\n    // Mimick CM behavior of link definitions.\n    // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/8290999/index.js#L26>.\n    if (!own.call(footnoteById, id)) {\n      footnoteById[id] = definition\n    }\n  }\n}\n\n// Transform `tree`, which is an mdast node, to a hast node.\nfunction toHast(tree, options) {\n  var h = factory(tree, options)\n  var node = one(h, tree)\n  var foot = footer(h)\n\n  if (foot) {\n    node.children = node.children.concat(u('text', '\\n'), foot)\n  }\n\n  return node\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/one.js":
/*!****************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/one.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = one\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\nvar all = __webpack_require__(/*! ./all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\nvar own = {}.hasOwnProperty\n\n// Transform an unknown node.\nfunction unknown(h, node) {\n  if (text(node)) {\n    return h.augment(node, u('text', node.value))\n  }\n\n  return h(node, 'div', all(h, node))\n}\n\n// Visit a node.\nfunction one(h, node, parent) {\n  var type = node && node.type\n  var fn\n\n  // Fail on non-nodes.\n  if (!type) {\n    throw new Error('Expected node, got `' + node + '`')\n  }\n\n  if (own.call(h.handlers, type)) {\n    fn = h.handlers[type]\n  } else if (h.passThrough && h.passThrough.indexOf(type) > -1) {\n    fn = returnNode\n  } else {\n    fn = h.unknownHandler\n  }\n\n  return (typeof fn === 'function' ? fn : unknown)(h, node, parent)\n}\n\n// Check if the node should be renderered as a text node.\nfunction text(node) {\n  var data = node.data || {}\n\n  if (\n    own.call(data, 'hName') ||\n    own.call(data, 'hProperties') ||\n    own.call(data, 'hChildren')\n  ) {\n    return false\n  }\n\n  return 'value' in node\n}\n\nfunction returnNode(h, node) {\n  var clone\n\n  if (node.children) {\n    clone = Object.assign({}, node)\n    clone.children = all(h, node)\n    return clone\n  }\n\n  return node\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/one.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js":
/*!*******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/revert.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = revert\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\nvar all = __webpack_require__(/*! ./all */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/all.js\")\n\n// Return the content of a reference without definition as Markdown.\nfunction revert(h, node) {\n  var subtype = node.referenceType\n  var suffix = ']'\n  var contents\n  var head\n  var tail\n\n  if (subtype === 'collapsed') {\n    suffix += '[]'\n  } else if (subtype === 'full') {\n    suffix += '[' + (node.label || node.identifier) + ']'\n  }\n\n  if (node.type === 'imageReference') {\n    return u('text', '![' + node.alt + suffix)\n  }\n\n  contents = all(h, node)\n  head = contents[0]\n\n  if (head && head.type === 'text') {\n    head.value = '[' + head.value\n  } else {\n    contents.unshift(u('text', '['))\n  }\n\n  tail = contents[contents.length - 1]\n\n  if (tail && tail.type === 'text') {\n    tail.value += suffix\n  } else {\n    contents.push(u('text', suffix))\n  }\n\n  return contents\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9yZXZlcnQuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUEsUUFBUSxtQkFBTyxDQUFDLGtFQUFlO0FBQy9CLFVBQVUsbUJBQU8sQ0FBQyxpRUFBTzs7QUFFekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9yZXZlcnQuanM/ZmVmZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSByZXZlcnRcblxudmFyIHUgPSByZXF1aXJlKCd1bmlzdC1idWlsZGVyJylcbnZhciBhbGwgPSByZXF1aXJlKCcuL2FsbCcpXG5cbi8vIFJldHVybiB0aGUgY29udGVudCBvZiBhIHJlZmVyZW5jZSB3aXRob3V0IGRlZmluaXRpb24gYXMgTWFya2Rvd24uXG5mdW5jdGlvbiByZXZlcnQoaCwgbm9kZSkge1xuICB2YXIgc3VidHlwZSA9IG5vZGUucmVmZXJlbmNlVHlwZVxuICB2YXIgc3VmZml4ID0gJ10nXG4gIHZhciBjb250ZW50c1xuICB2YXIgaGVhZFxuICB2YXIgdGFpbFxuXG4gIGlmIChzdWJ0eXBlID09PSAnY29sbGFwc2VkJykge1xuICAgIHN1ZmZpeCArPSAnW10nXG4gIH0gZWxzZSBpZiAoc3VidHlwZSA9PT0gJ2Z1bGwnKSB7XG4gICAgc3VmZml4ICs9ICdbJyArIChub2RlLmxhYmVsIHx8IG5vZGUuaWRlbnRpZmllcikgKyAnXSdcbiAgfVxuXG4gIGlmIChub2RlLnR5cGUgPT09ICdpbWFnZVJlZmVyZW5jZScpIHtcbiAgICByZXR1cm4gdSgndGV4dCcsICchWycgKyBub2RlLmFsdCArIHN1ZmZpeClcbiAgfVxuXG4gIGNvbnRlbnRzID0gYWxsKGgsIG5vZGUpXG4gIGhlYWQgPSBjb250ZW50c1swXVxuXG4gIGlmIChoZWFkICYmIGhlYWQudHlwZSA9PT0gJ3RleHQnKSB7XG4gICAgaGVhZC52YWx1ZSA9ICdbJyArIGhlYWQudmFsdWVcbiAgfSBlbHNlIHtcbiAgICBjb250ZW50cy51bnNoaWZ0KHUoJ3RleHQnLCAnWycpKVxuICB9XG5cbiAgdGFpbCA9IGNvbnRlbnRzW2NvbnRlbnRzLmxlbmd0aCAtIDFdXG5cbiAgaWYgKHRhaWwgJiYgdGFpbC50eXBlID09PSAndGV4dCcpIHtcbiAgICB0YWlsLnZhbHVlICs9IHN1ZmZpeFxuICB9IGVsc2Uge1xuICAgIGNvbnRlbnRzLnB1c2godSgndGV4dCcsIHN1ZmZpeCkpXG4gIH1cblxuICByZXR1cm4gY29udGVudHNcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/wrap.js":
/*!*****************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/wrap.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = wrap\n\nvar u = __webpack_require__(/*! unist-builder */ \"(ssr)/./node_modules/unist-builder/index.js\")\n\n// Wrap `nodes` with line feeds between each entry.\n// Optionally adds line feeds at the start and end.\nfunction wrap(nodes, loose) {\n  var result = []\n  var index = -1\n  var length = nodes.length\n\n  if (loose) {\n    result.push(u('text', '\\n'))\n  }\n\n  while (++index < length) {\n    if (index) {\n      result.push(u('text', '\\n'))\n    }\n\n    result.push(nodes[index])\n  }\n\n  if (loose && nodes.length > 0) {\n    result.push(u('text', '\\n'))\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi93cmFwLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBLFFBQVEsbUJBQU8sQ0FBQyxrRUFBZTs7QUFFL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL3dyYXAuanM/MGYwZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSB3cmFwXG5cbnZhciB1ID0gcmVxdWlyZSgndW5pc3QtYnVpbGRlcicpXG5cbi8vIFdyYXAgYG5vZGVzYCB3aXRoIGxpbmUgZmVlZHMgYmV0d2VlbiBlYWNoIGVudHJ5LlxuLy8gT3B0aW9uYWxseSBhZGRzIGxpbmUgZmVlZHMgYXQgdGhlIHN0YXJ0IGFuZCBlbmQuXG5mdW5jdGlvbiB3cmFwKG5vZGVzLCBsb29zZSkge1xuICB2YXIgcmVzdWx0ID0gW11cbiAgdmFyIGluZGV4ID0gLTFcbiAgdmFyIGxlbmd0aCA9IG5vZGVzLmxlbmd0aFxuXG4gIGlmIChsb29zZSkge1xuICAgIHJlc3VsdC5wdXNoKHUoJ3RleHQnLCAnXFxuJykpXG4gIH1cblxuICB3aGlsZSAoKytpbmRleCA8IGxlbmd0aCkge1xuICAgIGlmIChpbmRleCkge1xuICAgICAgcmVzdWx0LnB1c2godSgndGV4dCcsICdcXG4nKSlcbiAgICB9XG5cbiAgICByZXN1bHQucHVzaChub2Rlc1tpbmRleF0pXG4gIH1cblxuICBpZiAobG9vc2UgJiYgbm9kZXMubGVuZ3RoID4gMCkge1xuICAgIHJlc3VsdC5wdXNoKHUoJ3RleHQnLCAnXFxuJykpXG4gIH1cblxuICByZXR1cm4gcmVzdWx0XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/wrap.js\n");

/***/ })

};
;