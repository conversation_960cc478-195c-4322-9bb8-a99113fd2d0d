"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/trough";
exports.ids = ["vendor-chunks/trough"];
exports.modules = {

/***/ "(ssr)/./node_modules/trough/index.js":
/*!**************************************!*\
  !*** ./node_modules/trough/index.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar wrap = __webpack_require__(/*! ./wrap.js */ \"(ssr)/./node_modules/trough/wrap.js\")\n\nmodule.exports = trough\n\ntrough.wrap = wrap\n\nvar slice = [].slice\n\n// Create new middleware.\nfunction trough() {\n  var fns = []\n  var middleware = {}\n\n  middleware.run = run\n  middleware.use = use\n\n  return middleware\n\n  // Run `fns`.  Last argument must be a completion handler.\n  function run() {\n    var index = -1\n    var input = slice.call(arguments, 0, -1)\n    var done = arguments[arguments.length - 1]\n\n    if (typeof done !== 'function') {\n      throw new Error('Expected function as last argument, not ' + done)\n    }\n\n    next.apply(null, [null].concat(input))\n\n    // Run the next `fn`, if any.\n    function next(err) {\n      var fn = fns[++index]\n      var params = slice.call(arguments, 0)\n      var values = params.slice(1)\n      var length = input.length\n      var pos = -1\n\n      if (err) {\n        done(err)\n        return\n      }\n\n      // Copy non-nully input into values.\n      while (++pos < length) {\n        if (values[pos] === null || values[pos] === undefined) {\n          values[pos] = input[pos]\n        }\n      }\n\n      input = values\n\n      // Next or done.\n      if (fn) {\n        wrap(fn, next).apply(null, input)\n      } else {\n        done.apply(null, [null].concat(input))\n      }\n    }\n  }\n\n  // Add `fn` to the list.\n  function use(fn) {\n    if (typeof fn !== 'function') {\n      throw new Error('Expected `fn` to be a function, not ' + fn)\n    }\n\n    fns.push(fn)\n\n    return middleware\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/trough/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/trough/wrap.js":
/*!*************************************!*\
  !*** ./node_modules/trough/wrap.js ***!
  \*************************************/
/***/ ((module) => {

eval("\n\nvar slice = [].slice\n\nmodule.exports = wrap\n\n// Wrap `fn`.\n// Can be sync or async; return a promise, receive a completion handler, return\n// new values and errors.\nfunction wrap(fn, callback) {\n  var invoked\n\n  return wrapped\n\n  function wrapped() {\n    var params = slice.call(arguments, 0)\n    var callback = fn.length > params.length\n    var result\n\n    if (callback) {\n      params.push(done)\n    }\n\n    try {\n      result = fn.apply(null, params)\n    } catch (error) {\n      // Well, this is quite the pickle.\n      // `fn` received a callback and invoked it (thus continuing the pipeline),\n      // but later also threw an error.\n      // We’re not about to restart the pipeline again, so the only thing left\n      // to do is to throw the thing instead.\n      if (callback && invoked) {\n        throw error\n      }\n\n      return done(error)\n    }\n\n    if (!callback) {\n      if (result && typeof result.then === 'function') {\n        result.then(then, done)\n      } else if (result instanceof Error) {\n        done(result)\n      } else {\n        then(result)\n      }\n    }\n  }\n\n  // Invoke `next`, only once.\n  function done() {\n    if (!invoked) {\n      invoked = true\n\n      callback.apply(null, arguments)\n    }\n  }\n\n  // Invoke `done` with one value.\n  // Tracks if an error is passed, too.\n  function then(value) {\n    done(null, value)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/trough/wrap.js\n");

/***/ })

};
;