'use client';

import React, { useState } from 'react';
import { useList } from '@refinedev/core';
import { List, useDataGrid, CreateButton } from '@refinedev/mui';
import {
  DataGrid,
  GridColDef,
  GridActionsCellItem,
  GridRowParams,
} from '@mui/x-data-grid';
import {
  Box,
  Chip,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stepper,
  Step,
  StepLabel,
  Card,
  CardContent,
  LinearProgress,
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Edit as EditIcon,
  FileUpload as UploadIcon,
  Assignment as ApplicationIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/components/auth/ProtectedRoute';

const ApplicationsPage: React.FC = () => {
  const [selectedApplication, setSelectedApplication] = useState<any>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const { userRole, isApplicant } = useAuth();

  const { dataGridProps } = useDataGrid({
    resource: 'applications',
    initialSorter: [
      {
        field: 'created_at',
        order: 'desc',
      },
    ],
    filters: {
      permanent: isApplicant
        ? [{ field: 'applicant_id', operator: 'eq', value: 'current_user_id' }]
        : [],
    },
  });

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      new: 'info',
      in_review: 'primary',
      documents_pending: 'warning',
      interview_scheduled: 'secondary',
      interviewed: 'secondary',
      shortlisted: 'primary',
      offered: 'success',
      accepted: 'success',
      enrolled: 'success',
      rejected: 'error',
      withdrawn: 'default',
      waitlisted: 'warning',
    };
    return colors[status] || 'default';
  };

  const getApplicationProgress = (status: string) => {
    const progressMap: Record<string, number> = {
      new: 10,
      in_review: 25,
      documents_pending: 35,
      interview_scheduled: 50,
      interviewed: 65,
      shortlisted: 75,
      offered: 85,
      accepted: 95,
      enrolled: 100,
      rejected: 100,
      withdrawn: 100,
      waitlisted: 70,
    };
    return progressMap[status] || 0;
  };

  const applicationSteps = [
    'Application Submitted',
    'Document Verification',
    'Review Process',
    'Interview',
    'Decision',
    'Enrollment',
  ];

  const getActiveStep = (status: string) => {
    const stepMap: Record<string, number> = {
      new: 0,
      in_review: 2,
      documents_pending: 1,
      interview_scheduled: 3,
      interviewed: 3,
      shortlisted: 4,
      offered: 4,
      accepted: 5,
      enrolled: 5,
      rejected: 4,
      withdrawn: 4,
      waitlisted: 4,
    };
    return stepMap[status] || 0;
  };

  const columns: GridColDef[] = [
    {
      field: 'application_number',
      headerName: 'Application #',
      width: 150,
      renderCell: ({ value }) => (
        <Typography variant="body2" fontWeight="bold" color="primary">
          {value}
        </Typography>
      ),
    },
    {
      field: 'full_name',
      headerName: 'Applicant Name',
      width: 200,
      valueGetter: ({ row }) => `${row.first_name} ${row.last_name}`,
    },
    {
      field: 'program_name',
      headerName: 'Program',
      width: 200,
      valueGetter: ({ row }) => row.academic_programs?.name || 'N/A',
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 150,
      renderCell: ({ value }) => (
        <Chip
          label={value?.replace('_', ' ').toUpperCase()}
          color={getStatusColor(value)}
          size="small"
        />
      ),
    },
    {
      field: 'progress',
      headerName: 'Progress',
      width: 150,
      renderCell: ({ row }) => {
        const progress = getApplicationProgress(row.status);
        return (
          <Box sx={{ width: '100%' }}>
            <LinearProgress
              variant="determinate"
              value={progress}
              sx={{ height: 8, borderRadius: 4 }}
            />
            <Typography variant="caption" color="text.secondary">
              {progress}%
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'submission_date',
      headerName: 'Submitted',
      width: 120,
      type: 'date',
      valueGetter: ({ value }) => new Date(value),
    },
    {
      field: 'merit_score',
      headerName: 'Merit Score',
      width: 120,
      renderCell: ({ value }) => (
        <Typography variant="body2" fontWeight="bold">
          {value ? `${value}/100` : 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 200,
      getActions: ({ row }: GridRowParams) => [
        <GridActionsCellItem
          key="view"
          icon={<ViewIcon />}
          label="View Details"
          onClick={() => {
            setSelectedApplication(row);
            setDetailsDialogOpen(true);
          }}
        />,
        <GridActionsCellItem
          key="edit"
          icon={<EditIcon />}
          label="Edit"
          onClick={() => console.log('Edit application:', row)}
          disabled={!['new', 'documents_pending'].includes(row.status)}
        />,
        <GridActionsCellItem
          key="documents"
          icon={<UploadIcon />}
          label="Documents"
          onClick={() => console.log('Manage documents:', row)}
        />,
      ],
    },
  ];

  return (
    <ProtectedRoute resource="applications" action="list">
      <List
        title={isApplicant ? 'My Applications' : 'Applications Management'}
        headerButtons={
          isApplicant ? (
            <Button
              variant="contained"
              startIcon={<ApplicationIcon />}
              href="/apply"
            >
              New Application
            </Button>
          ) : (
            <CreateButton>New Application</CreateButton>
          )
        }
      >
        <DataGrid
          {...dataGridProps}
          columns={columns}
          autoHeight
          pageSizeOptions={[10, 25, 50]}
          disableRowSelectionOnClick
        />
      </List>

      {/* Application Details Dialog */}
      <Dialog
        open={detailsDialogOpen}
        onClose={() => setDetailsDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Application Details - {selectedApplication?.application_number}
        </DialogTitle>
        <DialogContent>
          {selectedApplication && (
            <Box>
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Application Progress
                  </Typography>
                  <Stepper
                    activeStep={getActiveStep(selectedApplication.status)}
                    alternativeLabel
                  >
                    {applicationSteps.map((label) => (
                      <Step key={label}>
                        <StepLabel>{label}</StepLabel>
                      </Step>
                    ))}
                  </Stepper>
                </CardContent>
              </Card>

              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Applicant Information
                  </Typography>
                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                    <Typography>
                      <strong>Name:</strong> {selectedApplication.first_name}{' '}
                      {selectedApplication.last_name}
                    </Typography>
                    <Typography>
                      <strong>Email:</strong> {selectedApplication.email}
                    </Typography>
                    <Typography>
                      <strong>Phone:</strong> {selectedApplication.phone}
                    </Typography>
                    <Typography>
                      <strong>Date of Birth:</strong>{' '}
                      {selectedApplication.date_of_birth}
                    </Typography>
                    <Typography>
                      <strong>Program:</strong>{' '}
                      {selectedApplication.academic_programs?.name}
                    </Typography>
                    <Typography>
                      <strong>Status:</strong>{' '}
                      <Chip
                        label={selectedApplication.status?.replace('_', ' ').toUpperCase()}
                        color={getStatusColor(selectedApplication.status)}
                        size="small"
                      />
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </ProtectedRoute>
  );
};

export default ApplicationsPage;
