// Merit Score Calculation System
export interface MeritCriteria {
  id: string;
  criteria_name: string;
  weight_percentage: number;
  max_score: number;
  description?: string;
}

export interface ApplicationScore {
  criteria_id: string;
  score: number;
  notes?: string;
}

export interface MeritCalculationResult {
  total_score: number;
  weighted_score: number;
  percentage: number;
  rank_position?: number;
  breakdown: {
    criteria_name: string;
    score: number;
    max_score: number;
    weight: number;
    weighted_contribution: number;
  }[];
}

// Calculate merit score for an application
export const calculateMeritScore = (
  criteria: MeritCriteria[],
  scores: ApplicationScore[]
): MeritCalculationResult => {
  let totalWeightedScore = 0;
  let totalMaxWeightedScore = 0;
  const breakdown: MeritCalculationResult['breakdown'] = [];

  criteria.forEach((criterion) => {
    const applicationScore = scores.find(s => s.criteria_id === criterion.id);
    const score = applicationScore?.score || 0;
    
    // Calculate weighted contribution
    const weightedContribution = (score / criterion.max_score) * criterion.weight_percentage;
    const maxWeightedContribution = criterion.weight_percentage;
    
    totalWeightedScore += weightedContribution;
    totalMaxWeightedScore += maxWeightedContribution;
    
    breakdown.push({
      criteria_name: criterion.criteria_name,
      score,
      max_score: criterion.max_score,
      weight: criterion.weight_percentage,
      weighted_contribution: weightedContribution,
    });
  });

  const percentage = totalMaxWeightedScore > 0 ? (totalWeightedScore / totalMaxWeightedScore) * 100 : 0;

  return {
    total_score: totalWeightedScore,
    weighted_score: totalWeightedScore,
    percentage: Math.round(percentage * 100) / 100, // Round to 2 decimal places
    breakdown,
  };
};

// Generate merit list for a program
export const generateMeritList = (
  applications: any[],
  criteria: MeritCriteria[]
): any[] => {
  const applicationsWithMerit = applications.map((application) => {
    const meritResult = calculateMeritScore(criteria, application.application_scores || []);
    
    return {
      ...application,
      merit_calculation: meritResult,
      final_merit_score: meritResult.percentage,
    };
  });

  // Sort by merit score (descending)
  const sortedApplications = applicationsWithMerit.sort(
    (a, b) => b.final_merit_score - a.final_merit_score
  );

  // Assign rank positions
  return sortedApplications.map((application, index) => ({
    ...application,
    rank_position: index + 1,
  }));
};

// Default merit criteria templates
export const DEFAULT_MERIT_CRITERIA: Omit<MeritCriteria, 'id'>[] = [
  {
    criteria_name: 'Academic Performance',
    weight_percentage: 40,
    max_score: 100,
    description: 'Previous academic grades and qualifications',
  },
  {
    criteria_name: 'Interview Score',
    weight_percentage: 30,
    max_score: 10,
    description: 'Performance in admission interview',
  },
  {
    criteria_name: 'Entrance Test',
    weight_percentage: 20,
    max_score: 100,
    description: 'Score in entrance examination',
  },
  {
    criteria_name: 'Extracurricular Activities',
    weight_percentage: 10,
    max_score: 50,
    description: 'Sports, cultural activities, leadership roles',
  },
];

// Validate merit criteria (weights should sum to 100%)
export const validateMeritCriteria = (criteria: MeritCriteria[]): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  // Check if weights sum to 100%
  const totalWeight = criteria.reduce((sum, criterion) => sum + criterion.weight_percentage, 0);
  if (Math.abs(totalWeight - 100) > 0.01) {
    errors.push(`Total weight percentage must equal 100%. Current total: ${totalWeight}%`);
  }
  
  // Check for duplicate criteria names
  const names = criteria.map(c => c.criteria_name.toLowerCase());
  const duplicates = names.filter((name, index) => names.indexOf(name) !== index);
  if (duplicates.length > 0) {
    errors.push(`Duplicate criteria names found: ${duplicates.join(', ')}`);
  }
  
  // Check for valid weight percentages
  criteria.forEach((criterion, index) => {
    if (criterion.weight_percentage <= 0 || criterion.weight_percentage > 100) {
      errors.push(`Criterion "${criterion.criteria_name}" has invalid weight: ${criterion.weight_percentage}%`);
    }
    
    if (criterion.max_score <= 0) {
      errors.push(`Criterion "${criterion.criteria_name}" has invalid max score: ${criterion.max_score}`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Calculate cutoff scores for different categories
export const calculateCutoffScores = (
  meritList: any[],
  capacities: { [category: string]: number }
): { [category: string]: number } => {
  const cutoffs: { [category: string]: number } = {};
  
  Object.entries(capacities).forEach(([category, capacity]) => {
    // Filter applications for this category if needed
    const categoryApplications = meritList; // Implement category filtering if needed
    
    if (categoryApplications.length >= capacity) {
      cutoffs[category] = categoryApplications[capacity - 1]?.final_merit_score || 0;
    } else {
      cutoffs[category] = 0; // All applicants qualify
    }
  });
  
  return cutoffs;
};

// Auto-calculate academic performance score from percentage
export const calculateAcademicScore = (percentage: number, maxScore: number = 100): number => {
  // Simple linear mapping: percentage directly maps to score
  return Math.min(percentage, maxScore);
};

// Convert interview rating to score
export const convertInterviewRatingToScore = (rating: number, maxRating: number = 10): number => {
  return rating; // Direct mapping for 1-10 scale
};

// Batch update merit scores for multiple applications
export const batchUpdateMeritScores = async (
  applications: any[],
  criteria: MeritCriteria[],
  updateFunction: (applicationId: string, meritScore: number, rankPosition: number) => Promise<void>
): Promise<void> => {
  const meritList = generateMeritList(applications, criteria);
  
  const updatePromises = meritList.map((application) =>
    updateFunction(
      application.id,
      application.final_merit_score,
      application.rank_position
    )
  );
  
  await Promise.all(updatePromises);
};

// Export utility functions for merit score formatting
export const formatMeritScore = (score: number): string => {
  return `${score.toFixed(2)}%`;
};

export const getMeritGrade = (percentage: number): string => {
  if (percentage >= 90) return 'A+';
  if (percentage >= 80) return 'A';
  if (percentage >= 70) return 'B+';
  if (percentage >= 60) return 'B';
  if (percentage >= 50) return 'C+';
  if (percentage >= 40) return 'C';
  return 'F';
};

export const getMeritColor = (percentage: number): string => {
  if (percentage >= 80) return '#4caf50'; // Green
  if (percentage >= 60) return '#ff9800'; // Orange
  if (percentage >= 40) return '#f44336'; // Red
  return '#9e9e9e'; // Grey
};
