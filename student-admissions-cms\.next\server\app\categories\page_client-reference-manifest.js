globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/categories/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@refinedev/core/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@refinedev/core/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@refinedev/kbar/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@refinedev/kbar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@refinedev/mui/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@refinedev/mui/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@refinedev/nextjs-router/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@refinedev/nextjs-router/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/color-mode/index.tsx":{"*":{"id":"(ssr)/./src/contexts/color-mode/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/providers/auth-provider/auth-provider.client.ts":{"*":{"id":"(ssr)/./src/providers/auth-provider/auth-provider.client.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/providers/data-provider/index.ts":{"*":{"id":"(ssr)/./src/providers/data-provider/index.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/providers/devtools/index.tsx":{"*":{"id":"(ssr)/./src/providers/devtools/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth-page/index.tsx":{"*":{"id":"(ssr)/./src/components/auth-page/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/blog-posts/page.tsx":{"*":{"id":"(ssr)/./src/app/blog-posts/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/header/index.tsx":{"*":{"id":"(ssr)/./src/components/header/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/categories/page.tsx":{"*":{"id":"(ssr)/./src/app/categories/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/blog-posts/edit/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/blog-posts/edit/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/blog-posts/show/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/blog-posts/show/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/inquiries/page.tsx":{"*":{"id":"(ssr)/./src/app/inquiries/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/applications/page.tsx":{"*":{"id":"(ssr)/./src/app/applications/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\@refinedev\\core\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@refinedev/core/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\@refinedev\\kbar\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@refinedev/kbar/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\@refinedev\\mui\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@refinedev/mui/dist/index.mjs","name":"*","chunks":["app/categories/layout","static/chunks/app/categories/layout.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\@refinedev\\nextjs-router\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@refinedev/nextjs-router/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\contexts\\color-mode\\index.tsx":{"id":"(app-pages-browser)/./src/contexts/color-mode/index.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\providers\\auth-provider\\auth-provider.client.ts":{"id":"(app-pages-browser)/./src/providers/auth-provider/auth-provider.client.ts","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\providers\\data-provider\\index.ts":{"id":"(app-pages-browser)/./src/providers/data-provider/index.ts","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\providers\\devtools\\index.tsx":{"id":"(app-pages-browser)/./src/providers/devtools/index.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\components\\auth-page\\index.tsx":{"id":"(app-pages-browser)/./src/components/auth-page/index.tsx","name":"*","chunks":[],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\blog-posts\\page.tsx":{"id":"(app-pages-browser)/./src/app/blog-posts/page.tsx","name":"*","chunks":[],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\components\\header\\index.tsx":{"id":"(app-pages-browser)/./src/components/header/index.tsx","name":"*","chunks":["app/categories/layout","static/chunks/app/categories/layout.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\categories\\page.tsx":{"id":"(app-pages-browser)/./src/app/categories/page.tsx","name":"*","chunks":["app/categories/page","static/chunks/app/categories/page.js"],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\blog-posts\\edit\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/blog-posts/edit/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\blog-posts\\show\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/blog-posts/show/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\inquiries\\page.tsx":{"id":"(app-pages-browser)/./src/app/inquiries/page.tsx","name":"*","chunks":[],"async":false},"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\applications\\page.tsx":{"id":"(app-pages-browser)/./src/app/applications/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\":[],"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\page":[],"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\layout":[],"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\not-found":[],"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\categories\\page":[],"C:\\citrus-works\\CMS\\student-admissions-cms\\src\\app\\categories\\layout":[]}}