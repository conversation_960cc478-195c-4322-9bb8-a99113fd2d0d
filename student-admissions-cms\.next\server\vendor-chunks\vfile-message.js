"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vfile-message";
exports.ids = ["vendor-chunks/vfile-message"];
exports.modules = {

/***/ "(ssr)/./node_modules/vfile-message/index.js":
/*!*********************************************!*\
  !*** ./node_modules/vfile-message/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar stringify = __webpack_require__(/*! unist-util-stringify-position */ \"(ssr)/./node_modules/unist-util-stringify-position/index.js\")\n\nmodule.exports = VMessage\n\n// Inherit from `Error#`.\nfunction VMessagePrototype() {}\nVMessagePrototype.prototype = Error.prototype\nVMessage.prototype = new VMessagePrototype()\n\n// Message properties.\nvar proto = VMessage.prototype\n\nproto.file = ''\nproto.name = ''\nproto.reason = ''\nproto.message = ''\nproto.stack = ''\nproto.fatal = null\nproto.column = null\nproto.line = null\n\n// Construct a new VMessage.\n//\n// Note: We cannot invoke `Error` on the created context, as that adds readonly\n// `line` and `column` attributes on Safari 9, thus throwing and failing the\n// data.\nfunction VMessage(reason, position, origin) {\n  var parts\n  var range\n  var location\n\n  if (typeof position === 'string') {\n    origin = position\n    position = null\n  }\n\n  parts = parseOrigin(origin)\n  range = stringify(position) || '1:1'\n\n  location = {\n    start: {line: null, column: null},\n    end: {line: null, column: null}\n  }\n\n  // Node.\n  if (position && position.position) {\n    position = position.position\n  }\n\n  if (position) {\n    // Position.\n    if (position.start) {\n      location = position\n      position = position.start\n    } else {\n      // Point.\n      location.start = position\n    }\n  }\n\n  if (reason.stack) {\n    this.stack = reason.stack\n    reason = reason.message\n  }\n\n  this.message = reason\n  this.name = range\n  this.reason = reason\n  this.line = position ? position.line : null\n  this.column = position ? position.column : null\n  this.location = location\n  this.source = parts[0]\n  this.ruleId = parts[1]\n}\n\nfunction parseOrigin(origin) {\n  var result = [null, null]\n  var index\n\n  if (typeof origin === 'string') {\n    index = origin.indexOf(':')\n\n    if (index === -1) {\n      result[1] = origin\n    } else {\n      result[0] = origin.slice(0, index)\n      result[1] = origin.slice(index + 1)\n    }\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile-message/index.js\n");

/***/ })

};
;