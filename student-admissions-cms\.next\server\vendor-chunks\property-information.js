"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/property-information";
exports.ids = ["vendor-chunks/property-information"];
exports.modules = {

/***/ "(ssr)/./node_modules/property-information/find.js":
/*!***************************************************!*\
  !*** ./node_modules/property-information/find.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar normalize = __webpack_require__(/*! ./normalize */ \"(ssr)/./node_modules/property-information/normalize.js\")\nvar DefinedInfo = __webpack_require__(/*! ./lib/util/defined-info */ \"(ssr)/./node_modules/property-information/lib/util/defined-info.js\")\nvar Info = __webpack_require__(/*! ./lib/util/info */ \"(ssr)/./node_modules/property-information/lib/util/info.js\")\n\nvar data = 'data'\n\nmodule.exports = find\n\nvar valid = /^data[-\\w.:]+$/i\nvar dash = /-[a-z]/g\nvar cap = /[A-Z]/g\n\nfunction find(schema, value) {\n  var normal = normalize(value)\n  var prop = value\n  var Type = Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === data && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      prop = datasetToProperty(value)\n    } else {\n      value = datasetToAttribute(value)\n    }\n\n    Type = DefinedInfo\n  }\n\n  return new Type(prop, value)\n}\n\nfunction datasetToProperty(attribute) {\n  var value = attribute.slice(5).replace(dash, camelcase)\n  return data + value.charAt(0).toUpperCase() + value.slice(1)\n}\n\nfunction datasetToAttribute(property) {\n  var value = property.slice(4)\n\n  if (dash.test(value)) {\n    return property\n  }\n\n  value = value.replace(cap, kebab)\n\n  if (value.charAt(0) !== '-') {\n    value = '-' + value\n  }\n\n  return data + value\n}\n\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/find.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/html.js":
/*!***************************************************!*\
  !*** ./node_modules/property-information/html.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar merge = __webpack_require__(/*! ./lib/util/merge */ \"(ssr)/./node_modules/property-information/lib/util/merge.js\")\nvar xlink = __webpack_require__(/*! ./lib/xlink */ \"(ssr)/./node_modules/property-information/lib/xlink.js\")\nvar xml = __webpack_require__(/*! ./lib/xml */ \"(ssr)/./node_modules/property-information/lib/xml.js\")\nvar xmlns = __webpack_require__(/*! ./lib/xmlns */ \"(ssr)/./node_modules/property-information/lib/xmlns.js\")\nvar aria = __webpack_require__(/*! ./lib/aria */ \"(ssr)/./node_modules/property-information/lib/aria.js\")\nvar html = __webpack_require__(/*! ./lib/html */ \"(ssr)/./node_modules/property-information/lib/html.js\")\n\nmodule.exports = merge([xml, xlink, xmlns, aria, html])\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vaHRtbC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixZQUFZLG1CQUFPLENBQUMscUZBQWtCO0FBQ3RDLFlBQVksbUJBQU8sQ0FBQywyRUFBYTtBQUNqQyxVQUFVLG1CQUFPLENBQUMsdUVBQVc7QUFDN0IsWUFBWSxtQkFBTyxDQUFDLDJFQUFhO0FBQ2pDLFdBQVcsbUJBQU8sQ0FBQyx5RUFBWTtBQUMvQixXQUFXLG1CQUFPLENBQUMseUVBQVk7O0FBRS9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9odG1sLmpzPzA0YmEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBtZXJnZSA9IHJlcXVpcmUoJy4vbGliL3V0aWwvbWVyZ2UnKVxudmFyIHhsaW5rID0gcmVxdWlyZSgnLi9saWIveGxpbmsnKVxudmFyIHhtbCA9IHJlcXVpcmUoJy4vbGliL3htbCcpXG52YXIgeG1sbnMgPSByZXF1aXJlKCcuL2xpYi94bWxucycpXG52YXIgYXJpYSA9IHJlcXVpcmUoJy4vbGliL2FyaWEnKVxudmFyIGh0bWwgPSByZXF1aXJlKCcuL2xpYi9odG1sJylcblxubW9kdWxlLmV4cG9ydHMgPSBtZXJnZShbeG1sLCB4bGluaywgeG1sbnMsIGFyaWEsIGh0bWxdKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/aria.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/aria.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar types = __webpack_require__(/*! ./util/types */ \"(ssr)/./node_modules/property-information/lib/util/types.js\")\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/property-information/lib/util/create.js\")\n\nvar booleanish = types.booleanish\nvar number = types.number\nvar spaceSeparated = types.spaceSeparated\n\nmodule.exports = create({\n  transform: ariaTransform,\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  }\n})\n\nfunction ariaTransform(_, prop) {\n  return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/aria.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/html.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/html.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar types = __webpack_require__(/*! ./util/types */ \"(ssr)/./node_modules/property-information/lib/util/types.js\")\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/property-information/lib/util/create.js\")\nvar caseInsensitiveTransform = __webpack_require__(/*! ./util/case-insensitive-transform */ \"(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\")\n\nvar boolean = types.boolean\nvar overloadedBoolean = types.overloadedBoolean\nvar booleanish = types.booleanish\nvar number = types.number\nvar spaceSeparated = types.spaceSeparated\nvar commaSeparated = types.commaSeparated\n\nmodule.exports = create({\n  space: 'html',\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  transform: caseInsensitiveTransform,\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: commaSeparated,\n    acceptCharset: spaceSeparated,\n    accessKey: spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: boolean,\n    allowPaymentRequest: boolean,\n    allowUserMedia: boolean,\n    alt: null,\n    as: null,\n    async: boolean,\n    autoCapitalize: null,\n    autoComplete: spaceSeparated,\n    autoFocus: boolean,\n    autoPlay: boolean,\n    capture: boolean,\n    charSet: null,\n    checked: boolean,\n    cite: null,\n    className: spaceSeparated,\n    cols: number,\n    colSpan: null,\n    content: null,\n    contentEditable: booleanish,\n    controls: boolean,\n    controlsList: spaceSeparated,\n    coords: number | commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: boolean,\n    defer: boolean,\n    dir: null,\n    dirName: null,\n    disabled: boolean,\n    download: overloadedBoolean,\n    draggable: booleanish,\n    encType: null,\n    enterKeyHint: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: boolean,\n    formTarget: null,\n    headers: spaceSeparated,\n    height: number,\n    hidden: boolean,\n    high: number,\n    href: null,\n    hrefLang: null,\n    htmlFor: spaceSeparated,\n    httpEquiv: spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: commaSeparated,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: boolean,\n    itemId: null,\n    itemProp: spaceSeparated,\n    itemRef: spaceSeparated,\n    itemScope: boolean,\n    itemType: spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: boolean,\n    low: number,\n    manifest: null,\n    max: null,\n    maxLength: number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: number,\n    multiple: boolean,\n    muted: boolean,\n    name: null,\n    nonce: null,\n    noModule: boolean,\n    noValidate: boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforePrint: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextMenu: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: boolean,\n    optimum: number,\n    pattern: null,\n    ping: spaceSeparated,\n    placeholder: null,\n    playsInline: boolean,\n    poster: null,\n    preload: null,\n    readOnly: boolean,\n    referrerPolicy: null,\n    rel: spaceSeparated,\n    required: boolean,\n    reversed: boolean,\n    rows: number,\n    rowSpan: number,\n    sandbox: spaceSeparated,\n    scope: null,\n    scoped: boolean,\n    seamless: boolean,\n    selected: boolean,\n    shape: null,\n    size: number,\n    sizes: null,\n    slot: null,\n    span: number,\n    spellCheck: booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: commaSeparated,\n    start: number,\n    step: null,\n    style: null,\n    tabIndex: number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: boolean,\n    useMap: null,\n    value: booleanish,\n    width: number,\n    wrap: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: boolean, // Lists. Use CSS to reduce space between items instead\n    declare: boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: number, // `<img>` and `<object>`\n    leftMargin: number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: number, // `<body>`\n    marginWidth: number, // `<body>`\n    noResize: boolean, // `<frame>`\n    noHref: boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: boolean,\n    disableRemotePlayback: boolean,\n    prefix: null,\n    property: null,\n    results: number,\n    security: null,\n    unselectable: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/svg.js":
/*!******************************************************!*\
  !*** ./node_modules/property-information/lib/svg.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar types = __webpack_require__(/*! ./util/types */ \"(ssr)/./node_modules/property-information/lib/util/types.js\")\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/property-information/lib/util/create.js\")\nvar caseSensitiveTransform = __webpack_require__(/*! ./util/case-sensitive-transform */ \"(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\")\n\nvar boolean = types.boolean\nvar number = types.number\nvar spaceSeparated = types.spaceSeparated\nvar commaSeparated = types.commaSeparated\nvar commaOrSpaceSeparated = types.commaOrSpaceSeparated\n\nmodule.exports = create({\n  space: 'svg',\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  transform: caseSensitiveTransform,\n  properties: {\n    about: commaOrSpaceSeparated,\n    accentHeight: number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: number,\n    amplitude: number,\n    arabicForm: null,\n    ascent: number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: number,\n    by: null,\n    calcMode: null,\n    capHeight: number,\n    className: spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: number,\n    diffuseConstant: number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: number,\n    dominantBaseline: null,\n    download: boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: commaSeparated,\n    g2: commaSeparated,\n    glyphName: commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: number,\n    horizOriginX: number,\n    horizOriginY: number,\n    id: null,\n    ideographic: number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: number,\n    k: number,\n    k1: number,\n    k2: number,\n    k3: number,\n    k4: number,\n    kernelMatrix: commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: number,\n    overlineThickness: number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: number,\n    pointsAtY: number,\n    pointsAtZ: number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: commaOrSpaceSeparated,\n    rev: commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: commaOrSpaceSeparated,\n    requiredFeatures: commaOrSpaceSeparated,\n    requiredFonts: commaOrSpaceSeparated,\n    requiredFormats: commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: number,\n    specularExponent: number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: number,\n    strikethroughThickness: number,\n    string: null,\n    stroke: null,\n    strokeDashArray: commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: number,\n    strokeOpacity: number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: commaOrSpaceSeparated,\n    tabIndex: number,\n    tableValues: null,\n    target: null,\n    targetX: number,\n    targetY: number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    u1: null,\n    u2: null,\n    underlinePosition: number,\n    underlineThickness: number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: number,\n    values: null,\n    vAlphabetic: number,\n    vMathematical: number,\n    vectorEffect: null,\n    vHanging: number,\n    vIdeographic: number,\n    version: null,\n    vertAdvY: number,\n    vertOriginX: number,\n    vertOriginY: number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/svg.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/property-information/lib/util/case-insensitive-transform.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar caseSensitiveTransform = __webpack_require__(/*! ./case-sensitive-transform */ \"(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\")\n\nmodule.exports = caseInsensitiveTransform\n\nfunction caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase())\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0uanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosNkJBQTZCLG1CQUFPLENBQUMsa0hBQTRCOztBQUVqRTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2Nhc2UtaW5zZW5zaXRpdmUtdHJhbnNmb3JtLmpzPzQzNzgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtID0gcmVxdWlyZSgnLi9jYXNlLXNlbnNpdGl2ZS10cmFuc2Zvcm0nKVxuXG5tb2R1bGUuZXhwb3J0cyA9IGNhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybVxuXG5mdW5jdGlvbiBjYXNlSW5zZW5zaXRpdmVUcmFuc2Zvcm0oYXR0cmlidXRlcywgcHJvcGVydHkpIHtcbiAgcmV0dXJuIGNhc2VTZW5zaXRpdmVUcmFuc2Zvcm0oYXR0cmlidXRlcywgcHJvcGVydHkudG9Mb3dlckNhc2UoKSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js":
/*!********************************************************************************!*\
  !*** ./node_modules/property-information/lib/util/case-sensitive-transform.js ***!
  \********************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = caseSensitiveTransform\n\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzPzBiNWQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gY2FzZVNlbnNpdGl2ZVRyYW5zZm9ybVxuXG5mdW5jdGlvbiBjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtKGF0dHJpYnV0ZXMsIGF0dHJpYnV0ZSkge1xuICByZXR1cm4gYXR0cmlidXRlIGluIGF0dHJpYnV0ZXMgPyBhdHRyaWJ1dGVzW2F0dHJpYnV0ZV0gOiBhdHRyaWJ1dGVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/create.js":
/*!**************************************************************!*\
  !*** ./node_modules/property-information/lib/util/create.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar normalize = __webpack_require__(/*! ../../normalize */ \"(ssr)/./node_modules/property-information/normalize.js\")\nvar Schema = __webpack_require__(/*! ./schema */ \"(ssr)/./node_modules/property-information/lib/util/schema.js\")\nvar DefinedInfo = __webpack_require__(/*! ./defined-info */ \"(ssr)/./node_modules/property-information/lib/util/defined-info.js\")\n\nmodule.exports = create\n\nfunction create(definition) {\n  var space = definition.space\n  var mustUseProperty = definition.mustUseProperty || []\n  var attributes = definition.attributes || {}\n  var props = definition.properties\n  var transform = definition.transform\n  var property = {}\n  var normal = {}\n  var prop\n  var info\n\n  for (prop in props) {\n    info = new DefinedInfo(\n      prop,\n      transform(attributes, prop),\n      props[prop],\n      space\n    )\n\n    if (mustUseProperty.indexOf(prop) !== -1) {\n      info.mustUseProperty = true\n    }\n\n    property[prop] = info\n\n    normal[normalize(prop)] = prop\n    normal[normalize(info.attribute)] = prop\n  }\n\n  return new Schema(property, normal, space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY3JlYXRlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLCtFQUFpQjtBQUN6QyxhQUFhLG1CQUFPLENBQUMsOEVBQVU7QUFDL0Isa0JBQWtCLG1CQUFPLENBQUMsMEZBQWdCOztBQUUxQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2NyZWF0ZS5qcz8zMmQ3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgbm9ybWFsaXplID0gcmVxdWlyZSgnLi4vLi4vbm9ybWFsaXplJylcbnZhciBTY2hlbWEgPSByZXF1aXJlKCcuL3NjaGVtYScpXG52YXIgRGVmaW5lZEluZm8gPSByZXF1aXJlKCcuL2RlZmluZWQtaW5mbycpXG5cbm1vZHVsZS5leHBvcnRzID0gY3JlYXRlXG5cbmZ1bmN0aW9uIGNyZWF0ZShkZWZpbml0aW9uKSB7XG4gIHZhciBzcGFjZSA9IGRlZmluaXRpb24uc3BhY2VcbiAgdmFyIG11c3RVc2VQcm9wZXJ0eSA9IGRlZmluaXRpb24ubXVzdFVzZVByb3BlcnR5IHx8IFtdXG4gIHZhciBhdHRyaWJ1dGVzID0gZGVmaW5pdGlvbi5hdHRyaWJ1dGVzIHx8IHt9XG4gIHZhciBwcm9wcyA9IGRlZmluaXRpb24ucHJvcGVydGllc1xuICB2YXIgdHJhbnNmb3JtID0gZGVmaW5pdGlvbi50cmFuc2Zvcm1cbiAgdmFyIHByb3BlcnR5ID0ge31cbiAgdmFyIG5vcm1hbCA9IHt9XG4gIHZhciBwcm9wXG4gIHZhciBpbmZvXG5cbiAgZm9yIChwcm9wIGluIHByb3BzKSB7XG4gICAgaW5mbyA9IG5ldyBEZWZpbmVkSW5mbyhcbiAgICAgIHByb3AsXG4gICAgICB0cmFuc2Zvcm0oYXR0cmlidXRlcywgcHJvcCksXG4gICAgICBwcm9wc1twcm9wXSxcbiAgICAgIHNwYWNlXG4gICAgKVxuXG4gICAgaWYgKG11c3RVc2VQcm9wZXJ0eS5pbmRleE9mKHByb3ApICE9PSAtMSkge1xuICAgICAgaW5mby5tdXN0VXNlUHJvcGVydHkgPSB0cnVlXG4gICAgfVxuXG4gICAgcHJvcGVydHlbcHJvcF0gPSBpbmZvXG5cbiAgICBub3JtYWxbbm9ybWFsaXplKHByb3ApXSA9IHByb3BcbiAgICBub3JtYWxbbm9ybWFsaXplKGluZm8uYXR0cmlidXRlKV0gPSBwcm9wXG4gIH1cblxuICByZXR1cm4gbmV3IFNjaGVtYShwcm9wZXJ0eSwgbm9ybWFsLCBzcGFjZSlcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/create.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/defined-info.js":
/*!********************************************************************!*\
  !*** ./node_modules/property-information/lib/util/defined-info.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Info = __webpack_require__(/*! ./info */ \"(ssr)/./node_modules/property-information/lib/util/info.js\")\nvar types = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/property-information/lib/util/types.js\")\n\nmodule.exports = DefinedInfo\n\nDefinedInfo.prototype = new Info()\nDefinedInfo.prototype.defined = true\n\nvar checks = [\n  'boolean',\n  'booleanish',\n  'overloadedBoolean',\n  'number',\n  'commaSeparated',\n  'spaceSeparated',\n  'commaOrSpaceSeparated'\n]\nvar checksLength = checks.length\n\nfunction DefinedInfo(property, attribute, mask, space) {\n  var index = -1\n  var check\n\n  mark(this, 'space', space)\n\n  Info.call(this, property, attribute)\n\n  while (++index < checksLength) {\n    check = checks[index]\n    mark(this, check, (mask & types[check]) === types[check])\n  }\n}\n\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvZGVmaW5lZC1pbmZvLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLFdBQVcsbUJBQU8sQ0FBQywwRUFBUTtBQUMzQixZQUFZLG1CQUFPLENBQUMsNEVBQVM7O0FBRTdCOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvZGVmaW5lZC1pbmZvLmpzPzBhMmMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBJbmZvID0gcmVxdWlyZSgnLi9pbmZvJylcbnZhciB0eXBlcyA9IHJlcXVpcmUoJy4vdHlwZXMnKVxuXG5tb2R1bGUuZXhwb3J0cyA9IERlZmluZWRJbmZvXG5cbkRlZmluZWRJbmZvLnByb3RvdHlwZSA9IG5ldyBJbmZvKClcbkRlZmluZWRJbmZvLnByb3RvdHlwZS5kZWZpbmVkID0gdHJ1ZVxuXG52YXIgY2hlY2tzID0gW1xuICAnYm9vbGVhbicsXG4gICdib29sZWFuaXNoJyxcbiAgJ292ZXJsb2FkZWRCb29sZWFuJyxcbiAgJ251bWJlcicsXG4gICdjb21tYVNlcGFyYXRlZCcsXG4gICdzcGFjZVNlcGFyYXRlZCcsXG4gICdjb21tYU9yU3BhY2VTZXBhcmF0ZWQnXG5dXG52YXIgY2hlY2tzTGVuZ3RoID0gY2hlY2tzLmxlbmd0aFxuXG5mdW5jdGlvbiBEZWZpbmVkSW5mbyhwcm9wZXJ0eSwgYXR0cmlidXRlLCBtYXNrLCBzcGFjZSkge1xuICB2YXIgaW5kZXggPSAtMVxuICB2YXIgY2hlY2tcblxuICBtYXJrKHRoaXMsICdzcGFjZScsIHNwYWNlKVxuXG4gIEluZm8uY2FsbCh0aGlzLCBwcm9wZXJ0eSwgYXR0cmlidXRlKVxuXG4gIHdoaWxlICgrK2luZGV4IDwgY2hlY2tzTGVuZ3RoKSB7XG4gICAgY2hlY2sgPSBjaGVja3NbaW5kZXhdXG4gICAgbWFyayh0aGlzLCBjaGVjaywgKG1hc2sgJiB0eXBlc1tjaGVja10pID09PSB0eXBlc1tjaGVja10pXG4gIH1cbn1cblxuZnVuY3Rpb24gbWFyayh2YWx1ZXMsIGtleSwgdmFsdWUpIHtcbiAgaWYgKHZhbHVlKSB7XG4gICAgdmFsdWVzW2tleV0gPSB2YWx1ZVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/defined-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/info.js":
/*!************************************************************!*\
  !*** ./node_modules/property-information/lib/util/info.js ***!
  \************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = Info\n\nvar proto = Info.prototype\n\nproto.space = null\nproto.attribute = null\nproto.property = null\nproto.boolean = false\nproto.booleanish = false\nproto.overloadedBoolean = false\nproto.number = false\nproto.commaSeparated = false\nproto.spaceSeparated = false\nproto.commaOrSpaceSeparated = false\nproto.mustUseProperty = false\nproto.defined = false\n\nfunction Info(property, attribute) {\n  this.property = property\n  this.attribute = attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvaW5mby5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2luZm8uanM/YWI0MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBJbmZvXG5cbnZhciBwcm90byA9IEluZm8ucHJvdG90eXBlXG5cbnByb3RvLnNwYWNlID0gbnVsbFxucHJvdG8uYXR0cmlidXRlID0gbnVsbFxucHJvdG8ucHJvcGVydHkgPSBudWxsXG5wcm90by5ib29sZWFuID0gZmFsc2VcbnByb3RvLmJvb2xlYW5pc2ggPSBmYWxzZVxucHJvdG8ub3ZlcmxvYWRlZEJvb2xlYW4gPSBmYWxzZVxucHJvdG8ubnVtYmVyID0gZmFsc2VcbnByb3RvLmNvbW1hU2VwYXJhdGVkID0gZmFsc2VcbnByb3RvLnNwYWNlU2VwYXJhdGVkID0gZmFsc2VcbnByb3RvLmNvbW1hT3JTcGFjZVNlcGFyYXRlZCA9IGZhbHNlXG5wcm90by5tdXN0VXNlUHJvcGVydHkgPSBmYWxzZVxucHJvdG8uZGVmaW5lZCA9IGZhbHNlXG5cbmZ1bmN0aW9uIEluZm8ocHJvcGVydHksIGF0dHJpYnV0ZSkge1xuICB0aGlzLnByb3BlcnR5ID0gcHJvcGVydHlcbiAgdGhpcy5hdHRyaWJ1dGUgPSBhdHRyaWJ1dGVcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/merge.js":
/*!*************************************************************!*\
  !*** ./node_modules/property-information/lib/util/merge.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar xtend = __webpack_require__(/*! xtend */ \"(ssr)/./node_modules/xtend/immutable.js\")\nvar Schema = __webpack_require__(/*! ./schema */ \"(ssr)/./node_modules/property-information/lib/util/schema.js\")\n\nmodule.exports = merge\n\nfunction merge(definitions) {\n  var length = definitions.length\n  var property = []\n  var normal = []\n  var index = -1\n  var info\n  var space\n\n  while (++index < length) {\n    info = definitions[index]\n    property.push(info.property)\n    normal.push(info.normal)\n    space = info.space\n  }\n\n  return new Schema(\n    xtend.apply(null, property),\n    xtend.apply(null, normal),\n    space\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvbWVyZ2UuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosWUFBWSxtQkFBTyxDQUFDLHNEQUFPO0FBQzNCLGFBQWEsbUJBQU8sQ0FBQyw4RUFBVTs7QUFFL0I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvbWVyZ2UuanM/ZWU0YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIHh0ZW5kID0gcmVxdWlyZSgneHRlbmQnKVxudmFyIFNjaGVtYSA9IHJlcXVpcmUoJy4vc2NoZW1hJylcblxubW9kdWxlLmV4cG9ydHMgPSBtZXJnZVxuXG5mdW5jdGlvbiBtZXJnZShkZWZpbml0aW9ucykge1xuICB2YXIgbGVuZ3RoID0gZGVmaW5pdGlvbnMubGVuZ3RoXG4gIHZhciBwcm9wZXJ0eSA9IFtdXG4gIHZhciBub3JtYWwgPSBbXVxuICB2YXIgaW5kZXggPSAtMVxuICB2YXIgaW5mb1xuICB2YXIgc3BhY2VcblxuICB3aGlsZSAoKytpbmRleCA8IGxlbmd0aCkge1xuICAgIGluZm8gPSBkZWZpbml0aW9uc1tpbmRleF1cbiAgICBwcm9wZXJ0eS5wdXNoKGluZm8ucHJvcGVydHkpXG4gICAgbm9ybWFsLnB1c2goaW5mby5ub3JtYWwpXG4gICAgc3BhY2UgPSBpbmZvLnNwYWNlXG4gIH1cblxuICByZXR1cm4gbmV3IFNjaGVtYShcbiAgICB4dGVuZC5hcHBseShudWxsLCBwcm9wZXJ0eSksXG4gICAgeHRlbmQuYXBwbHkobnVsbCwgbm9ybWFsKSxcbiAgICBzcGFjZVxuICApXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/schema.js":
/*!**************************************************************!*\
  !*** ./node_modules/property-information/lib/util/schema.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = Schema\n\nvar proto = Schema.prototype\n\nproto.space = null\nproto.normal = {}\nproto.property = {}\n\nfunction Schema(property, normal, space) {\n  this.property = property\n  this.normal = normal\n\n  if (space) {\n    this.space = space\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvc2NoZW1hLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL3NjaGVtYS5qcz9kYTE1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IFNjaGVtYVxuXG52YXIgcHJvdG8gPSBTY2hlbWEucHJvdG90eXBlXG5cbnByb3RvLnNwYWNlID0gbnVsbFxucHJvdG8ubm9ybWFsID0ge31cbnByb3RvLnByb3BlcnR5ID0ge31cblxuZnVuY3Rpb24gU2NoZW1hKHByb3BlcnR5LCBub3JtYWwsIHNwYWNlKSB7XG4gIHRoaXMucHJvcGVydHkgPSBwcm9wZXJ0eVxuICB0aGlzLm5vcm1hbCA9IG5vcm1hbFxuXG4gIGlmIChzcGFjZSkge1xuICAgIHRoaXMuc3BhY2UgPSBzcGFjZVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/schema.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/types.js":
/*!*************************************************************!*\
  !*** ./node_modules/property-information/lib/util/types.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nvar powers = 0\n\nexports.boolean = increment()\nexports.booleanish = increment()\nexports.overloadedBoolean = increment()\nexports.number = increment()\nexports.spaceSeparated = increment()\nexports.commaSeparated = increment()\nexports.commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return Math.pow(2, ++powers)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUEsZUFBZTtBQUNmLGtCQUFrQjtBQUNsQix5QkFBeUI7QUFDekIsY0FBYztBQUNkLHNCQUFzQjtBQUN0QixzQkFBc0I7QUFDdEIsNkJBQTZCOztBQUU3QjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL3R5cGVzLmpzPzQ2MGYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBwb3dlcnMgPSAwXG5cbmV4cG9ydHMuYm9vbGVhbiA9IGluY3JlbWVudCgpXG5leHBvcnRzLmJvb2xlYW5pc2ggPSBpbmNyZW1lbnQoKVxuZXhwb3J0cy5vdmVybG9hZGVkQm9vbGVhbiA9IGluY3JlbWVudCgpXG5leHBvcnRzLm51bWJlciA9IGluY3JlbWVudCgpXG5leHBvcnRzLnNwYWNlU2VwYXJhdGVkID0gaW5jcmVtZW50KClcbmV4cG9ydHMuY29tbWFTZXBhcmF0ZWQgPSBpbmNyZW1lbnQoKVxuZXhwb3J0cy5jb21tYU9yU3BhY2VTZXBhcmF0ZWQgPSBpbmNyZW1lbnQoKVxuXG5mdW5jdGlvbiBpbmNyZW1lbnQoKSB7XG4gIHJldHVybiBNYXRoLnBvdygyLCArK3Bvd2Vycylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xlink.js":
/*!********************************************************!*\
  !*** ./node_modules/property-information/lib/xlink.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/property-information/lib/util/create.js\")\n\nmodule.exports = create({\n  space: 'xlink',\n  transform: xlinkTransform,\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n})\n\nfunction xlinkTransform(_, prop) {\n  return 'xlink:' + prop.slice(5).toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3hsaW5rLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGFBQWEsbUJBQU8sQ0FBQyxtRkFBZTs7QUFFcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bGluay5qcz9mZDU5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgY3JlYXRlID0gcmVxdWlyZSgnLi91dGlsL2NyZWF0ZScpXG5cbm1vZHVsZS5leHBvcnRzID0gY3JlYXRlKHtcbiAgc3BhY2U6ICd4bGluaycsXG4gIHRyYW5zZm9ybTogeGxpbmtUcmFuc2Zvcm0sXG4gIHByb3BlcnRpZXM6IHtcbiAgICB4TGlua0FjdHVhdGU6IG51bGwsXG4gICAgeExpbmtBcmNSb2xlOiBudWxsLFxuICAgIHhMaW5rSHJlZjogbnVsbCxcbiAgICB4TGlua1JvbGU6IG51bGwsXG4gICAgeExpbmtTaG93OiBudWxsLFxuICAgIHhMaW5rVGl0bGU6IG51bGwsXG4gICAgeExpbmtUeXBlOiBudWxsXG4gIH1cbn0pXG5cbmZ1bmN0aW9uIHhsaW5rVHJhbnNmb3JtKF8sIHByb3ApIHtcbiAgcmV0dXJuICd4bGluazonICsgcHJvcC5zbGljZSg1KS50b0xvd2VyQ2FzZSgpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xlink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xml.js":
/*!******************************************************!*\
  !*** ./node_modules/property-information/lib/xml.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/property-information/lib/util/create.js\")\n\nmodule.exports = create({\n  space: 'xml',\n  transform: xmlTransform,\n  properties: {\n    xmlLang: null,\n    xmlBase: null,\n    xmlSpace: null\n  }\n})\n\nfunction xmlTransform(_, prop) {\n  return 'xml:' + prop.slice(3).toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixhQUFhLG1CQUFPLENBQUMsbUZBQWU7O0FBRXBDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbC5qcz84MWZmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgY3JlYXRlID0gcmVxdWlyZSgnLi91dGlsL2NyZWF0ZScpXG5cbm1vZHVsZS5leHBvcnRzID0gY3JlYXRlKHtcbiAgc3BhY2U6ICd4bWwnLFxuICB0cmFuc2Zvcm06IHhtbFRyYW5zZm9ybSxcbiAgcHJvcGVydGllczoge1xuICAgIHhtbExhbmc6IG51bGwsXG4gICAgeG1sQmFzZTogbnVsbCxcbiAgICB4bWxTcGFjZTogbnVsbFxuICB9XG59KVxuXG5mdW5jdGlvbiB4bWxUcmFuc2Zvcm0oXywgcHJvcCkge1xuICByZXR1cm4gJ3htbDonICsgcHJvcC5zbGljZSgzKS50b0xvd2VyQ2FzZSgpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xml.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xmlns.js":
/*!********************************************************!*\
  !*** ./node_modules/property-information/lib/xmlns.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/property-information/lib/util/create.js\")\nvar caseInsensitiveTransform = __webpack_require__(/*! ./util/case-insensitive-transform */ \"(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\")\n\nmodule.exports = create({\n  space: 'xmlns',\n  attributes: {\n    xmlnsxlink: 'xmlns:xlink'\n  },\n  transform: caseInsensitiveTransform,\n  properties: {\n    xmlns: null,\n    xmlnsXLink: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbG5zLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGFBQWEsbUJBQU8sQ0FBQyxtRkFBZTtBQUNwQywrQkFBK0IsbUJBQU8sQ0FBQywySEFBbUM7O0FBRTFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIveG1sbnMuanM/MzZhYyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIGNyZWF0ZSA9IHJlcXVpcmUoJy4vdXRpbC9jcmVhdGUnKVxudmFyIGNhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybSA9IHJlcXVpcmUoJy4vdXRpbC9jYXNlLWluc2Vuc2l0aXZlLXRyYW5zZm9ybScpXG5cbm1vZHVsZS5leHBvcnRzID0gY3JlYXRlKHtcbiAgc3BhY2U6ICd4bWxucycsXG4gIGF0dHJpYnV0ZXM6IHtcbiAgICB4bWxuc3hsaW5rOiAneG1sbnM6eGxpbmsnXG4gIH0sXG4gIHRyYW5zZm9ybTogY2FzZUluc2Vuc2l0aXZlVHJhbnNmb3JtLFxuICBwcm9wZXJ0aWVzOiB7XG4gICAgeG1sbnM6IG51bGwsXG4gICAgeG1sbnNYTGluazogbnVsbFxuICB9XG59KVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xmlns.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/normalize.js":
/*!********************************************************!*\
  !*** ./node_modules/property-information/normalize.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = normalize\n\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbm9ybWFsaXplLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbm9ybWFsaXplLmpzP2Y1MTgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gbm9ybWFsaXplXG5cbmZ1bmN0aW9uIG5vcm1hbGl6ZSh2YWx1ZSkge1xuICByZXR1cm4gdmFsdWUudG9Mb3dlckNhc2UoKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/normalize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/svg.js":
/*!**************************************************!*\
  !*** ./node_modules/property-information/svg.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar merge = __webpack_require__(/*! ./lib/util/merge */ \"(ssr)/./node_modules/property-information/lib/util/merge.js\")\nvar xlink = __webpack_require__(/*! ./lib/xlink */ \"(ssr)/./node_modules/property-information/lib/xlink.js\")\nvar xml = __webpack_require__(/*! ./lib/xml */ \"(ssr)/./node_modules/property-information/lib/xml.js\")\nvar xmlns = __webpack_require__(/*! ./lib/xmlns */ \"(ssr)/./node_modules/property-information/lib/xmlns.js\")\nvar aria = __webpack_require__(/*! ./lib/aria */ \"(ssr)/./node_modules/property-information/lib/aria.js\")\nvar svg = __webpack_require__(/*! ./lib/svg */ \"(ssr)/./node_modules/property-information/lib/svg.js\")\n\nmodule.exports = merge([xml, xlink, xmlns, aria, svg])\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vc3ZnLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLFlBQVksbUJBQU8sQ0FBQyxxRkFBa0I7QUFDdEMsWUFBWSxtQkFBTyxDQUFDLDJFQUFhO0FBQ2pDLFVBQVUsbUJBQU8sQ0FBQyx1RUFBVztBQUM3QixZQUFZLG1CQUFPLENBQUMsMkVBQWE7QUFDakMsV0FBVyxtQkFBTyxDQUFDLHlFQUFZO0FBQy9CLFVBQVUsbUJBQU8sQ0FBQyx1RUFBVzs7QUFFN0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL3N2Zy5qcz9lM2I0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgbWVyZ2UgPSByZXF1aXJlKCcuL2xpYi91dGlsL21lcmdlJylcbnZhciB4bGluayA9IHJlcXVpcmUoJy4vbGliL3hsaW5rJylcbnZhciB4bWwgPSByZXF1aXJlKCcuL2xpYi94bWwnKVxudmFyIHhtbG5zID0gcmVxdWlyZSgnLi9saWIveG1sbnMnKVxudmFyIGFyaWEgPSByZXF1aXJlKCcuL2xpYi9hcmlhJylcbnZhciBzdmcgPSByZXF1aXJlKCcuL2xpYi9zdmcnKVxuXG5tb2R1bGUuZXhwb3J0cyA9IG1lcmdlKFt4bWwsIHhsaW5rLCB4bWxucywgYXJpYSwgc3ZnXSlcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/svg.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/hast-to-react.json":
/*!**************************************************************!*\
  !*** ./node_modules/property-information/hast-to-react.json ***!
  \**************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"classId":"classID","dataType":"datatype","itemId":"itemID","strokeDashArray":"strokeDasharray","strokeDashOffset":"strokeDashoffset","strokeLineCap":"strokeLinecap","strokeLineJoin":"strokeLinejoin","strokeMiterLimit":"strokeMiterlimit","typeOf":"typeof","xLinkActuate":"xlinkActuate","xLinkArcRole":"xlinkArcrole","xLinkHref":"xlinkHref","xLinkRole":"xlinkRole","xLinkShow":"xlinkShow","xLinkTitle":"xlinkTitle","xLinkType":"xlinkType","xmlnsXLink":"xmlnsXlink"}');

/***/ })

};
;