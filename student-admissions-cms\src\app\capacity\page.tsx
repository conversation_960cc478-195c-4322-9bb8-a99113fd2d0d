'use client';

import {
  DataGrid,
  GridColDef,
  GridActionsCellItem,
  GridRowParams,
} from '@mui/x-data-grid';
import {
  Box,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Card,
  CardContent,
  CardHeader,
  LinearProgress,
  Chip,
  Alert,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Edit as EditIcon,
  Group as GroupIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  List as ListIcon,
} from '@mui/icons-material';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useForm, Controller } from 'react-hook-form';
import { useState } from 'react';
import { List, useDataGrid } from '@refinedev/mui';
import { useList, useUpdate } from '@refinedev/core';

interface CapacityFormData {
  capacity: number;
  notes: string;
}

const CapacityManagementPage: React.FC = () => {
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [waitlistDialogOpen, setWaitlistDialogOpen] = useState(false);
  const [selectedClass, setSelectedClass] = useState<any>(null);

  const { dataGridProps } = useDataGrid({
    resource: 'classes',
    initialSorter: [
      {
        field: 'program_id',
        order: 'asc',
      },
    ],
  });

  const { mutate: updateClass } = useUpdate();

  const { data: waitlistData } = useList({
    resource: 'waitlists',
    filters: [
      { field: 'is_active', operator: 'eq', value: true }
    ],
  });

  const { data: enrollmentStats } = useList({
    resource: 'enrollments',
    meta: {
      select: 'class_id, count(*) as enrollment_count',
      groupBy: 'class_id',
    },
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CapacityFormData>({
    defaultValues: {
      capacity: 0,
      notes: '',
    },
  });

  const getCapacityStatus = (current: number, capacity: number) => {
    const percentage = (current / capacity) * 100;
    if (percentage >= 100) return { status: 'full', color: 'error' as const };
    if (percentage >= 90) return { status: 'nearly_full', color: 'warning' as const };
    if (percentage >= 70) return { status: 'filling', color: 'info' as const };
    return { status: 'available', color: 'success' as const };
  };

  const getVacancies = (current: number, capacity: number) => {
    return Math.max(0, capacity - current);
  };

  const onCapacitySubmit = (data: CapacityFormData) => {
    updateClass(
      {
        resource: 'classes',
        id: selectedClass.id,
        values: {
          capacity: data.capacity,
          notes: data.notes,
        },
      },
      {
        onSuccess: () => {
          reset();
          setEditDialogOpen(false);
          setSelectedClass(null);
        },
      }
    );
  };

  const extendWaitlistOffers = (classId: string) => {
    // Logic to extend offers to waitlisted students
    console.log('Extending offers to waitlisted students for class:', classId);
  };

  const columns: GridColDef[] = [
    {
      field: 'program_name',
      headerName: 'Program',
      width: 200,
      valueGetter: ({ row }) => row.academic_programs?.name || 'N/A',
    },
    {
      field: 'class_name',
      headerName: 'Class',
      width: 150,
      renderCell: ({ value }) => (
        <Typography variant="body2" fontWeight="bold">
          {value}
        </Typography>
      ),
    },
    {
      field: 'level',
      headerName: 'Level',
      width: 100,
      renderCell: ({ value }) => (
        <Chip label={`Year ${value}`} size="small" variant="outlined" />
      ),
    },
    {
      field: 'capacity',
      headerName: 'Capacity',
      width: 100,
      renderCell: ({ value }) => (
        <Typography variant="body2" fontWeight="bold">
          {value}
        </Typography>
      ),
    },
    {
      field: 'current_enrollment',
      headerName: 'Enrolled',
      width: 100,
      renderCell: ({ value }) => (
        <Typography variant="body2" color="primary">
          {value || 0}
        </Typography>
      ),
    },
    {
      field: 'vacancies',
      headerName: 'Vacancies',
      width: 100,
      renderCell: ({ row }) => {
        const vacancies = getVacancies(row.current_enrollment || 0, row.capacity);
        return (
          <Typography 
            variant="body2" 
            color={vacancies > 0 ? 'success.main' : 'error.main'}
            fontWeight="bold"
          >
            {vacancies}
          </Typography>
        );
      },
    },
    {
      field: 'utilization',
      headerName: 'Utilization',
      width: 150,
      renderCell: ({ row }) => {
        const current = row.current_enrollment || 0;
        const capacity = row.capacity;
        const percentage = (current / capacity) * 100;
        const { color } = getCapacityStatus(current, capacity);
        
        return (
          <Box sx={{ width: '100%' }}>
            <LinearProgress
              variant="determinate"
              value={Math.min(percentage, 100)}
              color={color}
              sx={{ height: 8, borderRadius: 4, mb: 0.5 }}
            />
            <Typography variant="caption" color="text.secondary">
              {percentage.toFixed(1)}%
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'waitlist_count',
      headerName: 'Waitlist',
      width: 100,
      renderCell: ({ row }) => {
        const waitlistCount = waitlistData?.data?.filter(
          (w: any) => w.class_id === row.id && w.is_active
        ).length || 0;
        
        return (
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="body2">{waitlistCount}</Typography>
            {waitlistCount > 0 && (
              <Tooltip title="View Waitlist">
                <IconButton
                  size="small"
                  onClick={() => {
                    setSelectedClass(row);
                    setWaitlistDialogOpen(true);
                  }}
                >
                  <ListIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        );
      },
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: ({ row }) => {
        const current = row.current_enrollment || 0;
        const capacity = row.capacity;
        const { status, color } = getCapacityStatus(current, capacity);
        
        const statusLabels = {
          full: 'Full',
          nearly_full: 'Nearly Full',
          filling: 'Filling',
          available: 'Available',
        };
        
        return (
          <Chip
            label={statusLabels[status]}
            color={color}
            size="small"
          />
        );
      },
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 150,
      getActions: ({ row }: GridRowParams) => [
        <GridActionsCellItem
          key="edit"
          icon={<EditIcon />}
          label="Edit Capacity"
          onClick={() => {
            setSelectedClass(row);
            reset({
              capacity: row.capacity,
              notes: row.notes || '',
            });
            setEditDialogOpen(true);
          }}
        />,
        <GridActionsCellItem
          key="extend"
          icon={<TrendingUpIcon />}
          label="Extend Offers"
          onClick={() => extendWaitlistOffers(row.id)}
          disabled={getVacancies(row.current_enrollment || 0, row.capacity) === 0}
        />,
      ],
    },
  ];

  // Calculate summary statistics
  const totalCapacity = dataGridProps.rows?.reduce((sum: number, row: any) => sum + row.capacity, 0) || 0;
  const totalEnrolled = dataGridProps.rows?.reduce((sum: number, row: any) => sum + (row.current_enrollment || 0), 0) || 0;
  const totalVacancies = totalCapacity - totalEnrolled;
  const totalWaitlist = waitlistData?.data?.filter((w: any) => w.is_active).length || 0;

  return (
    <ProtectedRoute resource="classes" action="list">
      <Box>
        {/* Summary Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardHeader
                title="Total Capacity"
                avatar={<GroupIcon color="primary" />}
              />
              <CardContent>
                <Typography variant="h4" color="primary">
                  {totalCapacity.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Across all programs
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardHeader
                title="Current Enrollment"
                avatar={<CheckIcon color="success" />}
              />
              <CardContent>
                <Typography variant="h4" color="success.main">
                  {totalEnrolled.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {((totalEnrolled / totalCapacity) * 100).toFixed(1)}% utilization
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardHeader
                title="Available Vacancies"
                avatar={<TrendingUpIcon color="info" />}
              />
              <CardContent>
                <Typography variant="h4" color="info.main">
                  {totalVacancies.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Ready for new admissions
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardHeader
                title="Waitlisted Students"
                avatar={<WarningIcon color="warning" />}
              />
              <CardContent>
                <Typography variant="h4" color="warning.main">
                  {totalWaitlist.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Awaiting vacancy
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Capacity Overview */}
        <List title="Class Capacity Management">
          <DataGrid
            {...dataGridProps}
            columns={columns}
            autoHeight
            pageSizeOptions={[10, 25, 50]}
            disableRowSelectionOnClick
          />
        </List>

        {/* Edit Capacity Dialog */}
        <Dialog
          open={editDialogOpen}
          onClose={() => setEditDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Edit Class Capacity - {selectedClass?.class_name}
          </DialogTitle>
          <form onSubmit={handleSubmit(onCapacitySubmit)}>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Current enrollment: {selectedClass?.current_enrollment || 0} students
                  </Alert>
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="capacity"
                    control={control}
                    rules={{
                      required: 'Capacity is required',
                      min: {
                        value: selectedClass?.current_enrollment || 0,
                        message: `Capacity cannot be less than current enrollment (${selectedClass?.current_enrollment || 0})`,
                      },
                    }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Class Capacity"
                        type="number"
                        fullWidth
                        error={!!errors.capacity}
                        helperText={errors.capacity?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="notes"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Notes"
                        multiline
                        rows={3}
                        fullWidth
                        placeholder="Reason for capacity change, special considerations, etc."
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
              <Button type="submit" variant="contained">
                Update Capacity
              </Button>
            </DialogActions>
          </form>
        </Dialog>

        {/* Waitlist Dialog */}
        <Dialog
          open={waitlistDialogOpen}
          onClose={() => setWaitlistDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Waitlist - {selectedClass?.class_name}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body1" gutterBottom>
                <strong>Available Vacancies:</strong> {
                  selectedClass ? getVacancies(
                    selectedClass.current_enrollment || 0,
                    selectedClass.capacity
                  ) : 0
                }
              </Typography>
            </Box>
            
            {/* Waitlist would be displayed here */}
            <Alert severity="info">
              Waitlist management interface would be implemented here, showing:
              <ul>
                <li>List of waitlisted students in order</li>
                <li>Option to extend offers to next students in line</li>
                <li>Automatic offer extension when vacancies become available</li>
                <li>Waitlist position management</li>
              </ul>
            </Alert>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setWaitlistDialogOpen(false)}>Close</Button>
            <Button 
              variant="contained" 
              onClick={() => extendWaitlistOffers(selectedClass?.id)}
              disabled={!selectedClass || getVacancies(
                selectedClass?.current_enrollment || 0,
                selectedClass?.capacity || 0
              ) === 0}
            >
              Extend Offers to Waitlist
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ProtectedRoute>
  );
};

export default CapacityManagementPage;
