"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inquiries/page",{

/***/ "(app-pages-browser)/./src/app/inquiries/page.tsx":
/*!************************************!*\
  !*** ./src/app/inquiries/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _refinedev_mui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @refinedev/mui */ \"(app-pages-browser)/./node_modules/@refinedev/mui/dist/index.mjs\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/components/cell/GridActionsCellItem.js\");\n/* harmony import */ var _mui_x_data_grid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/x-data-grid */ \"(app-pages-browser)/./node_modules/@mui/x-data-grid/DataGrid/DataGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Delete,Edit,Email,PersonAdd,Phone,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Email.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Delete,Edit,Email,PersonAdd,Phone,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Phone.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Delete,Edit,Email,PersonAdd,Phone,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Visibility.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Delete,Edit,Email,PersonAdd,Phone,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Edit.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Delete,Edit,Email,PersonAdd,Phone,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/PersonAdd.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Delete,Edit,Email,PersonAdd,Phone,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Delete,Edit,Email,PersonAdd,Phone,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst InquiriesPage = ()=>{\n    _s();\n    const [createDialogOpen, setCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editDialogOpen, setEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedInquiry, setSelectedInquiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Mock data for inquiries\n    const mockInquiries = [\n        {\n            id: \"1\",\n            inquiry_number: \"INQ2024-001\",\n            full_name: \"John Doe\",\n            email: \"<EMAIL>\",\n            phone: \"******-0123\",\n            program_name: \"Computer Science\",\n            inquiry_source: \"Website\",\n            status: \"new\",\n            inquiry_date: \"2024-01-15\",\n            follow_up_date: \"2024-01-20\",\n            converted_to_application: false,\n            academic_programs: {\n                name: \"Bachelor of Computer Science\"\n            }\n        },\n        {\n            id: \"2\",\n            inquiry_number: \"INQ2024-002\",\n            full_name: \"Jane Smith\",\n            email: \"<EMAIL>\",\n            phone: \"******-0124\",\n            program_name: \"Business Administration\",\n            inquiry_source: \"Referral\",\n            status: \"contacted\",\n            inquiry_date: \"2024-01-16\",\n            follow_up_date: \"2024-01-22\",\n            converted_to_application: false,\n            academic_programs: {\n                name: \"Master of Business Administration\"\n            }\n        },\n        {\n            id: \"3\",\n            inquiry_number: \"INQ2024-003\",\n            full_name: \"Mike Johnson\",\n            email: \"<EMAIL>\",\n            phone: \"******-0125\",\n            program_name: \"Engineering\",\n            inquiry_source: \"Education Fair\",\n            status: \"converted\",\n            inquiry_date: \"2024-01-10\",\n            follow_up_date: null,\n            converted_to_application: true,\n            academic_programs: {\n                name: \"Bachelor of Engineering\"\n            }\n        }\n    ];\n    const dataGridProps = {\n        rows: mockInquiries,\n        loading: false,\n        rowCount: mockInquiries.length\n    };\n    const createInquiry = (data)=>{\n        console.log(\"Creating inquiry:\", data);\n    };\n    const updateInquiry = (data)=>{\n        console.log(\"Updating inquiry:\", data);\n    };\n    const deleteInquiry = (data)=>{\n        console.log(\"Deleting inquiry:\", data);\n    };\n    const getStatusColor = (status)=>{\n        const colors = {\n            new: \"info\",\n            contacted: \"primary\",\n            follow_up: \"warning\",\n            converted: \"success\",\n            closed: \"default\"\n        };\n        return colors[status] || \"default\";\n    };\n    const handleConvertToApplication = (inquiry)=>{\n        // Logic to convert inquiry to application\n        console.log(\"Converting inquiry to application:\", inquiry);\n    };\n    const columns = [\n        {\n            field: \"inquiry_number\",\n            headerName: \"Inquiry #\",\n            width: 120,\n            renderCell: (param)=>{\n                let { value } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"full_name\",\n            headerName: \"Full Name\",\n            width: 200,\n            renderCell: (param)=>{\n                let { value } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    variant: \"body2\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"email\",\n            headerName: \"Email\",\n            width: 200,\n            renderCell: (param)=>{\n                let { value } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            fontSize: \"small\",\n                            color: \"action\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            variant: \"body2\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"phone\",\n            headerName: \"Phone\",\n            width: 150,\n            renderCell: (param)=>{\n                let { value } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            fontSize: \"small\",\n                            color: \"action\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            variant: \"body2\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"program_name\",\n            headerName: \"Program\",\n            width: 180,\n            valueGetter: (param)=>{\n                let { row } = param;\n                var _row_academic_programs;\n                return ((_row_academic_programs = row.academic_programs) === null || _row_academic_programs === void 0 ? void 0 : _row_academic_programs.name) || \"Not specified\";\n            }\n        },\n        {\n            field: \"inquiry_source\",\n            headerName: \"Source\",\n            width: 120,\n            renderCell: (param)=>{\n                let { value } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    label: value,\n                    size: \"small\",\n                    variant: \"outlined\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"status\",\n            headerName: \"Status\",\n            width: 120,\n            renderCell: (param)=>{\n                let { value } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    label: value === null || value === void 0 ? void 0 : value.replace(\"_\", \" \").toUpperCase(),\n                    color: getStatusColor(value),\n                    size: \"small\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"inquiry_date\",\n            headerName: \"Date\",\n            width: 120,\n            type: \"date\",\n            valueGetter: (param)=>{\n                let { value } = param;\n                return new Date(value);\n            }\n        },\n        {\n            field: \"follow_up_date\",\n            headerName: \"Follow Up\",\n            width: 120,\n            type: \"date\",\n            valueGetter: (param)=>{\n                let { value } = param;\n                return value ? new Date(value) : null;\n            }\n        },\n        {\n            field: \"actions\",\n            type: \"actions\",\n            headerName: \"Actions\",\n            width: 200,\n            getActions: (param)=>{\n                let { row } = param;\n                return [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_data_grid__WEBPACK_IMPORTED_MODULE_8__.GridActionsCellItem, {\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 17\n                        }, void 0),\n                        label: \"View\",\n                        onClick: ()=>console.log(\"View inquiry:\", row)\n                    }, \"view\", false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_data_grid__WEBPACK_IMPORTED_MODULE_8__.GridActionsCellItem, {\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 17\n                        }, void 0),\n                        label: \"Edit\",\n                        onClick: ()=>{\n                            setSelectedInquiry(row);\n                            setEditDialogOpen(true);\n                        }\n                    }, \"edit\", false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_data_grid__WEBPACK_IMPORTED_MODULE_8__.GridActionsCellItem, {\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 17\n                        }, void 0),\n                        label: \"Convert to Application\",\n                        onClick: ()=>handleConvertToApplication(row),\n                        disabled: row.converted_to_application\n                    }, \"convert\", false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_data_grid__WEBPACK_IMPORTED_MODULE_8__.GridActionsCellItem, {\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 17\n                        }, void 0),\n                        label: \"Delete\",\n                        onClick: ()=>{\n                            if (confirm(\"Are you sure you want to delete this inquiry?\")) {\n                                deleteInquiry({\n                                    resource: \"inquiries\",\n                                    id: row.id\n                                });\n                            }\n                        }\n                    }, \"delete\", false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, undefined)\n                ];\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__.ProtectedRoute, {\n        resource: \"inquiries\",\n        action: \"list\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_mui__WEBPACK_IMPORTED_MODULE_13__.List, {\n            title: \"Inquiries Management\",\n            headerButtons: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_mui__WEBPACK_IMPORTED_MODULE_13__.CreateButton, {\n                onClick: ()=>setCreateDialogOpen(true),\n                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Delete_Edit_Email_PersonAdd_Phone_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 24\n                }, void 0),\n                children: \"New Inquiry\"\n            }, void 0, false, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                lineNumber: 260,\n                columnNumber: 11\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_data_grid__WEBPACK_IMPORTED_MODULE_15__.DataGrid, {\n                ...dataGridProps,\n                columns: columns,\n                autoHeight: true,\n                pageSizeOptions: [\n                    10,\n                    25,\n                    50\n                ],\n                disableRowSelectionOnClick: true\n            }, void 0, false, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\inquiries\\\\page.tsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InquiriesPage, \"fBZdtVE1PsX2+ADRoaieASWpnuA=\");\n_c = InquiriesPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InquiriesPage);\nvar _c;\n$RefreshReg$(_c, \"InquiriesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inquiries/page.tsx\n"));

/***/ })

});