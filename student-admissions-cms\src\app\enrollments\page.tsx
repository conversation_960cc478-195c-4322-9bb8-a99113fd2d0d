'use client';

import React, { useState } from 'react';
import { useList, useCreate, useUpdate } from '@refinedev/core';
import { List, useDataGrid, CreateButton } from '@refinedev/mui';
import {
  DataGrid,
  GridColDef,
  GridActionsCellItem,
  GridRowParams,
} from '@mui/x-data-grid';
import {
  Box,
  Chip,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Alert,
} from '@mui/material';
import {
  School as SchoolIcon,
  Assignment as OfferIcon,
  CheckCircle as AcceptIcon,
  Cancel as RejectIcon,
  Print as PrintIcon,
  Email as EmailIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useForm, Controller } from 'react-hook-form';

interface OfferFormData {
  application_id: string;
  offer_acceptance_deadline: Date | null;
  total_fees: number;
  special_conditions: string;
}

interface EnrollmentFormData {
  student_id: string;
  class_id: string;
  fees_paid: number;
  enrollment_date: Date | null;
}

const EnrollmentsPage: React.FC = () => {
  const [offerDialogOpen, setOfferDialogOpen] = useState(false);
  const [enrollDialogOpen, setEnrollDialogOpen] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState<any>(null);

  const { dataGridProps } = useDataGrid({
    resource: 'enrollments',
    initialSorter: [
      {
        field: 'enrollment_date',
        order: 'desc',
      },
    ],
  });

  const { mutate: updateApplication } = useUpdate();
  const { mutate: createEnrollment } = useCreate();

  const { data: offeredApplicationsData } = useList({
    resource: 'applications',
    filters: [
      { field: 'status', operator: 'eq', value: 'offered' }
    ],
  });

  const { data: acceptedApplicationsData } = useList({
    resource: 'applications',
    filters: [
      { field: 'status', operator: 'eq', value: 'accepted' }
    ],
  });

  const { data: classesData } = useList({
    resource: 'classes',
    filters: [
      { field: 'is_active', operator: 'eq', value: true }
    ],
  });

  const {
    control: offerControl,
    handleSubmit: handleOfferSubmit,
    reset: resetOffer,
    formState: { errors: offerErrors },
  } = useForm<OfferFormData>({
    defaultValues: {
      application_id: '',
      offer_acceptance_deadline: null,
      total_fees: 0,
      special_conditions: '',
    },
  });

  const {
    control: enrollControl,
    handleSubmit: handleEnrollSubmit,
    reset: resetEnroll,
    formState: { errors: enrollErrors },
  } = useForm<EnrollmentFormData>({
    defaultValues: {
      student_id: '',
      class_id: '',
      fees_paid: 0,
      enrollment_date: new Date(),
    },
  });

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      enrolled: 'success',
      deferred: 'warning',
      cancelled: 'error',
      graduated: 'info',
      transferred: 'secondary',
    };
    return colors[status] || 'default';
  };

  const generateOfferLetter = (application: any) => {
    // Generate offer letter PDF
    console.log('Generating offer letter for:', application);
    // Implementation would use jsPDF or similar library
  };

  const sendOfferEmail = (application: any) => {
    // Send offer letter via email
    console.log('Sending offer email to:', application);
  };

  const onOfferSubmit = (data: OfferFormData) => {
    updateApplication(
      {
        resource: 'applications',
        id: data.application_id,
        values: {
          status: 'offered',
          offer_letter_sent: true,
          offer_acceptance_deadline: data.offer_acceptance_deadline?.toISOString().split('T')[0],
          total_fees: data.total_fees,
        },
      },
      {
        onSuccess: () => {
          resetOffer();
          setOfferDialogOpen(false);
          // Generate and send offer letter
          const application = offeredApplicationsData?.data?.find(app => app.id === data.application_id);
          if (application) {
            generateOfferLetter(application);
            sendOfferEmail(application);
          }
        },
      }
    );
  };

  const onEnrollSubmit = (data: EnrollmentFormData) => {
    createEnrollment(
      {
        resource: 'enrollments',
        values: {
          ...data,
          application_id: selectedApplication.id,
          enrollment_date: data.enrollment_date?.toISOString().split('T')[0],
          status: 'enrolled',
        },
      },
      {
        onSuccess: () => {
          // Update application status
          updateApplication({
            resource: 'applications',
            id: selectedApplication.id,
            values: { status: 'enrolled', enrollment_confirmed: true },
          });
          
          resetEnroll();
          setEnrollDialogOpen(false);
          setSelectedApplication(null);
        },
      }
    );
  };

  const columns: GridColDef[] = [
    {
      field: 'student_id',
      headerName: 'Student ID',
      width: 120,
      renderCell: ({ value }) => (
        <Typography variant="body2" fontWeight="bold" color="primary">
          {value}
        </Typography>
      ),
    },
    {
      field: 'student_name',
      headerName: 'Student Name',
      width: 200,
      valueGetter: ({ row }) => 
        `${row.applications?.first_name} ${row.applications?.last_name}`,
    },
    {
      field: 'program_name',
      headerName: 'Program',
      width: 200,
      valueGetter: ({ row }) => row.classes?.academic_programs?.name || 'N/A',
    },
    {
      field: 'class_name',
      headerName: 'Class',
      width: 150,
      valueGetter: ({ row }) => row.classes?.class_name || 'N/A',
    },
    {
      field: 'enrollment_date',
      headerName: 'Enrollment Date',
      width: 150,
      type: 'date',
      valueGetter: ({ value }) => new Date(value),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: ({ value }) => (
        <Chip
          label={value?.toUpperCase()}
          color={getStatusColor(value)}
          size="small"
        />
      ),
    },
    {
      field: 'fees_paid',
      headerName: 'Fees Paid',
      width: 120,
      renderCell: ({ value, row }) => (
        <Box>
          <Typography variant="body2" fontWeight="bold">
            ${value?.toLocaleString() || 0}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            of ${row.total_fees?.toLocaleString() || 0}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'academic_year',
      headerName: 'Academic Year',
      width: 150,
      valueGetter: ({ row }) => row.academic_years?.year_name || 'N/A',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 150,
      getActions: ({ row }: GridRowParams) => [
        <GridActionsCellItem
          key="print"
          icon={<PrintIcon />}
          label="Print Certificate"
          onClick={() => console.log('Print enrollment certificate:', row)}
        />,
        <GridActionsCellItem
          key="edit"
          icon={<SchoolIcon />}
          label="Edit Enrollment"
          onClick={() => console.log('Edit enrollment:', row)}
        />,
      ],
    },
  ];

  return (
    <ProtectedRoute resource="enrollments" action="list">
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Box>
          {/* Quick Actions */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={4}>
              <Card>
                <CardHeader
                  title="Generate Offers"
                  subheader={`${offeredApplicationsData?.data?.length || 0} applications ready`}
                />
                <CardContent>
                  <Button
                    variant="contained"
                    startIcon={<OfferIcon />}
                    onClick={() => setOfferDialogOpen(true)}
                    fullWidth
                  >
                    Send Offer Letters
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card>
                <CardHeader
                  title="Process Enrollments"
                  subheader={`${acceptedApplicationsData?.data?.length || 0} offers accepted`}
                />
                <CardContent>
                  <Button
                    variant="contained"
                    startIcon={<SchoolIcon />}
                    onClick={() => setEnrollDialogOpen(true)}
                    fullWidth
                  >
                    Enroll Students
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card>
                <CardHeader
                  title="Current Enrollments"
                  subheader={`${dataGridProps.rowCount || 0} students enrolled`}
                />
                <CardContent>
                  <Button
                    variant="outlined"
                    startIcon={<PrintIcon />}
                    fullWidth
                  >
                    Generate Reports
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          <List title="Enrollment Management">
            <DataGrid
              {...dataGridProps}
              columns={columns}
              autoHeight
              pageSizeOptions={[10, 25, 50]}
              disableRowSelectionOnClick
            />
          </List>
        </Box>

        {/* Generate Offer Dialog */}
        <Dialog
          open={offerDialogOpen}
          onClose={() => setOfferDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Generate Offer Letter</DialogTitle>
          <form onSubmit={handleOfferSubmit(onOfferSubmit)}>
            <DialogContent>
              <Alert severity="info" sx={{ mb: 3 }}>
                Select an application to generate and send an offer letter. The offer will be automatically emailed to the applicant.
              </Alert>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Controller
                    name="application_id"
                    control={offerControl}
                    rules={{ required: 'Application selection is required' }}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!offerErrors.application_id}>
                        <InputLabel>Select Application</InputLabel>
                        <Select {...field} label="Select Application">
                          {offeredApplicationsData?.data?.map((app: any) => (
                            <MenuItem key={app.id} value={app.id}>
                              {app.application_number} - {app.first_name} {app.last_name} 
                              ({app.academic_programs?.name})
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="offer_acceptance_deadline"
                    control={offerControl}
                    rules={{ required: 'Acceptance deadline is required' }}
                    render={({ field }) => (
                      <DatePicker
                        {...field}
                        label="Acceptance Deadline"
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            error: !!offerErrors.offer_acceptance_deadline,
                            helperText: offerErrors.offer_acceptance_deadline?.message,
                          },
                        }}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="total_fees"
                    control={offerControl}
                    rules={{ required: 'Total fees is required', min: 0 }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Total Fees"
                        type="number"
                        fullWidth
                        error={!!offerErrors.total_fees}
                        helperText={offerErrors.total_fees?.message}
                        InputProps={{
                          startAdornment: <Typography>$</Typography>,
                        }}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="special_conditions"
                    control={offerControl}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Special Conditions"
                        multiline
                        rows={3}
                        fullWidth
                        placeholder="Any special conditions or requirements for the offer"
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOfferDialogOpen(false)}>Cancel</Button>
              <Button type="submit" variant="contained" startIcon={<EmailIcon />}>
                Generate & Send Offer
              </Button>
            </DialogActions>
          </form>
        </Dialog>

        {/* Enrollment Dialog */}
        <Dialog
          open={enrollDialogOpen}
          onClose={() => setEnrollDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Enroll Student</DialogTitle>
          <form onSubmit={handleEnrollSubmit(onEnrollSubmit)}>
            <DialogContent>
              <Alert severity="success" sx={{ mb: 3 }}>
                Process enrollment for students who have accepted their offers.
              </Alert>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Select Accepted Application</InputLabel>
                    <Select
                      value={selectedApplication?.id || ''}
                      onChange={(e) => {
                        const app = acceptedApplicationsData?.data?.find(a => a.id === e.target.value);
                        setSelectedApplication(app);
                      }}
                      label="Select Accepted Application"
                    >
                      {acceptedApplicationsData?.data?.map((app: any) => (
                        <MenuItem key={app.id} value={app.id}>
                          {app.application_number} - {app.first_name} {app.last_name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                {selectedApplication && (
                  <>
                    <Grid item xs={12}>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant="h6" gutterBottom>
                        Student Information
                      </Typography>
                      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                        <Typography><strong>Name:</strong> {selectedApplication.first_name} {selectedApplication.last_name}</Typography>
                        <Typography><strong>Email:</strong> {selectedApplication.email}</Typography>
                        <Typography><strong>Program:</strong> {selectedApplication.academic_programs?.name}</Typography>
                        <Typography><strong>Application #:</strong> {selectedApplication.application_number}</Typography>
                      </Box>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Controller
                        name="student_id"
                        control={enrollControl}
                        rules={{ required: 'Student ID is required' }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Student ID"
                            fullWidth
                            error={!!enrollErrors.student_id}
                            helperText={enrollErrors.student_id?.message}
                            placeholder="Auto-generated if left empty"
                          />
                        )}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Controller
                        name="enrollment_date"
                        control={enrollControl}
                        rules={{ required: 'Enrollment date is required' }}
                        render={({ field }) => (
                          <DatePicker
                            {...field}
                            label="Enrollment Date"
                            slotProps={{
                              textField: {
                                fullWidth: true,
                                error: !!enrollErrors.enrollment_date,
                                helperText: enrollErrors.enrollment_date?.message,
                              },
                            }}
                          />
                        )}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <Controller
                        name="class_id"
                        control={enrollControl}
                        rules={{ required: 'Class selection is required' }}
                        render={({ field }) => (
                          <FormControl fullWidth error={!!enrollErrors.class_id}>
                            <InputLabel>Assign to Class</InputLabel>
                            <Select {...field} label="Assign to Class">
                              {classesData?.data
                                ?.filter((cls: any) => cls.program_id === selectedApplication.program_id)
                                ?.map((cls: any) => (
                                <MenuItem key={cls.id} value={cls.id}>
                                  {cls.class_name} - Level {cls.level} 
                                  ({cls.current_enrollment}/{cls.capacity} students)
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Controller
                        name="fees_paid"
                        control={enrollControl}
                        rules={{ required: 'Fees paid amount is required', min: 0 }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Fees Paid"
                            type="number"
                            fullWidth
                            error={!!enrollErrors.fees_paid}
                            helperText={enrollErrors.fees_paid?.message}
                            InputProps={{
                              startAdornment: <Typography>$</Typography>,
                            }}
                          />
                        )}
                      />
                    </Grid>
                  </>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setEnrollDialogOpen(false)}>Cancel</Button>
              <Button 
                type="submit" 
                variant="contained" 
                startIcon={<SchoolIcon />}
                disabled={!selectedApplication}
              >
                Enroll Student
              </Button>
            </DialogActions>
          </form>
        </Dialog>
      </LocalizationProvider>
    </ProtectedRoute>
  );
};

export default EnrollmentsPage;
