'use client';

import React from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { useCreate, useUpdate, useList } from '@refinedev/core';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Box,
  Typography,
  Autocomplete,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

interface InquiryFormProps {
  open: boolean;
  onClose: () => void;
  inquiry?: any;
  mode: 'create' | 'edit';
}

interface InquiryFormData {
  full_name: string;
  email: string;
  phone: string;
  program_id: string;
  inquiry_source: string;
  status: string;
  notes: string;
  follow_up_date: Date | null;
}

const INQUIRY_SOURCES = [
  'Website',
  'Phone Call',
  'Walk-in',
  'Referral',
  'Social Media',
  'Advertisement',
  'Education Fair',
  'Other',
];

const INQUIRY_STATUSES = [
  { value: 'new', label: 'New' },
  { value: 'contacted', label: 'Contacted' },
  { value: 'follow_up', label: 'Follow Up' },
  { value: 'converted', label: 'Converted' },
  { value: 'closed', label: 'Closed' },
];

export const InquiryForm: React.FC<InquiryFormProps> = ({
  open,
  onClose,
  inquiry,
  mode,
}) => {
  const { mutate: createInquiry, isLoading: createLoading } = useCreate();
  const { mutate: updateInquiry, isLoading: updateLoading } = useUpdate();

  const { data: programsData } = useList({
    resource: 'academic_programs',
    filters: [{ field: 'is_active', operator: 'eq', value: true }],
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<InquiryFormData>({
    defaultValues: {
      full_name: inquiry?.full_name || '',
      email: inquiry?.email || '',
      phone: inquiry?.phone || '',
      program_id: inquiry?.program_id || '',
      inquiry_source: inquiry?.inquiry_source || '',
      status: inquiry?.status || 'new',
      notes: inquiry?.notes || '',
      follow_up_date: inquiry?.follow_up_date ? new Date(inquiry.follow_up_date) : null,
    },
  });

  const onSubmit = (data: InquiryFormData) => {
    const formattedData = {
      ...data,
      follow_up_date: data.follow_up_date?.toISOString().split('T')[0] || null,
    };

    if (mode === 'create') {
      createInquiry(
        {
          resource: 'inquiries',
          values: {
            ...formattedData,
            inquiry_number: `INQ${Date.now()}`, // Generate inquiry number
            inquiry_date: new Date().toISOString().split('T')[0],
          },
        },
        {
          onSuccess: () => {
            reset();
            onClose();
          },
        }
      );
    } else {
      updateInquiry(
        {
          resource: 'inquiries',
          id: inquiry.id,
          values: formattedData,
        },
        {
          onSuccess: () => {
            onClose();
          },
        }
      );
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {mode === 'create' ? 'Create New Inquiry' : 'Edit Inquiry'}
        </DialogTitle>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Controller
                  name="full_name"
                  control={control}
                  rules={{ required: 'Full name is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Full Name"
                      fullWidth
                      error={!!errors.full_name}
                      helperText={errors.full_name?.message}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Controller
                  name="email"
                  control={control}
                  rules={{
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Invalid email address',
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Email"
                      type="email"
                      fullWidth
                      error={!!errors.email}
                      helperText={errors.email?.message}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Controller
                  name="phone"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Phone Number"
                      fullWidth
                      error={!!errors.phone}
                      helperText={errors.phone?.message}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Controller
                  name="program_id"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Program of Interest</InputLabel>
                      <Select {...field} label="Program of Interest">
                        <MenuItem value="">
                          <em>Not specified</em>
                        </MenuItem>
                        {programsData?.data?.map((program: any) => (
                          <MenuItem key={program.id} value={program.id}>
                            {program.name} ({program.code})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Controller
                  name="inquiry_source"
                  control={control}
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      options={INQUIRY_SOURCES}
                      freeSolo
                      onChange={(_, value) => field.onChange(value)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Inquiry Source"
                          fullWidth
                        />
                      )}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select {...field} label="Status">
                        {INQUIRY_STATUSES.map((status) => (
                          <MenuItem key={status.value} value={status.value}>
                            {status.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Controller
                  name="follow_up_date"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      {...field}
                      label="Follow Up Date"
                      slotProps={{
                        textField: {
                          fullWidth: true,
                        },
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Notes"
                      multiline
                      rows={4}
                      fullWidth
                    />
                  )}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose}>Cancel</Button>
            <Button
              type="submit"
              variant="contained"
              disabled={createLoading || updateLoading}
            >
              {mode === 'create' ? 'Create' : 'Update'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </LocalizationProvider>
  );
};
