/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm";
exports.ids = ["vendor-chunks/mdast-util-gfm"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm/from-markdown.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-gfm/from-markdown.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var autolinkLiteral = __webpack_require__(/*! mdast-util-gfm-autolink-literal/from-markdown */ \"(ssr)/./node_modules/mdast-util-gfm-autolink-literal/from-markdown.js\")\nvar strikethrough = __webpack_require__(/*! mdast-util-gfm-strikethrough/from-markdown */ \"(ssr)/./node_modules/mdast-util-gfm-strikethrough/from-markdown.js\")\nvar table = __webpack_require__(/*! mdast-util-gfm-table/from-markdown */ \"(ssr)/./node_modules/mdast-util-gfm-table/from-markdown.js\")\nvar taskListItem = __webpack_require__(/*! mdast-util-gfm-task-list-item/from-markdown */ \"(ssr)/./node_modules/mdast-util-gfm-task-list-item/from-markdown.js\")\n\nvar own = {}.hasOwnProperty\n\nmodule.exports = configure([\n  autolinkLiteral,\n  strikethrough,\n  table,\n  taskListItem\n])\n\nfunction configure(extensions) {\n  var config = {transforms: [], canContainEols: []}\n  var length = extensions.length\n  var index = -1\n\n  while (++index < length) {\n    extension(config, extensions[index])\n  }\n\n  return config\n}\n\nfunction extension(config, extension) {\n  var key\n  var left\n  var right\n\n  for (key in extension) {\n    left = own.call(config, key) ? config[key] : (config[key] = {})\n    right = extension[key]\n\n    if (key === 'canContainEols' || key === 'transforms') {\n      config[key] = [].concat(left, right)\n    } else {\n      Object.assign(left, right)\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1nZm0vZnJvbS1tYXJrZG93bi5qcyIsIm1hcHBpbmdzIjoiQUFBQSxzQkFBc0IsbUJBQU8sQ0FBQyw0SEFBK0M7QUFDN0Usb0JBQW9CLG1CQUFPLENBQUMsc0hBQTRDO0FBQ3hFLFlBQVksbUJBQU8sQ0FBQyxzR0FBb0M7QUFDeEQsbUJBQW1CLG1CQUFPLENBQUMsd0hBQTZDOztBQUV4RSxZQUFZOztBQUVaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0VBQWtFO0FBQ2xFOztBQUVBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtZ2ZtL2Zyb20tbWFya2Rvd24uanM/MGExYyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgYXV0b2xpbmtMaXRlcmFsID0gcmVxdWlyZSgnbWRhc3QtdXRpbC1nZm0tYXV0b2xpbmstbGl0ZXJhbC9mcm9tLW1hcmtkb3duJylcbnZhciBzdHJpa2V0aHJvdWdoID0gcmVxdWlyZSgnbWRhc3QtdXRpbC1nZm0tc3RyaWtldGhyb3VnaC9mcm9tLW1hcmtkb3duJylcbnZhciB0YWJsZSA9IHJlcXVpcmUoJ21kYXN0LXV0aWwtZ2ZtLXRhYmxlL2Zyb20tbWFya2Rvd24nKVxudmFyIHRhc2tMaXN0SXRlbSA9IHJlcXVpcmUoJ21kYXN0LXV0aWwtZ2ZtLXRhc2stbGlzdC1pdGVtL2Zyb20tbWFya2Rvd24nKVxuXG52YXIgb3duID0ge30uaGFzT3duUHJvcGVydHlcblxubW9kdWxlLmV4cG9ydHMgPSBjb25maWd1cmUoW1xuICBhdXRvbGlua0xpdGVyYWwsXG4gIHN0cmlrZXRocm91Z2gsXG4gIHRhYmxlLFxuICB0YXNrTGlzdEl0ZW1cbl0pXG5cbmZ1bmN0aW9uIGNvbmZpZ3VyZShleHRlbnNpb25zKSB7XG4gIHZhciBjb25maWcgPSB7dHJhbnNmb3JtczogW10sIGNhbkNvbnRhaW5Fb2xzOiBbXX1cbiAgdmFyIGxlbmd0aCA9IGV4dGVuc2lvbnMubGVuZ3RoXG4gIHZhciBpbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsraW5kZXggPCBsZW5ndGgpIHtcbiAgICBleHRlbnNpb24oY29uZmlnLCBleHRlbnNpb25zW2luZGV4XSlcbiAgfVxuXG4gIHJldHVybiBjb25maWdcbn1cblxuZnVuY3Rpb24gZXh0ZW5zaW9uKGNvbmZpZywgZXh0ZW5zaW9uKSB7XG4gIHZhciBrZXlcbiAgdmFyIGxlZnRcbiAgdmFyIHJpZ2h0XG5cbiAgZm9yIChrZXkgaW4gZXh0ZW5zaW9uKSB7XG4gICAgbGVmdCA9IG93bi5jYWxsKGNvbmZpZywga2V5KSA/IGNvbmZpZ1trZXldIDogKGNvbmZpZ1trZXldID0ge30pXG4gICAgcmlnaHQgPSBleHRlbnNpb25ba2V5XVxuXG4gICAgaWYgKGtleSA9PT0gJ2NhbkNvbnRhaW5Fb2xzJyB8fCBrZXkgPT09ICd0cmFuc2Zvcm1zJykge1xuICAgICAgY29uZmlnW2tleV0gPSBbXS5jb25jYXQobGVmdCwgcmlnaHQpXG4gICAgfSBlbHNlIHtcbiAgICAgIE9iamVjdC5hc3NpZ24obGVmdCwgcmlnaHQpXG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/from-markdown.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm/to-markdown.js":
/*!****************************************************!*\
  !*** ./node_modules/mdast-util-gfm/to-markdown.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var autolinkLiteral = __webpack_require__(/*! mdast-util-gfm-autolink-literal/to-markdown */ \"(ssr)/./node_modules/mdast-util-gfm-autolink-literal/to-markdown.js\")\nvar strikethrough = __webpack_require__(/*! mdast-util-gfm-strikethrough/to-markdown */ \"(ssr)/./node_modules/mdast-util-gfm-strikethrough/to-markdown.js\")\nvar table = __webpack_require__(/*! mdast-util-gfm-table/to-markdown */ \"(ssr)/./node_modules/mdast-util-gfm-table/to-markdown.js\")\nvar taskListItem = __webpack_require__(/*! mdast-util-gfm-task-list-item/to-markdown */ \"(ssr)/./node_modules/mdast-util-gfm-task-list-item/to-markdown.js\")\nvar configure = __webpack_require__(/*! mdast-util-to-markdown/lib/configure */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/configure.js\")\n\nmodule.exports = toMarkdown\n\nfunction toMarkdown(options) {\n  var config = configure(\n    {handlers: {}, join: [], unsafe: [], options: {}},\n    {\n      extensions: [autolinkLiteral, strikethrough, table(options), taskListItem]\n    }\n  )\n\n  return Object.assign(config.options, {\n    handlers: config.handlers,\n    join: config.join,\n    unsafe: config.unsafe\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1nZm0vdG8tbWFya2Rvd24uanMiLCJtYXBwaW5ncyI6IkFBQUEsc0JBQXNCLG1CQUFPLENBQUMsd0hBQTZDO0FBQzNFLG9CQUFvQixtQkFBTyxDQUFDLGtIQUEwQztBQUN0RSxZQUFZLG1CQUFPLENBQUMsa0dBQWtDO0FBQ3RELG1CQUFtQixtQkFBTyxDQUFDLG9IQUEyQztBQUN0RSxnQkFBZ0IsbUJBQU8sQ0FBQywwR0FBc0M7O0FBRTlEOztBQUVBO0FBQ0E7QUFDQSxLQUFLLFlBQVksb0NBQW9DO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLWdmbS90by1tYXJrZG93bi5qcz8yMTU3Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBhdXRvbGlua0xpdGVyYWwgPSByZXF1aXJlKCdtZGFzdC11dGlsLWdmbS1hdXRvbGluay1saXRlcmFsL3RvLW1hcmtkb3duJylcbnZhciBzdHJpa2V0aHJvdWdoID0gcmVxdWlyZSgnbWRhc3QtdXRpbC1nZm0tc3RyaWtldGhyb3VnaC90by1tYXJrZG93bicpXG52YXIgdGFibGUgPSByZXF1aXJlKCdtZGFzdC11dGlsLWdmbS10YWJsZS90by1tYXJrZG93bicpXG52YXIgdGFza0xpc3RJdGVtID0gcmVxdWlyZSgnbWRhc3QtdXRpbC1nZm0tdGFzay1saXN0LWl0ZW0vdG8tbWFya2Rvd24nKVxudmFyIGNvbmZpZ3VyZSA9IHJlcXVpcmUoJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2NvbmZpZ3VyZScpXG5cbm1vZHVsZS5leHBvcnRzID0gdG9NYXJrZG93blxuXG5mdW5jdGlvbiB0b01hcmtkb3duKG9wdGlvbnMpIHtcbiAgdmFyIGNvbmZpZyA9IGNvbmZpZ3VyZShcbiAgICB7aGFuZGxlcnM6IHt9LCBqb2luOiBbXSwgdW5zYWZlOiBbXSwgb3B0aW9uczoge319LFxuICAgIHtcbiAgICAgIGV4dGVuc2lvbnM6IFthdXRvbGlua0xpdGVyYWwsIHN0cmlrZXRocm91Z2gsIHRhYmxlKG9wdGlvbnMpLCB0YXNrTGlzdEl0ZW1dXG4gICAgfVxuICApXG5cbiAgcmV0dXJuIE9iamVjdC5hc3NpZ24oY29uZmlnLm9wdGlvbnMsIHtcbiAgICBoYW5kbGVyczogY29uZmlnLmhhbmRsZXJzLFxuICAgIGpvaW46IGNvbmZpZy5qb2luLFxuICAgIHVuc2FmZTogY29uZmlnLnVuc2FmZVxuICB9KVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/to-markdown.js\n");

/***/ })

};
;