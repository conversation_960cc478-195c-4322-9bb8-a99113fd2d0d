'use client';

import React, { useState } from 'react';
import { useList, useCreate, useUpdate, useDelete } from '@refinedev/core';
import { List, useDataGrid, CreateButton } from '@refinedev/mui';
import {
  DataGrid,
  GridColDef,
  GridActionsCellItem,
  GridRowParams,
} from '@mui/x-data-grid';
import {
  Box,
  Chip,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Alert,
  Tabs,
  Tab,
  Paper,
  List as MuiList,
  ListItem,
  ListItemText,
  ListItemIcon,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Email as EmailIcon,
  Sms as SmsIcon,
  WhatsApp as WhatsAppIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Preview as PreviewIcon,
  Send as SendIcon,
  Code as CodeIcon,
} from '@mui/icons-material';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useF<PERSON>, Controller } from 'react-hook-form';
import {
  TEMPLATE_VARIABLES,
  DEFAULT_TEMPLATES,
  processTemplate,
  validateTemplate,
  sendBatchNotifications,
} from '@/utils/notifications';

interface TemplateFormData {
  name: string;
  type: 'email' | 'sms' | 'whatsapp' | 'in_app';
  subject: string;
  content: string;
  is_active: boolean;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const CommunicationsPage: React.FC = () => {
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [sendDialogOpen, setSendDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [editMode, setEditMode] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  const { dataGridProps } = useDataGrid({
    resource: 'communication_templates',
    initialSorter: [
      {
        field: 'created_at',
        order: 'desc',
      },
    ],
  });

  const { mutate: createTemplate } = useCreate();
  const { mutate: updateTemplate } = useUpdate();
  const { mutate: deleteTemplate } = useDelete();

  const { data: notificationsData } = useList({
    resource: 'notifications',
    pagination: { pageSize: 100 },
    sorters: [{ field: 'created_at', order: 'desc' }],
  });

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<TemplateFormData>({
    defaultValues: {
      name: '',
      type: 'email',
      subject: '',
      content: '',
      is_active: true,
    },
  });

  const watchedContent = watch('content');
  const watchedSubject = watch('subject');
  const watchedType = watch('type');

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <EmailIcon />;
      case 'sms':
        return <SmsIcon />;
      case 'whatsapp':
        return <WhatsAppIcon />;
      default:
        return <EmailIcon />;
    }
  };

  const getTypeColor = (type: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      email: 'primary',
      sms: 'success',
      whatsapp: 'info',
      in_app: 'secondary',
    };
    return colors[type] || 'default';
  };

  const onTemplateSubmit = (data: TemplateFormData) => {
    const templateValidation = validateTemplate(data.content);
    
    if (!templateValidation.isValid) {
      alert('Template validation failed: ' + templateValidation.errors.join(', '));
      return;
    }

    const templateData = {
      ...data,
      variables: templateValidation.variables,
    };

    if (editMode && selectedTemplate) {
      updateTemplate(
        {
          resource: 'communication_templates',
          id: selectedTemplate.id,
          values: templateData,
        },
        {
          onSuccess: () => {
            reset();
            setTemplateDialogOpen(false);
            setEditMode(false);
            setSelectedTemplate(null);
          },
        }
      );
    } else {
      createTemplate(
        {
          resource: 'communication_templates',
          values: templateData,
        },
        {
          onSuccess: () => {
            reset();
            setTemplateDialogOpen(false);
          },
        }
      );
    }
  };

  const handlePreview = (template: any) => {
    setSelectedTemplate(template);
    setPreviewDialogOpen(true);
  };

  const handleEdit = (template: any) => {
    setSelectedTemplate(template);
    setEditMode(true);
    reset({
      name: template.name,
      type: template.type,
      subject: template.subject || '',
      content: template.content,
      is_active: template.is_active,
    });
    setTemplateDialogOpen(true);
  };

  const insertVariable = (variable: string) => {
    const currentContent = watchedContent || '';
    const newContent = currentContent + `{{${variable}}}`;
    reset({
      ...watch(),
      content: newContent,
    });
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Template Name',
      width: 200,
      renderCell: ({ value }) => (
        <Typography variant="body2" fontWeight="bold">
          {value}
        </Typography>
      ),
    },
    {
      field: 'type',
      headerName: 'Type',
      width: 120,
      renderCell: ({ value }) => (
        <Chip
          icon={getTypeIcon(value)}
          label={value?.toUpperCase()}
          color={getTypeColor(value)}
          size="small"
        />
      ),
    },
    {
      field: 'subject',
      headerName: 'Subject',
      width: 250,
      renderCell: ({ value, row }) => (
        <Typography variant="body2" noWrap>
          {row.type === 'email' ? value : 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'variables',
      headerName: 'Variables',
      width: 150,
      renderCell: ({ value }) => (
        <Typography variant="body2" color="text.secondary">
          {value?.length || 0} variables
        </Typography>
      ),
    },
    {
      field: 'is_active',
      headerName: 'Status',
      width: 100,
      renderCell: ({ value }) => (
        <Chip
          label={value ? 'Active' : 'Inactive'}
          color={value ? 'success' : 'default'}
          size="small"
        />
      ),
    },
    {
      field: 'created_at',
      headerName: 'Created',
      width: 150,
      type: 'date',
      valueGetter: ({ value }) => new Date(value),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 200,
      getActions: ({ row }: GridRowParams) => [
        <GridActionsCellItem
          key="preview"
          icon={<PreviewIcon />}
          label="Preview"
          onClick={() => handlePreview(row)}
        />,
        <GridActionsCellItem
          key="edit"
          icon={<EditIcon />}
          label="Edit"
          onClick={() => handleEdit(row)}
        />,
        <GridActionsCellItem
          key="delete"
          icon={<DeleteIcon />}
          label="Delete"
          onClick={() => {
            if (confirm('Are you sure you want to delete this template?')) {
              deleteTemplate({
                resource: 'communication_templates',
                id: row.id,
              });
            }
          }}
        />,
      ],
    },
  ];

  return (
    <ProtectedRoute resource="communications" action="list">
      <Box>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)} sx={{ mb: 3 }}>
          <Tab label="Templates" />
          <Tab label="Notification History" />
          <Tab label="Send Notifications" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <List
            title="Communication Templates"
            headerButtons={
              <CreateButton
                onClick={() => {
                  setEditMode(false);
                  setSelectedTemplate(null);
                  reset({
                    name: '',
                    type: 'email',
                    subject: '',
                    content: '',
                    is_active: true,
                  });
                  setTemplateDialogOpen(true);
                }}
              >
                New Template
              </CreateButton>
            }
          >
            <DataGrid
              {...dataGridProps}
              columns={columns}
              autoHeight
              pageSizeOptions={[10, 25, 50]}
              disableRowSelectionOnClick
            />
          </List>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Card>
            <CardHeader title="Recent Notifications" />
            <CardContent>
              <Alert severity="info">
                Notification history would be displayed here, showing:
                <ul>
                  <li>Sent notifications with status</li>
                  <li>Delivery confirmations</li>
                  <li>Failed notifications with reasons</li>
                  <li>Recipient engagement metrics</li>
                </ul>
              </Alert>
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Card>
            <CardHeader title="Send Bulk Notifications" />
            <CardContent>
              <Alert severity="info">
                Bulk notification interface would be implemented here, allowing:
                <ul>
                  <li>Select recipient groups (all applicants, specific status, etc.)</li>
                  <li>Choose template or create custom message</li>
                  <li>Preview before sending</li>
                  <li>Schedule notifications</li>
                </ul>
              </Alert>
            </CardContent>
          </Card>
        </TabPanel>

        {/* Template Editor Dialog */}
        <Dialog
          open={templateDialogOpen}
          onClose={() => setTemplateDialogOpen(false)}
          maxWidth="lg"
          fullWidth
        >
          <DialogTitle>
            {editMode ? 'Edit Template' : 'Create New Template'}
          </DialogTitle>
          <form onSubmit={handleSubmit(onTemplateSubmit)}>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="name"
                    control={control}
                    rules={{ required: 'Template name is required' }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Template Name"
                        fullWidth
                        error={!!errors.name}
                        helperText={errors.name?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="type"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth>
                        <InputLabel>Type</InputLabel>
                        <Select {...field} label="Type">
                          <MenuItem value="email">Email</MenuItem>
                          <MenuItem value="sms">SMS</MenuItem>
                          <MenuItem value="whatsapp">WhatsApp</MenuItem>
                          <MenuItem value="in_app">In-App</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>
                
                {watchedType === 'email' && (
                  <Grid item xs={12}>
                    <Controller
                      name="subject"
                      control={control}
                      rules={{ required: 'Subject is required for email templates' }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Subject"
                          fullWidth
                          error={!!errors.subject}
                          helperText={errors.subject?.message}
                        />
                      )}
                    />
                  </Grid>
                )}
                
                <Grid item xs={12} md={8}>
                  <Controller
                    name="content"
                    control={control}
                    rules={{ required: 'Content is required' }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Content"
                        multiline
                        rows={12}
                        fullWidth
                        error={!!errors.content}
                        helperText={errors.content?.message}
                        placeholder="Enter your template content. Use {{variable_name}} for dynamic content."
                      />
                    )}
                  />
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, height: '100%' }}>
                    <Typography variant="h6" gutterBottom>
                      <CodeIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Available Variables
                    </Typography>
                    <MuiList dense>
                      {TEMPLATE_VARIABLES.map((variable) => (
                        <ListItem
                          key={variable.key}
                          button
                          onClick={() => insertVariable(variable.key)}
                        >
                          <ListItemIcon>
                            <CodeIcon fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary={variable.label}
                            secondary={`{{${variable.key}}}`}
                          />
                        </ListItem>
                      ))}
                    </MuiList>
                  </Paper>
                </Grid>
                
                <Grid item xs={12}>
                  <Controller
                    name="is_active"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Switch
                            checked={field.value}
                            onChange={field.onChange}
                          />
                        }
                        label="Active Template"
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setTemplateDialogOpen(false)}>Cancel</Button>
              <Button
                onClick={() => {
                  const sampleData = {
                    applicant_name: 'John Doe',
                    application_number: 'APP2024-001234',
                    program_name: 'Bachelor of Computer Science',
                    interview_date: 'March 15, 2024',
                    interview_time: '10:00 AM',
                  };
                  const preview = processTemplate(watchedContent || '', sampleData);
                  alert(`Preview:\n\n${preview}`);
                }}
                variant="outlined"
              >
                Preview
              </Button>
              <Button type="submit" variant="contained">
                {editMode ? 'Update' : 'Create'} Template
              </Button>
            </DialogActions>
          </form>
        </Dialog>

        {/* Preview Dialog */}
        <Dialog
          open={previewDialogOpen}
          onClose={() => setPreviewDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Template Preview</DialogTitle>
          <DialogContent>
            {selectedTemplate && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  {selectedTemplate.name}
                </Typography>
                <Chip
                  icon={getTypeIcon(selectedTemplate.type)}
                  label={selectedTemplate.type?.toUpperCase()}
                  color={getTypeColor(selectedTemplate.type)}
                  sx={{ mb: 2 }}
                />
                
                {selectedTemplate.type === 'email' && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2">Subject:</Typography>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace', p: 1, bgcolor: 'grey.100' }}>
                      {selectedTemplate.subject}
                    </Typography>
                  </Box>
                )}
                
                <Typography variant="subtitle2">Content:</Typography>
                <Typography
                  variant="body2"
                  sx={{
                    fontFamily: 'monospace',
                    p: 2,
                    bgcolor: 'grey.100',
                    whiteSpace: 'pre-wrap',
                    maxHeight: 400,
                    overflow: 'auto',
                  }}
                >
                  {selectedTemplate.content}
                </Typography>
                
                <Typography variant="subtitle2" sx={{ mt: 2 }}>
                  Variables: {selectedTemplate.variables?.join(', ') || 'None'}
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPreviewDialogOpen(false)}>Close</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ProtectedRoute>
  );
};

export default CommunicationsPage;
