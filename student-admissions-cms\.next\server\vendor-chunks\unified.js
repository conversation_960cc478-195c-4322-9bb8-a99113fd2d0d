"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unified";
exports.ids = ["vendor-chunks/unified"];
exports.modules = {

/***/ "(ssr)/./node_modules/unified/index.js":
/*!***************************************!*\
  !*** ./node_modules/unified/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar bail = __webpack_require__(/*! bail */ \"(ssr)/./node_modules/bail/index.js\")\nvar buffer = __webpack_require__(/*! is-buffer */ \"(ssr)/./node_modules/is-buffer/index.js\")\nvar extend = __webpack_require__(/*! extend */ \"(ssr)/./node_modules/extend/index.js\")\nvar plain = __webpack_require__(/*! is-plain-obj */ \"(ssr)/./node_modules/is-plain-obj/index.js\")\nvar trough = __webpack_require__(/*! trough */ \"(ssr)/./node_modules/trough/index.js\")\nvar vfile = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/index.js\")\n\n// Expose a frozen processor.\nmodule.exports = unified().freeze()\n\nvar slice = [].slice\nvar own = {}.hasOwnProperty\n\n// Process pipeline.\nvar pipeline = trough()\n  .use(pipelineParse)\n  .use(pipelineRun)\n  .use(pipelineStringify)\n\nfunction pipelineParse(p, ctx) {\n  ctx.tree = p.parse(ctx.file)\n}\n\nfunction pipelineRun(p, ctx, next) {\n  p.run(ctx.tree, ctx.file, done)\n\n  function done(error, tree, file) {\n    if (error) {\n      next(error)\n    } else {\n      ctx.tree = tree\n      ctx.file = file\n      next()\n    }\n  }\n}\n\nfunction pipelineStringify(p, ctx) {\n  var result = p.stringify(ctx.tree, ctx.file)\n\n  if (result === undefined || result === null) {\n    // Empty.\n  } else if (typeof result === 'string' || buffer(result)) {\n    if ('value' in ctx.file) {\n      ctx.file.value = result\n    }\n\n    ctx.file.contents = result\n  } else {\n    ctx.file.result = result\n  }\n}\n\n// Function to create the first processor.\nfunction unified() {\n  var attachers = []\n  var transformers = trough()\n  var namespace = {}\n  var freezeIndex = -1\n  var frozen\n\n  // Data management.\n  processor.data = data\n\n  // Lock.\n  processor.freeze = freeze\n\n  // Plugins.\n  processor.attachers = attachers\n  processor.use = use\n\n  // API.\n  processor.parse = parse\n  processor.stringify = stringify\n  processor.run = run\n  processor.runSync = runSync\n  processor.process = process\n  processor.processSync = processSync\n\n  // Expose.\n  return processor\n\n  // Create a new processor based on the processor in the current scope.\n  function processor() {\n    var destination = unified()\n    var index = -1\n\n    while (++index < attachers.length) {\n      destination.use.apply(null, attachers[index])\n    }\n\n    destination.data(extend(true, {}, namespace))\n\n    return destination\n  }\n\n  // Freeze: used to signal a processor that has finished configuration.\n  //\n  // For example, take unified itself: it’s frozen.\n  // Plugins should not be added to it.\n  // Rather, it should be extended, by invoking it, before modifying it.\n  //\n  // In essence, always invoke this when exporting a processor.\n  function freeze() {\n    var values\n    var transformer\n\n    if (frozen) {\n      return processor\n    }\n\n    while (++freezeIndex < attachers.length) {\n      values = attachers[freezeIndex]\n\n      if (values[1] === false) {\n        continue\n      }\n\n      if (values[1] === true) {\n        values[1] = undefined\n      }\n\n      transformer = values[0].apply(processor, values.slice(1))\n\n      if (typeof transformer === 'function') {\n        transformers.use(transformer)\n      }\n    }\n\n    frozen = true\n    freezeIndex = Infinity\n\n    return processor\n  }\n\n  // Data management.\n  // Getter / setter for processor-specific informtion.\n  function data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', frozen)\n        namespace[key] = value\n        return processor\n      }\n\n      // Get `key`.\n      return (own.call(namespace, key) && namespace[key]) || null\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', frozen)\n      namespace = key\n      return processor\n    }\n\n    // Get space.\n    return namespace\n  }\n\n  // Plugin management.\n  //\n  // Pass it:\n  // *   an attacher and options,\n  // *   a preset,\n  // *   a list of presets, attachers, and arguments (list of attachers and\n  //     options).\n  function use(value) {\n    var settings\n\n    assertUnfrozen('use', frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin.apply(null, arguments)\n    } else if (typeof value === 'object') {\n      if ('length' in value) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new Error('Expected usable value, not `' + value + '`')\n    }\n\n    if (settings) {\n      namespace.settings = extend(namespace.settings || {}, settings)\n    }\n\n    return processor\n\n    function addPreset(result) {\n      addList(result.plugins)\n\n      if (result.settings) {\n        settings = extend(settings || {}, result.settings)\n      }\n    }\n\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value)\n      } else if (typeof value === 'object') {\n        if ('length' in value) {\n          addPlugin.apply(null, value)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new Error('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    function addList(plugins) {\n      var index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (typeof plugins === 'object' && 'length' in plugins) {\n        while (++index < plugins.length) {\n          add(plugins[index])\n        }\n      } else {\n        throw new Error('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    function addPlugin(plugin, value) {\n      var entry = find(plugin)\n\n      if (entry) {\n        if (plain(entry[1]) && plain(value)) {\n          value = extend(true, entry[1], value)\n        }\n\n        entry[1] = value\n      } else {\n        attachers.push(slice.call(arguments))\n      }\n    }\n  }\n\n  function find(plugin) {\n    var index = -1\n\n    while (++index < attachers.length) {\n      if (attachers[index][0] === plugin) {\n        return attachers[index]\n      }\n    }\n  }\n\n  // Parse a file (in string or vfile representation) into a unist node using\n  // the `Parser` on the processor.\n  function parse(doc) {\n    var file = vfile(doc)\n    var Parser\n\n    freeze()\n    Parser = processor.Parser\n    assertParser('parse', Parser)\n\n    if (newable(Parser, 'parse')) {\n      return new Parser(String(file), file).parse()\n    }\n\n    return Parser(String(file), file) // eslint-disable-line new-cap\n  }\n\n  // Run transforms on a unist node representation of a file (in string or\n  // vfile representation), async.\n  function run(node, file, cb) {\n    assertNode(node)\n    freeze()\n\n    if (!cb && typeof file === 'function') {\n      cb = file\n      file = null\n    }\n\n    if (!cb) {\n      return new Promise(executor)\n    }\n\n    executor(null, cb)\n\n    function executor(resolve, reject) {\n      transformers.run(node, vfile(file), done)\n\n      function done(error, tree, file) {\n        tree = tree || node\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(tree)\n        } else {\n          cb(null, tree, file)\n        }\n      }\n    }\n  }\n\n  // Run transforms on a unist node representation of a file (in string or\n  // vfile representation), sync.\n  function runSync(node, file) {\n    var result\n    var complete\n\n    run(node, file, done)\n\n    assertDone('runSync', 'run', complete)\n\n    return result\n\n    function done(error, tree) {\n      complete = true\n      result = tree\n      bail(error)\n    }\n  }\n\n  // Stringify a unist node representation of a file (in string or vfile\n  // representation) into a string using the `Compiler` on the processor.\n  function stringify(node, doc) {\n    var file = vfile(doc)\n    var Compiler\n\n    freeze()\n    Compiler = processor.Compiler\n    assertCompiler('stringify', Compiler)\n    assertNode(node)\n\n    if (newable(Compiler, 'compile')) {\n      return new Compiler(node, file).compile()\n    }\n\n    return Compiler(node, file) // eslint-disable-line new-cap\n  }\n\n  // Parse a file (in string or vfile representation) into a unist node using\n  // the `Parser` on the processor, then run transforms on that node, and\n  // compile the resulting node using the `Compiler` on the processor, and\n  // store that result on the vfile.\n  function process(doc, cb) {\n    freeze()\n    assertParser('process', processor.Parser)\n    assertCompiler('process', processor.Compiler)\n\n    if (!cb) {\n      return new Promise(executor)\n    }\n\n    executor(null, cb)\n\n    function executor(resolve, reject) {\n      var file = vfile(doc)\n\n      pipeline.run(processor, {file: file}, done)\n\n      function done(error) {\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          cb(null, file)\n        }\n      }\n    }\n  }\n\n  // Process the given document (in string or vfile representation), sync.\n  function processSync(doc) {\n    var file\n    var complete\n\n    freeze()\n    assertParser('processSync', processor.Parser)\n    assertCompiler('processSync', processor.Compiler)\n    file = vfile(doc)\n\n    process(file, done)\n\n    assertDone('processSync', 'process', complete)\n\n    return file\n\n    function done(error) {\n      complete = true\n      bail(error)\n    }\n  }\n}\n\n// Check if `value` is a constructor.\nfunction newable(value, name) {\n  return (\n    typeof value === 'function' &&\n    value.prototype &&\n    // A function with keys in its prototype is probably a constructor.\n    // Classes’ prototype methods are not enumerable, so we check if some value\n    // exists in the prototype.\n    (keys(value.prototype) || name in value.prototype)\n  )\n}\n\n// Check if `value` is an object with keys.\nfunction keys(value) {\n  var key\n  for (key in value) {\n    return true\n  }\n\n  return false\n}\n\n// Assert a parser is available.\nfunction assertParser(name, Parser) {\n  if (typeof Parser !== 'function') {\n    throw new Error('Cannot `' + name + '` without `Parser`')\n  }\n}\n\n// Assert a compiler is available.\nfunction assertCompiler(name, Compiler) {\n  if (typeof Compiler !== 'function') {\n    throw new Error('Cannot `' + name + '` without `Compiler`')\n  }\n}\n\n// Assert the processor is not frozen.\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot invoke `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by invoking it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n// Assert `node` is a unist node.\nfunction assertNode(node) {\n  if (!node || typeof node.type !== 'string') {\n    throw new Error('Expected node, got `' + node + '`')\n  }\n}\n\n// Assert that `complete` is `true`.\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unified/index.js\n");

/***/ })

};
;