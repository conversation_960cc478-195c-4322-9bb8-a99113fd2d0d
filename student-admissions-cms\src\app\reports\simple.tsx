'use client';

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Paper,
  Alert,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { mockData } from '@/components/DummyDataProvider';

const SimpleReportsPage: React.FC = () => {
  // Calculate statistics from mock data
  const totalInquiries = mockData.inquiries.length;
  const totalApplications = mockData.applications.length;
  const totalEnrollments = mockData.enrollments.length;
  const totalInterviews = mockData.interviews.length;

  const conversionRate = totalApplications > 0 ? ((totalEnrollments / totalApplications) * 100).toFixed(1) : '0';

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Reports & Analytics Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" gutterBottom>
        Comprehensive analytics and insights for the admissions process.
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <PeopleIcon color="primary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="primary">
                    {totalInquiries}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Inquiries
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AssessmentIcon color="info" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="info.main">
                    {totalApplications}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Applications
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SchoolIcon color="success" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="success.main">
                    {totalEnrollments}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Enrollments
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TrendingUpIcon color="warning" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="warning.main">
                    {conversionRate}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Conversion Rate
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Report Categories */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Application Status Report
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Track application progress and status distribution
            </Typography>
            <Box sx={{ mt: 2 }}>
              {mockData.applications.map((app, index) => (
                <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">{app.application_number}</Typography>
                  <Typography variant="body2" color="primary">
                    {app.status.replace('_', ' ').toUpperCase()}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Program Performance
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Analyze performance by academic program
            </Typography>
            <Box sx={{ mt: 2 }}>
              {mockData.programs.map((program, index) => (
                <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">{program.name}</Typography>
                  <Typography variant="body2" color="success.main">
                    {program.code}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Detailed Analytics */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Inquiry Sources
            </Typography>
            <Box sx={{ mt: 2 }}>
              {mockData.inquiries.map((inquiry, index) => (
                <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">{inquiry.inquiry_source}</Typography>
                  <Typography variant="body2" color="info.main">
                    {inquiry.status}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Interview Status
            </Typography>
            <Box sx={{ mt: 2 }}>
              {mockData.interviews.map((interview, index) => (
                <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">{interview.interview_type}</Typography>
                  <Typography variant="body2" color="warning.main">
                    {interview.status}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Enrollment Summary
            </Typography>
            <Box sx={{ mt: 2 }}>
              {mockData.enrollments.map((enrollment, index) => (
                <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">{enrollment.student_id}</Typography>
                  <Typography variant="body2" color="success.main">
                    {enrollment.status}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Demo Notice */}
      <Alert severity="info" sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          📊 Analytics Dashboard Demo
        </Typography>
        <Typography variant="body2">
          This reports dashboard displays analytics based on dummy data. In a real implementation, this would include:
        </Typography>
        <ul>
          <li>Interactive charts and graphs using Recharts</li>
          <li>Date range filtering and custom report generation</li>
          <li>Export functionality for PDF and Excel reports</li>
          <li>Real-time data updates and trend analysis</li>
          <li>Demographic breakdowns and conversion funnels</li>
          <li>Program-wise performance metrics</li>
        </ul>
      </Alert>
    </Box>
  );
};

export default SimpleReportsPage;
