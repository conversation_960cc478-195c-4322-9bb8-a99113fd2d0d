"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-equals";
exports.ids = ["vendor-chunks/fast-equals"];
exports.modules = {

/***/ "(ssr)/./node_modules/fast-equals/dist/fast-equals.esm.js":
/*!**********************************************************!*\
  !*** ./node_modules/fast-equals/dist/fast-equals.esm.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   circularDeepEqual: () => (/* binding */ circularDeepEqual),\n/* harmony export */   circularShallowEqual: () => (/* binding */ circularShallowEqual),\n/* harmony export */   createCustomEqual: () => (/* binding */ createComparator),\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual),\n/* harmony export */   sameValueZeroEqual: () => (/* binding */ sameValueZeroEqual),\n/* harmony export */   shallowEqual: () => (/* binding */ shallowEqual)\n/* harmony export */ });\nvar HAS_WEAKSET_SUPPORT = typeof WeakSet === 'function';\r\nvar keys = Object.keys;\r\n/**\r\n * are the values passed strictly equal or both NaN\r\n *\r\n * @param a the value to compare against\r\n * @param b the value to test\r\n * @returns are the values equal by the SameValueZero principle\r\n */\r\nfunction sameValueZeroEqual(a, b) {\r\n    return a === b || (a !== a && b !== b);\r\n}\r\n/**\r\n * is the value a plain object\r\n *\r\n * @param value the value to test\r\n * @returns is the value a plain object\r\n */\r\nfunction isPlainObject(value) {\r\n    return value.constructor === Object || value.constructor == null;\r\n}\r\n/**\r\n * is the value promise-like (meaning it is thenable)\r\n *\r\n * @param value the value to test\r\n * @returns is the value promise-like\r\n */\r\nfunction isPromiseLike(value) {\r\n    return !!value && typeof value.then === 'function';\r\n}\r\n/**\r\n * is the value passed a react element\r\n *\r\n * @param value the value to test\r\n * @returns is the value a react element\r\n */\r\nfunction isReactElement(value) {\r\n    return !!(value && value.$$typeof);\r\n}\r\n/**\r\n * in cases where WeakSet is not supported, creates a new custom\r\n * object that mimics the necessary API aspects for cache purposes\r\n *\r\n * @returns the new cache object\r\n */\r\nfunction getNewCacheFallback() {\r\n    var values = [];\r\n    return {\r\n        add: function (value) {\r\n            values.push(value);\r\n        },\r\n        has: function (value) {\r\n            return values.indexOf(value) !== -1;\r\n        },\r\n    };\r\n}\r\n/**\r\n * get a new cache object to prevent circular references\r\n *\r\n * @returns the new cache object\r\n */\r\nvar getNewCache = (function (canUseWeakMap) {\r\n    if (canUseWeakMap) {\r\n        return function _getNewCache() {\r\n            return new WeakSet();\r\n        };\r\n    }\r\n    return getNewCacheFallback;\r\n})(HAS_WEAKSET_SUPPORT);\r\n/**\r\n * create a custom isEqual handler specific to circular objects\r\n *\r\n * @param [isEqual] the isEqual comparator to use instead of isDeepEqual\r\n * @returns the method to create the `isEqual` function\r\n */\r\nfunction createCircularEqualCreator(isEqual) {\r\n    return function createCircularEqual(comparator) {\r\n        var _comparator = isEqual || comparator;\r\n        return function circularEqual(a, b, cache) {\r\n            if (cache === void 0) { cache = getNewCache(); }\r\n            var isCacheableA = !!a && typeof a === 'object';\r\n            var isCacheableB = !!b && typeof b === 'object';\r\n            if (isCacheableA || isCacheableB) {\r\n                var hasA = isCacheableA && cache.has(a);\r\n                var hasB = isCacheableB && cache.has(b);\r\n                if (hasA || hasB) {\r\n                    return hasA && hasB;\r\n                }\r\n                if (isCacheableA) {\r\n                    cache.add(a);\r\n                }\r\n                if (isCacheableB) {\r\n                    cache.add(b);\r\n                }\r\n            }\r\n            return _comparator(a, b, cache);\r\n        };\r\n    };\r\n}\r\n/**\r\n * are the arrays equal in value\r\n *\r\n * @param a the array to test\r\n * @param b the array to test against\r\n * @param isEqual the comparator to determine equality\r\n * @param meta the meta object to pass through\r\n * @returns are the arrays equal\r\n */\r\nfunction areArraysEqual(a, b, isEqual, meta) {\r\n    var index = a.length;\r\n    if (b.length !== index) {\r\n        return false;\r\n    }\r\n    while (index-- > 0) {\r\n        if (!isEqual(a[index], b[index], meta)) {\r\n            return false;\r\n        }\r\n    }\r\n    return true;\r\n}\r\n/**\r\n * are the maps equal in value\r\n *\r\n * @param a the map to test\r\n * @param b the map to test against\r\n * @param isEqual the comparator to determine equality\r\n * @param meta the meta map to pass through\r\n * @returns are the maps equal\r\n */\r\nfunction areMapsEqual(a, b, isEqual, meta) {\r\n    var isValueEqual = a.size === b.size;\r\n    if (isValueEqual && a.size) {\r\n        var matchedIndices_1 = {};\r\n        a.forEach(function (aValue, aKey) {\r\n            if (isValueEqual) {\r\n                var hasMatch_1 = false;\r\n                var matchIndex_1 = 0;\r\n                b.forEach(function (bValue, bKey) {\r\n                    if (!hasMatch_1 && !matchedIndices_1[matchIndex_1]) {\r\n                        hasMatch_1 =\r\n                            isEqual(aKey, bKey, meta) && isEqual(aValue, bValue, meta);\r\n                        if (hasMatch_1) {\r\n                            matchedIndices_1[matchIndex_1] = true;\r\n                        }\r\n                    }\r\n                    matchIndex_1++;\r\n                });\r\n                isValueEqual = hasMatch_1;\r\n            }\r\n        });\r\n    }\r\n    return isValueEqual;\r\n}\r\nvar OWNER = '_owner';\r\nvar hasOwnProperty = Function.prototype.bind.call(Function.prototype.call, Object.prototype.hasOwnProperty);\r\n/**\r\n * are the objects equal in value\r\n *\r\n * @param a the object to test\r\n * @param b the object to test against\r\n * @param isEqual the comparator to determine equality\r\n * @param meta the meta object to pass through\r\n * @returns are the objects equal\r\n */\r\nfunction areObjectsEqual(a, b, isEqual, meta) {\r\n    var keysA = keys(a);\r\n    var index = keysA.length;\r\n    if (keys(b).length !== index) {\r\n        return false;\r\n    }\r\n    if (index) {\r\n        var key = void 0;\r\n        while (index-- > 0) {\r\n            key = keysA[index];\r\n            if (key === OWNER) {\r\n                var reactElementA = isReactElement(a);\r\n                var reactElementB = isReactElement(b);\r\n                if ((reactElementA || reactElementB) &&\r\n                    reactElementA !== reactElementB) {\r\n                    return false;\r\n                }\r\n            }\r\n            if (!hasOwnProperty(b, key) || !isEqual(a[key], b[key], meta)) {\r\n                return false;\r\n            }\r\n        }\r\n    }\r\n    return true;\r\n}\r\n/**\r\n * are the regExps equal in value\r\n *\r\n * @param a the regExp to test\r\n * @param b the regExp to test agains\r\n * @returns are the regExps equal\r\n */\r\nfunction areRegExpsEqual(a, b) {\r\n    return (a.source === b.source &&\r\n        a.global === b.global &&\r\n        a.ignoreCase === b.ignoreCase &&\r\n        a.multiline === b.multiline &&\r\n        a.unicode === b.unicode &&\r\n        a.sticky === b.sticky &&\r\n        a.lastIndex === b.lastIndex);\r\n}\r\n/**\r\n * are the sets equal in value\r\n *\r\n * @param a the set to test\r\n * @param b the set to test against\r\n * @param isEqual the comparator to determine equality\r\n * @param meta the meta set to pass through\r\n * @returns are the sets equal\r\n */\r\nfunction areSetsEqual(a, b, isEqual, meta) {\r\n    var isValueEqual = a.size === b.size;\r\n    if (isValueEqual && a.size) {\r\n        var matchedIndices_2 = {};\r\n        a.forEach(function (aValue) {\r\n            if (isValueEqual) {\r\n                var hasMatch_2 = false;\r\n                var matchIndex_2 = 0;\r\n                b.forEach(function (bValue) {\r\n                    if (!hasMatch_2 && !matchedIndices_2[matchIndex_2]) {\r\n                        hasMatch_2 = isEqual(aValue, bValue, meta);\r\n                        if (hasMatch_2) {\r\n                            matchedIndices_2[matchIndex_2] = true;\r\n                        }\r\n                    }\r\n                    matchIndex_2++;\r\n                });\r\n                isValueEqual = hasMatch_2;\r\n            }\r\n        });\r\n    }\r\n    return isValueEqual;\r\n}\n\nvar HAS_MAP_SUPPORT = typeof Map === 'function';\r\nvar HAS_SET_SUPPORT = typeof Set === 'function';\r\nfunction createComparator(createIsEqual) {\r\n    var isEqual = \r\n    /* eslint-disable no-use-before-define */\r\n    typeof createIsEqual === 'function'\r\n        ? createIsEqual(comparator)\r\n        : comparator;\r\n    /* eslint-enable */\r\n    /**\r\n     * compare the value of the two objects and return true if they are equivalent in values\r\n     *\r\n     * @param a the value to test against\r\n     * @param b the value to test\r\n     * @param [meta] an optional meta object that is passed through to all equality test calls\r\n     * @returns are a and b equivalent in value\r\n     */\r\n    function comparator(a, b, meta) {\r\n        if (a === b) {\r\n            return true;\r\n        }\r\n        if (a && b && typeof a === 'object' && typeof b === 'object') {\r\n            if (isPlainObject(a) && isPlainObject(b)) {\r\n                return areObjectsEqual(a, b, isEqual, meta);\r\n            }\r\n            var aShape = Array.isArray(a);\r\n            var bShape = Array.isArray(b);\r\n            if (aShape || bShape) {\r\n                return aShape === bShape && areArraysEqual(a, b, isEqual, meta);\r\n            }\r\n            aShape = a instanceof Date;\r\n            bShape = b instanceof Date;\r\n            if (aShape || bShape) {\r\n                return (aShape === bShape && sameValueZeroEqual(a.getTime(), b.getTime()));\r\n            }\r\n            aShape = a instanceof RegExp;\r\n            bShape = b instanceof RegExp;\r\n            if (aShape || bShape) {\r\n                return aShape === bShape && areRegExpsEqual(a, b);\r\n            }\r\n            if (isPromiseLike(a) || isPromiseLike(b)) {\r\n                return a === b;\r\n            }\r\n            if (HAS_MAP_SUPPORT) {\r\n                aShape = a instanceof Map;\r\n                bShape = b instanceof Map;\r\n                if (aShape || bShape) {\r\n                    return aShape === bShape && areMapsEqual(a, b, isEqual, meta);\r\n                }\r\n            }\r\n            if (HAS_SET_SUPPORT) {\r\n                aShape = a instanceof Set;\r\n                bShape = b instanceof Set;\r\n                if (aShape || bShape) {\r\n                    return aShape === bShape && areSetsEqual(a, b, isEqual, meta);\r\n                }\r\n            }\r\n            return areObjectsEqual(a, b, isEqual, meta);\r\n        }\r\n        return a !== a && b !== b;\r\n    }\r\n    return comparator;\r\n}\n\nvar deepEqual = createComparator();\r\nvar shallowEqual = createComparator(function () { return sameValueZeroEqual; });\r\nvar circularDeepEqual = createComparator(createCircularEqualCreator());\r\nvar circularShallowEqual = createComparator(createCircularEqualCreator(sameValueZeroEqual));\n\n\n//# sourceMappingURL=fast-equals.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-equals/dist/fast-equals.esm.js\n");

/***/ })

};
;