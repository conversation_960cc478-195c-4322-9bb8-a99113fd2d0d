"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/focusManager.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/focusManager.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n\n\n\nclass FocusManager extends _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor() {\n    super();\n\n    this.setup = onFocus => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const listener = () => onFocus(); // Listen to visibillitychange and focus\n\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n\n      return;\n    };\n  }\n\n  onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  }\n\n  setEventListener(setup) {\n    var _this$cleanup2;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(focused => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n\n  setFocused(focused) {\n    const changed = this.focused !== focused;\n\n    if (changed) {\n      this.focused = focused;\n      this.onFocus();\n    }\n  }\n\n  onFocus() {\n    this.listeners.forEach(({\n      listener\n    }) => {\n      listener();\n    });\n  }\n\n  isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  }\n\n}\nconst focusManager = new FocusManager();\n\n\n//# sourceMappingURL=focusManager.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/focusManager.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/infiniteQueryBehavior.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/infiniteQueryBehavior.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNextPageParam: () => (/* binding */ getNextPageParam),\n/* harmony export */   getPreviousPageParam: () => (/* binding */ getPreviousPageParam),\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\nfunction infiniteQueryBehavior() {\n  return {\n    onFetch: context => {\n      context.fetchFn = () => {\n        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;\n\n        const refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;\n        const fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;\n        const pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;\n        const isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';\n        const isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';\n        const oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];\n        const oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];\n        let newPageParams = oldPageParams;\n        let cancelled = false;\n\n        const addSignalProperty = object => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              var _context$signal;\n\n              if ((_context$signal = context.signal) != null && _context$signal.aborted) {\n                cancelled = true;\n              } else {\n                var _context$signal2;\n\n                (_context$signal2 = context.signal) == null ? void 0 : _context$signal2.addEventListener('abort', () => {\n                  cancelled = true;\n                });\n              }\n\n              return context.signal;\n            }\n          });\n        }; // Get query function\n\n\n        const queryFn = context.options.queryFn || (() => Promise.reject(\"Missing queryFn for queryKey '\" + context.options.queryHash + \"'\"));\n\n        const buildNewPages = (pages, param, page, previous) => {\n          newPageParams = previous ? [param, ...newPageParams] : [...newPageParams, param];\n          return previous ? [page, ...pages] : [...pages, page];\n        }; // Create function to fetch a page\n\n\n        const fetchPage = (pages, manual, param, previous) => {\n          if (cancelled) {\n            return Promise.reject('Cancelled');\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages);\n          }\n\n          const queryFnContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const queryFnResult = queryFn(queryFnContext);\n          const promise = Promise.resolve(queryFnResult).then(page => buildNewPages(pages, param, page, previous));\n          return promise;\n        };\n\n        let promise; // Fetch first page?\n\n        if (!oldPages.length) {\n          promise = fetchPage([]);\n        } // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getNextPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param);\n        } // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getPreviousPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param, true);\n        } // Refetch pages\n        else {\n          newPageParams = [];\n          const manual = typeof context.options.getNextPageParam === 'undefined';\n          const shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page\n\n          promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages\n\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then(pages => {\n              const shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;\n\n              if (shouldFetchNextPage) {\n                const param = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);\n                return fetchPage(pages, manual, param);\n              }\n\n              return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));\n            });\n          }\n        }\n\n        const finalPromise = promise.then(pages => ({\n          pages,\n          pageParams: newPageParams\n        }));\n        return finalPromise;\n      };\n    }\n  };\n}\nfunction getNextPageParam(options, pages) {\n  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);\n}\nfunction getPreviousPageParam(options, pages) {\n  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);\n}\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasNextPage(options, pages) {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages);\n    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;\n  }\n\n  return;\n}\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasPreviousPage(options, pages) {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages);\n    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;\n  }\n\n  return;\n}\n\n\n//# sourceMappingURL=infiniteQueryBehavior.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/infiniteQueryBehavior.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/infiniteQueryObserver.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/infiniteQueryObserver.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfiniteQueryObserver: () => (/* binding */ InfiniteQueryObserver)\n/* harmony export */ });\n/* harmony import */ var _queryObserver_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryObserver.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/queryObserver.mjs\");\n/* harmony import */ var _infiniteQueryBehavior_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./infiniteQueryBehavior.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/infiniteQueryBehavior.mjs\");\n\n\n\nclass InfiniteQueryObserver extends _queryObserver_mjs__WEBPACK_IMPORTED_MODULE_0__.QueryObserver {\n  // Type override\n  // Type override\n  // Type override\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(client, options) {\n    super(client, options);\n  }\n\n  bindMethods() {\n    super.bindMethods();\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  }\n\n  setOptions(options, notifyOptions) {\n    super.setOptions({ ...options,\n      behavior: (0,_infiniteQueryBehavior_mjs__WEBPACK_IMPORTED_MODULE_1__.infiniteQueryBehavior)()\n    }, notifyOptions);\n  }\n\n  getOptimisticResult(options) {\n    options.behavior = (0,_infiniteQueryBehavior_mjs__WEBPACK_IMPORTED_MODULE_1__.infiniteQueryBehavior)();\n    return super.getOptimisticResult(options);\n  }\n\n  fetchNextPage({\n    pageParam,\n    ...options\n  } = {}) {\n    return this.fetch({ ...options,\n      meta: {\n        fetchMore: {\n          direction: 'forward',\n          pageParam\n        }\n      }\n    });\n  }\n\n  fetchPreviousPage({\n    pageParam,\n    ...options\n  } = {}) {\n    return this.fetch({ ...options,\n      meta: {\n        fetchMore: {\n          direction: 'backward',\n          pageParam\n        }\n      }\n    });\n  }\n\n  createResult(query, options) {\n    var _state$fetchMeta, _state$fetchMeta$fetc, _state$fetchMeta2, _state$fetchMeta2$fet, _state$data, _state$data2;\n\n    const {\n      state\n    } = query;\n    const result = super.createResult(query, options);\n    const {\n      isFetching,\n      isRefetching\n    } = result;\n    const isFetchingNextPage = isFetching && ((_state$fetchMeta = state.fetchMeta) == null ? void 0 : (_state$fetchMeta$fetc = _state$fetchMeta.fetchMore) == null ? void 0 : _state$fetchMeta$fetc.direction) === 'forward';\n    const isFetchingPreviousPage = isFetching && ((_state$fetchMeta2 = state.fetchMeta) == null ? void 0 : (_state$fetchMeta2$fet = _state$fetchMeta2.fetchMore) == null ? void 0 : _state$fetchMeta2$fet.direction) === 'backward';\n    return { ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: (0,_infiniteQueryBehavior_mjs__WEBPACK_IMPORTED_MODULE_1__.hasNextPage)(options, (_state$data = state.data) == null ? void 0 : _state$data.pages),\n      hasPreviousPage: (0,_infiniteQueryBehavior_mjs__WEBPACK_IMPORTED_MODULE_1__.hasPreviousPage)(options, (_state$data2 = state.data) == null ? void 0 : _state$data2.pages),\n      isFetchingNextPage,\n      isFetchingPreviousPage,\n      isRefetching: isRefetching && !isFetchingNextPage && !isFetchingPreviousPage\n    };\n  }\n\n}\n\n\n//# sourceMappingURL=infiniteQueryObserver.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/infiniteQueryObserver.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/logger.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/logger.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLogger: () => (/* binding */ defaultLogger)\n/* harmony export */ });\nconst defaultLogger = console;\n\n\n//# sourceMappingURL=logger.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbGliL2xvZ2dlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUV5QjtBQUN6QiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbGliL2xvZ2dlci5tanM/NjAyOSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBkZWZhdWx0TG9nZ2VyID0gY29uc29sZTtcblxuZXhwb3J0IHsgZGVmYXVsdExvZ2dlciB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9nZ2VyLm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/logger.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/mutation.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/mutation.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _logger_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logger.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/logger.mjs\");\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _removable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/removable.mjs\");\n/* harmony import */ var _retryer_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/retryer.mjs\");\n\n\n\n\n\n// CLASS\nclass Mutation extends _removable_mjs__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  constructor(config) {\n    super();\n    this.defaultOptions = config.defaultOptions;\n    this.mutationId = config.mutationId;\n    this.mutationCache = config.mutationCache;\n    this.logger = config.logger || _logger_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultLogger;\n    this.observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n\n  setOptions(options) {\n    this.options = { ...this.defaultOptions,\n      ...options\n    };\n    this.updateCacheTime(this.options.cacheTime);\n  }\n\n  get meta() {\n    return this.options.meta;\n  }\n\n  setState(state) {\n    this.dispatch({\n      type: 'setState',\n      state\n    });\n  }\n\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer); // Stop the mutation from being garbage collected\n\n      this.clearGcTimeout();\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer\n      });\n    }\n  }\n\n  removeObserver(observer) {\n    this.observers = this.observers.filter(x => x !== observer);\n    this.scheduleGc();\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer\n    });\n  }\n\n  optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc();\n      } else {\n        this.mutationCache.remove(this);\n      }\n    }\n  }\n\n  continue() {\n    var _this$retryer$continu, _this$retryer;\n\n    return (_this$retryer$continu = (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.continue()) != null ? _this$retryer$continu : this.execute();\n  }\n\n  async execute() {\n    const executeMutation = () => {\n      var _this$options$retry;\n\n      this.retryer = (0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found');\n          }\n\n          return this.options.mutationFn(this.state.variables);\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({\n            type: 'failed',\n            failureCount,\n            error\n          });\n        },\n        onPause: () => {\n          this.dispatch({\n            type: 'pause'\n          });\n        },\n        onContinue: () => {\n          this.dispatch({\n            type: 'continue'\n          });\n        },\n        retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode\n      });\n      return this.retryer.promise;\n    };\n\n    const restored = this.state.status === 'loading';\n\n    try {\n      var _this$mutationCache$c3, _this$mutationCache$c4, _this$options$onSucce, _this$options2, _this$mutationCache$c5, _this$mutationCache$c6, _this$options$onSettl, _this$options3;\n\n      if (!restored) {\n        var _this$mutationCache$c, _this$mutationCache$c2, _this$options$onMutat, _this$options;\n\n        this.dispatch({\n          type: 'loading',\n          variables: this.options.variables\n        }); // Notify cache callback\n\n        await ((_this$mutationCache$c = (_this$mutationCache$c2 = this.mutationCache.config).onMutate) == null ? void 0 : _this$mutationCache$c.call(_this$mutationCache$c2, this.state.variables, this));\n        const context = await ((_this$options$onMutat = (_this$options = this.options).onMutate) == null ? void 0 : _this$options$onMutat.call(_this$options, this.state.variables));\n\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables\n          });\n        }\n      }\n\n      const data = await executeMutation(); // Notify cache callback\n\n      await ((_this$mutationCache$c3 = (_this$mutationCache$c4 = this.mutationCache.config).onSuccess) == null ? void 0 : _this$mutationCache$c3.call(_this$mutationCache$c4, data, this.state.variables, this.state.context, this));\n      await ((_this$options$onSucce = (_this$options2 = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options2, data, this.state.variables, this.state.context)); // Notify cache callback\n\n      await ((_this$mutationCache$c5 = (_this$mutationCache$c6 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c5.call(_this$mutationCache$c6, data, null, this.state.variables, this.state.context, this));\n      await ((_this$options$onSettl = (_this$options3 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options3, data, null, this.state.variables, this.state.context));\n      this.dispatch({\n        type: 'success',\n        data\n      });\n      return data;\n    } catch (error) {\n      try {\n        var _this$mutationCache$c7, _this$mutationCache$c8, _this$options$onError, _this$options4, _this$mutationCache$c9, _this$mutationCache$c10, _this$options$onSettl2, _this$options5;\n\n        // Notify cache callback\n        await ((_this$mutationCache$c7 = (_this$mutationCache$c8 = this.mutationCache.config).onError) == null ? void 0 : _this$mutationCache$c7.call(_this$mutationCache$c8, error, this.state.variables, this.state.context, this));\n\n        if (true) {\n          this.logger.error(error);\n        }\n\n        await ((_this$options$onError = (_this$options4 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options4, error, this.state.variables, this.state.context)); // Notify cache callback\n\n        await ((_this$mutationCache$c9 = (_this$mutationCache$c10 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c9.call(_this$mutationCache$c10, undefined, error, this.state.variables, this.state.context, this));\n        await ((_this$options$onSettl2 = (_this$options5 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options5, undefined, error, this.state.variables, this.state.context));\n        throw error;\n      } finally {\n        this.dispatch({\n          type: 'error',\n          error: error\n        });\n      }\n    }\n  }\n\n  dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case 'failed':\n          return { ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n\n        case 'pause':\n          return { ...state,\n            isPaused: true\n          };\n\n        case 'continue':\n          return { ...state,\n            isPaused: false\n          };\n\n        case 'loading':\n          return { ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !(0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_2__.canFetch)(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables\n          };\n\n        case 'success':\n          return { ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false\n          };\n\n        case 'error':\n          return { ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error'\n          };\n\n        case 'setState':\n          return { ...state,\n            ...action.state\n          };\n      }\n    };\n\n    this.state = reducer(this.state);\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onMutationUpdate(action);\n      });\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action\n      });\n    });\n  }\n\n}\nfunction getDefaultState() {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined\n  };\n}\n\n\n//# sourceMappingURL=mutation.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/mutation.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/mutationCache.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/mutationCache.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _mutation_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/mutation.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\");\n\n\n\n\n\n// CLASS\nclass MutationCache extends _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config) {\n    super();\n    this.config = config || {};\n    this.mutations = [];\n    this.mutationId = 0;\n  }\n\n  build(client, options, state) {\n    const mutation = new _mutation_mjs__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined\n    });\n    this.add(mutation);\n    return mutation;\n  }\n\n  add(mutation) {\n    this.mutations.push(mutation);\n    this.notify({\n      type: 'added',\n      mutation\n    });\n  }\n\n  remove(mutation) {\n    this.mutations = this.mutations.filter(x => x !== mutation);\n    this.notify({\n      type: 'removed',\n      mutation\n    });\n  }\n\n  clear() {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.mutations.forEach(mutation => {\n        this.remove(mutation);\n      });\n    });\n  }\n\n  getAll() {\n    return this.mutations;\n  }\n\n  find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.mutations.find(mutation => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n  }\n\n  findAll(filters) {\n    return this.mutations.filter(mutation => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n  }\n\n  notify(event) {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(event);\n      });\n    });\n  }\n\n  resumePausedMutations() {\n    var _this$resuming;\n\n    this.resuming = ((_this$resuming = this.resuming) != null ? _this$resuming : Promise.resolve()).then(() => {\n      const pausedMutations = this.mutations.filter(x => x.state.isPaused);\n      return _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => pausedMutations.reduce((promise, mutation) => promise.then(() => mutation.continue().catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.noop)), Promise.resolve()));\n    }).then(() => {\n      this.resuming = undefined;\n    });\n    return this.resuming;\n  }\n\n}\n\n\n//# sourceMappingURL=mutationCache.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/mutationCache.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/mutationObserver.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/mutationObserver.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationObserver: () => (/* binding */ MutationObserver)\n/* harmony export */ });\n/* harmony import */ var _mutation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutation.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/mutation.mjs\");\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n\n\n\n\n\n// CLASS\nclass MutationObserver extends _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(client, options) {\n    super();\n    this.client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.updateResult();\n  }\n\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n\n  setOptions(options) {\n    var _this$currentMutation;\n\n    const prevOptions = this.options;\n    this.options = this.client.defaultMutationOptions(options);\n\n    if (!(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(prevOptions, this.options)) {\n      this.client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.currentMutation,\n        observer: this\n      });\n    }\n\n    (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.setOptions(this.options);\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$currentMutation2;\n\n      (_this$currentMutation2 = this.currentMutation) == null ? void 0 : _this$currentMutation2.removeObserver(this);\n    }\n  }\n\n  onMutationUpdate(action) {\n    this.updateResult(); // Determine which callbacks to trigger\n\n    const notifyOptions = {\n      listeners: true\n    };\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true;\n    }\n\n    this.notify(notifyOptions);\n  }\n\n  getCurrentResult() {\n    return this.currentResult;\n  }\n\n  reset() {\n    this.currentMutation = undefined;\n    this.updateResult();\n    this.notify({\n      listeners: true\n    });\n  }\n\n  mutate(variables, options) {\n    this.mutateOptions = options;\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this);\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, { ...this.options,\n      variables: typeof variables !== 'undefined' ? variables : this.options.variables\n    });\n    this.currentMutation.addObserver(this);\n    return this.currentMutation.execute();\n  }\n\n  updateResult() {\n    const state = this.currentMutation ? this.currentMutation.state : (0,_mutation_mjs__WEBPACK_IMPORTED_MODULE_2__.getDefaultState)();\n    const isLoading = state.status === 'loading';\n    const result = { ...state,\n      isLoading,\n      isPending: isLoading,\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset\n    };\n    this.currentResult = result;\n  }\n\n  notify(options) {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions && this.hasListeners()) {\n        if (options.onSuccess) {\n          var _this$mutateOptions$o, _this$mutateOptions, _this$mutateOptions$o2, _this$mutateOptions2;\n\n          (_this$mutateOptions$o = (_this$mutateOptions = this.mutateOptions).onSuccess) == null ? void 0 : _this$mutateOptions$o.call(_this$mutateOptions, this.currentResult.data, this.currentResult.variables, this.currentResult.context);\n          (_this$mutateOptions$o2 = (_this$mutateOptions2 = this.mutateOptions).onSettled) == null ? void 0 : _this$mutateOptions$o2.call(_this$mutateOptions2, this.currentResult.data, null, this.currentResult.variables, this.currentResult.context);\n        } else if (options.onError) {\n          var _this$mutateOptions$o3, _this$mutateOptions3, _this$mutateOptions$o4, _this$mutateOptions4;\n\n          (_this$mutateOptions$o3 = (_this$mutateOptions3 = this.mutateOptions).onError) == null ? void 0 : _this$mutateOptions$o3.call(_this$mutateOptions3, this.currentResult.error, this.currentResult.variables, this.currentResult.context);\n          (_this$mutateOptions$o4 = (_this$mutateOptions4 = this.mutateOptions).onSettled) == null ? void 0 : _this$mutateOptions$o4.call(_this$mutateOptions4, undefined, this.currentResult.error, this.currentResult.variables, this.currentResult.context);\n        }\n      } // Then trigger the listeners\n\n\n      if (options.listeners) {\n        this.listeners.forEach(({\n          listener\n        }) => {\n          listener(this.currentResult);\n        });\n      }\n    });\n  }\n\n}\n\n\n//# sourceMappingURL=mutationObserver.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbGliL211dGF0aW9uT2JzZXJ2ZXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWlEO0FBQ0c7QUFDRjtBQUNBOztBQUVsRDtBQUNBLCtCQUErQiwyREFBWTtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsU0FBUywrREFBbUI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EseUJBQXlCOztBQUV6QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsK0VBQStFO0FBQy9FO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHNFQUFzRSw4REFBZTtBQUNyRjtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLElBQUksNkRBQWE7QUFDakI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFVBQVU7QUFDVjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxRQUFROzs7QUFHUjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7O0FBRTRCO0FBQzVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcXVlcnktY29yZS9idWlsZC9saWIvbXV0YXRpb25PYnNlcnZlci5tanM/ZDFkMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXREZWZhdWx0U3RhdGUgfSBmcm9tICcuL211dGF0aW9uLm1qcyc7XG5pbXBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSAnLi9ub3RpZnlNYW5hZ2VyLm1qcyc7XG5pbXBvcnQgeyBTdWJzY3JpYmFibGUgfSBmcm9tICcuL3N1YnNjcmliYWJsZS5tanMnO1xuaW1wb3J0IHsgc2hhbGxvd0VxdWFsT2JqZWN0cyB9IGZyb20gJy4vdXRpbHMubWpzJztcblxuLy8gQ0xBU1NcbmNsYXNzIE11dGF0aW9uT2JzZXJ2ZXIgZXh0ZW5kcyBTdWJzY3JpYmFibGUge1xuICBjb25zdHJ1Y3RvcihjbGllbnQsIG9wdGlvbnMpIHtcbiAgICBzdXBlcigpO1xuICAgIHRoaXMuY2xpZW50ID0gY2xpZW50O1xuICAgIHRoaXMuc2V0T3B0aW9ucyhvcHRpb25zKTtcbiAgICB0aGlzLmJpbmRNZXRob2RzKCk7XG4gICAgdGhpcy51cGRhdGVSZXN1bHQoKTtcbiAgfVxuXG4gIGJpbmRNZXRob2RzKCkge1xuICAgIHRoaXMubXV0YXRlID0gdGhpcy5tdXRhdGUuYmluZCh0aGlzKTtcbiAgICB0aGlzLnJlc2V0ID0gdGhpcy5yZXNldC5iaW5kKHRoaXMpO1xuICB9XG5cbiAgc2V0T3B0aW9ucyhvcHRpb25zKSB7XG4gICAgdmFyIF90aGlzJGN1cnJlbnRNdXRhdGlvbjtcblxuICAgIGNvbnN0IHByZXZPcHRpb25zID0gdGhpcy5vcHRpb25zO1xuICAgIHRoaXMub3B0aW9ucyA9IHRoaXMuY2xpZW50LmRlZmF1bHRNdXRhdGlvbk9wdGlvbnMob3B0aW9ucyk7XG5cbiAgICBpZiAoIXNoYWxsb3dFcXVhbE9iamVjdHMocHJldk9wdGlvbnMsIHRoaXMub3B0aW9ucykpIHtcbiAgICAgIHRoaXMuY2xpZW50LmdldE11dGF0aW9uQ2FjaGUoKS5ub3RpZnkoe1xuICAgICAgICB0eXBlOiAnb2JzZXJ2ZXJPcHRpb25zVXBkYXRlZCcsXG4gICAgICAgIG11dGF0aW9uOiB0aGlzLmN1cnJlbnRNdXRhdGlvbixcbiAgICAgICAgb2JzZXJ2ZXI6IHRoaXNcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIChfdGhpcyRjdXJyZW50TXV0YXRpb24gPSB0aGlzLmN1cnJlbnRNdXRhdGlvbikgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzJGN1cnJlbnRNdXRhdGlvbi5zZXRPcHRpb25zKHRoaXMub3B0aW9ucyk7XG4gIH1cblxuICBvblVuc3Vic2NyaWJlKCkge1xuICAgIGlmICghdGhpcy5oYXNMaXN0ZW5lcnMoKSkge1xuICAgICAgdmFyIF90aGlzJGN1cnJlbnRNdXRhdGlvbjI7XG5cbiAgICAgIChfdGhpcyRjdXJyZW50TXV0YXRpb24yID0gdGhpcy5jdXJyZW50TXV0YXRpb24pID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRjdXJyZW50TXV0YXRpb24yLnJlbW92ZU9ic2VydmVyKHRoaXMpO1xuICAgIH1cbiAgfVxuXG4gIG9uTXV0YXRpb25VcGRhdGUoYWN0aW9uKSB7XG4gICAgdGhpcy51cGRhdGVSZXN1bHQoKTsgLy8gRGV0ZXJtaW5lIHdoaWNoIGNhbGxiYWNrcyB0byB0cmlnZ2VyXG5cbiAgICBjb25zdCBub3RpZnlPcHRpb25zID0ge1xuICAgICAgbGlzdGVuZXJzOiB0cnVlXG4gICAgfTtcblxuICAgIGlmIChhY3Rpb24udHlwZSA9PT0gJ3N1Y2Nlc3MnKSB7XG4gICAgICBub3RpZnlPcHRpb25zLm9uU3VjY2VzcyA9IHRydWU7XG4gICAgfSBlbHNlIGlmIChhY3Rpb24udHlwZSA9PT0gJ2Vycm9yJykge1xuICAgICAgbm90aWZ5T3B0aW9ucy5vbkVycm9yID0gdHJ1ZTtcbiAgICB9XG5cbiAgICB0aGlzLm5vdGlmeShub3RpZnlPcHRpb25zKTtcbiAgfVxuXG4gIGdldEN1cnJlbnRSZXN1bHQoKSB7XG4gICAgcmV0dXJuIHRoaXMuY3VycmVudFJlc3VsdDtcbiAgfVxuXG4gIHJlc2V0KCkge1xuICAgIHRoaXMuY3VycmVudE11dGF0aW9uID0gdW5kZWZpbmVkO1xuICAgIHRoaXMudXBkYXRlUmVzdWx0KCk7XG4gICAgdGhpcy5ub3RpZnkoe1xuICAgICAgbGlzdGVuZXJzOiB0cnVlXG4gICAgfSk7XG4gIH1cblxuICBtdXRhdGUodmFyaWFibGVzLCBvcHRpb25zKSB7XG4gICAgdGhpcy5tdXRhdGVPcHRpb25zID0gb3B0aW9ucztcblxuICAgIGlmICh0aGlzLmN1cnJlbnRNdXRhdGlvbikge1xuICAgICAgdGhpcy5jdXJyZW50TXV0YXRpb24ucmVtb3ZlT2JzZXJ2ZXIodGhpcyk7XG4gICAgfVxuXG4gICAgdGhpcy5jdXJyZW50TXV0YXRpb24gPSB0aGlzLmNsaWVudC5nZXRNdXRhdGlvbkNhY2hlKCkuYnVpbGQodGhpcy5jbGllbnQsIHsgLi4udGhpcy5vcHRpb25zLFxuICAgICAgdmFyaWFibGVzOiB0eXBlb2YgdmFyaWFibGVzICE9PSAndW5kZWZpbmVkJyA/IHZhcmlhYmxlcyA6IHRoaXMub3B0aW9ucy52YXJpYWJsZXNcbiAgICB9KTtcbiAgICB0aGlzLmN1cnJlbnRNdXRhdGlvbi5hZGRPYnNlcnZlcih0aGlzKTtcbiAgICByZXR1cm4gdGhpcy5jdXJyZW50TXV0YXRpb24uZXhlY3V0ZSgpO1xuICB9XG5cbiAgdXBkYXRlUmVzdWx0KCkge1xuICAgIGNvbnN0IHN0YXRlID0gdGhpcy5jdXJyZW50TXV0YXRpb24gPyB0aGlzLmN1cnJlbnRNdXRhdGlvbi5zdGF0ZSA6IGdldERlZmF1bHRTdGF0ZSgpO1xuICAgIGNvbnN0IGlzTG9hZGluZyA9IHN0YXRlLnN0YXR1cyA9PT0gJ2xvYWRpbmcnO1xuICAgIGNvbnN0IHJlc3VsdCA9IHsgLi4uc3RhdGUsXG4gICAgICBpc0xvYWRpbmcsXG4gICAgICBpc1BlbmRpbmc6IGlzTG9hZGluZyxcbiAgICAgIGlzU3VjY2Vzczogc3RhdGUuc3RhdHVzID09PSAnc3VjY2VzcycsXG4gICAgICBpc0Vycm9yOiBzdGF0ZS5zdGF0dXMgPT09ICdlcnJvcicsXG4gICAgICBpc0lkbGU6IHN0YXRlLnN0YXR1cyA9PT0gJ2lkbGUnLFxuICAgICAgbXV0YXRlOiB0aGlzLm11dGF0ZSxcbiAgICAgIHJlc2V0OiB0aGlzLnJlc2V0XG4gICAgfTtcbiAgICB0aGlzLmN1cnJlbnRSZXN1bHQgPSByZXN1bHQ7XG4gIH1cblxuICBub3RpZnkob3B0aW9ucykge1xuICAgIG5vdGlmeU1hbmFnZXIuYmF0Y2goKCkgPT4ge1xuICAgICAgLy8gRmlyc3QgdHJpZ2dlciB0aGUgbXV0YXRlIGNhbGxiYWNrc1xuICAgICAgaWYgKHRoaXMubXV0YXRlT3B0aW9ucyAmJiB0aGlzLmhhc0xpc3RlbmVycygpKSB7XG4gICAgICAgIGlmIChvcHRpb25zLm9uU3VjY2Vzcykge1xuICAgICAgICAgIHZhciBfdGhpcyRtdXRhdGVPcHRpb25zJG8sIF90aGlzJG11dGF0ZU9wdGlvbnMsIF90aGlzJG11dGF0ZU9wdGlvbnMkbzIsIF90aGlzJG11dGF0ZU9wdGlvbnMyO1xuXG4gICAgICAgICAgKF90aGlzJG11dGF0ZU9wdGlvbnMkbyA9IChfdGhpcyRtdXRhdGVPcHRpb25zID0gdGhpcy5tdXRhdGVPcHRpb25zKS5vblN1Y2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRtdXRhdGVPcHRpb25zJG8uY2FsbChfdGhpcyRtdXRhdGVPcHRpb25zLCB0aGlzLmN1cnJlbnRSZXN1bHQuZGF0YSwgdGhpcy5jdXJyZW50UmVzdWx0LnZhcmlhYmxlcywgdGhpcy5jdXJyZW50UmVzdWx0LmNvbnRleHQpO1xuICAgICAgICAgIChfdGhpcyRtdXRhdGVPcHRpb25zJG8yID0gKF90aGlzJG11dGF0ZU9wdGlvbnMyID0gdGhpcy5tdXRhdGVPcHRpb25zKS5vblNldHRsZWQpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRtdXRhdGVPcHRpb25zJG8yLmNhbGwoX3RoaXMkbXV0YXRlT3B0aW9uczIsIHRoaXMuY3VycmVudFJlc3VsdC5kYXRhLCBudWxsLCB0aGlzLmN1cnJlbnRSZXN1bHQudmFyaWFibGVzLCB0aGlzLmN1cnJlbnRSZXN1bHQuY29udGV4dCk7XG4gICAgICAgIH0gZWxzZSBpZiAob3B0aW9ucy5vbkVycm9yKSB7XG4gICAgICAgICAgdmFyIF90aGlzJG11dGF0ZU9wdGlvbnMkbzMsIF90aGlzJG11dGF0ZU9wdGlvbnMzLCBfdGhpcyRtdXRhdGVPcHRpb25zJG80LCBfdGhpcyRtdXRhdGVPcHRpb25zNDtcblxuICAgICAgICAgIChfdGhpcyRtdXRhdGVPcHRpb25zJG8zID0gKF90aGlzJG11dGF0ZU9wdGlvbnMzID0gdGhpcy5tdXRhdGVPcHRpb25zKS5vbkVycm9yKSA9PSBudWxsID8gdm9pZCAwIDogX3RoaXMkbXV0YXRlT3B0aW9ucyRvMy5jYWxsKF90aGlzJG11dGF0ZU9wdGlvbnMzLCB0aGlzLmN1cnJlbnRSZXN1bHQuZXJyb3IsIHRoaXMuY3VycmVudFJlc3VsdC52YXJpYWJsZXMsIHRoaXMuY3VycmVudFJlc3VsdC5jb250ZXh0KTtcbiAgICAgICAgICAoX3RoaXMkbXV0YXRlT3B0aW9ucyRvNCA9IChfdGhpcyRtdXRhdGVPcHRpb25zNCA9IHRoaXMubXV0YXRlT3B0aW9ucykub25TZXR0bGVkKSA9PSBudWxsID8gdm9pZCAwIDogX3RoaXMkbXV0YXRlT3B0aW9ucyRvNC5jYWxsKF90aGlzJG11dGF0ZU9wdGlvbnM0LCB1bmRlZmluZWQsIHRoaXMuY3VycmVudFJlc3VsdC5lcnJvciwgdGhpcy5jdXJyZW50UmVzdWx0LnZhcmlhYmxlcywgdGhpcy5jdXJyZW50UmVzdWx0LmNvbnRleHQpO1xuICAgICAgICB9XG4gICAgICB9IC8vIFRoZW4gdHJpZ2dlciB0aGUgbGlzdGVuZXJzXG5cblxuICAgICAgaWYgKG9wdGlvbnMubGlzdGVuZXJzKSB7XG4gICAgICAgIHRoaXMubGlzdGVuZXJzLmZvckVhY2goKHtcbiAgICAgICAgICBsaXN0ZW5lclxuICAgICAgICB9KSA9PiB7XG4gICAgICAgICAgbGlzdGVuZXIodGhpcy5jdXJyZW50UmVzdWx0KTtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG4gIH1cblxufVxuXG5leHBvcnQgeyBNdXRhdGlvbk9ic2VydmVyIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tdXRhdGlvbk9ic2VydmVyLm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/mutationObserver.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n\n\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n\n  let notifyFn = callback => {\n    callback();\n  };\n\n  let batchNotifyFn = callback => {\n    callback();\n  };\n\n  const batch = callback => {\n    let result;\n    transactions++;\n\n    try {\n      result = callback();\n    } finally {\n      transactions--;\n\n      if (!transactions) {\n        flush();\n      }\n    }\n\n    return result;\n  };\n\n  const schedule = callback => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n\n\n  const batchCalls = callback => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args);\n      });\n    };\n  };\n\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n\n    if (originalQueue.length) {\n      (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach(callback => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n\n\n  const setNotifyFunction = fn => {\n    notifyFn = fn;\n  };\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n\n\n  const setBatchNotifyFunction = fn => {\n    batchNotifyFn = fn;\n  };\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction\n  };\n} // SINGLETON\n\nconst notifyManager = createNotifyManager();\n\n\n//# sourceMappingURL=notifyManager.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/onlineManager.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/onlineManager.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n\n\n\nconst onlineEvents = ['online', 'offline'];\nclass OnlineManager extends _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor() {\n    super();\n\n    this.setup = onOnline => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const listener = () => onOnline(); // Listen to online\n\n\n        onlineEvents.forEach(event => {\n          window.addEventListener(event, listener, false);\n        });\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach(event => {\n            window.removeEventListener(event, listener);\n          });\n        };\n      }\n\n      return;\n    };\n  }\n\n  onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  }\n\n  setEventListener(setup) {\n    var _this$cleanup2;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(online => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online);\n      } else {\n        this.onOnline();\n      }\n    });\n  }\n\n  setOnline(online) {\n    const changed = this.online !== online;\n\n    if (changed) {\n      this.online = online;\n      this.onOnline();\n    }\n  }\n\n  onOnline() {\n    this.listeners.forEach(({\n      listener\n    }) => {\n      listener();\n    });\n  }\n\n  isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n\n    return navigator.onLine;\n  }\n\n}\nconst onlineManager = new OnlineManager();\n\n\n//# sourceMappingURL=onlineManager.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/onlineManager.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/query.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/query.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _logger_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logger.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/logger.mjs\");\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _retryer_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./retryer.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/retryer.mjs\");\n/* harmony import */ var _removable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/removable.mjs\");\n\n\n\n\n\n\n// CLASS\nclass Query extends _removable_mjs__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  constructor(config) {\n    super();\n    this.abortSignalConsumed = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.logger = config.logger || _logger_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultLogger;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || getDefaultState(this.options);\n    this.state = this.initialState;\n    this.scheduleGc();\n  }\n\n  get meta() {\n    return this.options.meta;\n  }\n\n  setOptions(options) {\n    this.options = { ...this.defaultOptions,\n      ...options\n    };\n    this.updateCacheTime(this.options.cacheTime);\n  }\n\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this);\n    }\n  }\n\n  setData(newData, options) {\n    const data = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.replaceData)(this.state.data, newData, this.options); // Set data and mark it as cached\n\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt,\n      manual: options == null ? void 0 : options.manual\n    });\n    return data;\n  }\n\n  setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state,\n      setStateOptions\n    });\n  }\n\n  cancel(options) {\n    var _this$retryer;\n\n    const promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.noop).catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.noop) : Promise.resolve();\n  }\n\n  destroy() {\n    super.destroy();\n    this.cancel({\n      silent: true\n    });\n  }\n\n  reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  }\n\n  isActive() {\n    return this.observers.some(observer => observer.options.enabled !== false);\n  }\n\n  isDisabled() {\n    return this.getObserversCount() > 0 && !this.isActive();\n  }\n\n  isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(observer => observer.getCurrentResult().isStale);\n  }\n\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n  }\n\n  onFocus() {\n    var _this$retryer2;\n\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus());\n\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  }\n\n  onOnline() {\n    var _this$retryer3;\n\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect());\n\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  }\n\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer); // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer\n      });\n    }\n  }\n\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter(x => x !== observer);\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n\n        this.scheduleGc();\n      }\n\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer\n      });\n    }\n  }\n\n  getObserversCount() {\n    return this.observers.length;\n  }\n\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  }\n\n  fetch(options, fetchOptions) {\n    var _this$options$behavio, _context$fetchOptions;\n\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions != null && fetchOptions.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn);\n\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n\n    if (true) {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\"As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']\");\n      }\n    }\n\n    const abortController = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.getAbortController)(); // Create query function context\n\n    const queryFnContext = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    }; // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n\n    const addSignalProperty = object => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true;\n            return abortController.signal;\n          }\n\n          return undefined;\n        }\n      });\n    };\n\n    addSignalProperty(queryFnContext); // Create fetch function\n\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\"Missing queryFn for queryKey '\" + this.options.queryHash + \"'\");\n      }\n\n      this.abortSignalConsumed = false;\n      return this.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    (_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch(context); // Store state in case the current fetch needs to be reverted\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (this.state.fetchStatus === 'idle' || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    }\n\n    const onError = error => {\n      // Optimistically update state if needed\n      if (!((0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.isCancelledError)(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error\n        });\n      }\n\n      if (!(0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.isCancelledError)(error)) {\n        var _this$cache$config$on, _this$cache$config, _this$cache$config$on2, _this$cache$config2;\n\n        // Notify cache callback\n        (_this$cache$config$on = (_this$cache$config = this.cache.config).onError) == null ? void 0 : _this$cache$config$on.call(_this$cache$config, error, this);\n        (_this$cache$config$on2 = (_this$cache$config2 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on2.call(_this$cache$config2, this.state.data, error, this);\n\n        if (true) {\n          this.logger.error(error);\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc();\n      }\n\n      this.isFetchingOptimistic = false;\n    }; // Try to fetch the data\n\n\n    this.retryer = (0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.createRetryer)({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : abortController.abort.bind(abortController),\n      onSuccess: data => {\n        var _this$cache$config$on3, _this$cache$config3, _this$cache$config$on4, _this$cache$config4;\n\n        if (typeof data === 'undefined') {\n          if (true) {\n            this.logger.error(\"Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: \" + this.queryHash);\n          }\n\n          onError(new Error(this.queryHash + \" data is undefined\"));\n          return;\n        }\n\n        this.setData(data); // Notify cache callback\n\n        (_this$cache$config$on3 = (_this$cache$config3 = this.cache.config).onSuccess) == null ? void 0 : _this$cache$config$on3.call(_this$cache$config3, data, this);\n        (_this$cache$config$on4 = (_this$cache$config4 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on4.call(_this$cache$config4, data, this.state.error, this);\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc();\n        }\n\n        this.isFetchingOptimistic = false;\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({\n          type: 'failed',\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: () => {\n        this.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  }\n\n  dispatch(action) {\n    const reducer = state => {\n      var _action$meta, _action$dataUpdatedAt;\n\n      switch (action.type) {\n        case 'failed':\n          return { ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n\n        case 'pause':\n          return { ...state,\n            fetchStatus: 'paused'\n          };\n\n        case 'continue':\n          return { ...state,\n            fetchStatus: 'fetching'\n          };\n\n        case 'fetch':\n          return { ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n            fetchStatus: (0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.canFetch)(this.options.networkMode) ? 'fetching' : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading'\n            })\n          };\n\n        case 'success':\n          return { ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            })\n          };\n\n        case 'error':\n          const error = action.error;\n\n          if ((0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.isCancelledError)(error) && error.revert && this.revertState) {\n            return { ...this.revertState,\n              fetchStatus: 'idle'\n            };\n          }\n\n          return { ...state,\n            error: error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error'\n          };\n\n        case 'invalidate':\n          return { ...state,\n            isInvalidated: true\n          };\n\n        case 'setState':\n          return { ...state,\n            ...action.state\n          };\n      }\n    };\n\n    this.state = reducer(this.state);\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate(action);\n      });\n      this.cache.notify({\n        query: this,\n        type: 'updated',\n        action\n      });\n    });\n  }\n\n}\n\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n  const hasData = typeof data !== 'undefined';\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle'\n  };\n}\n\n\n//# sourceMappingURL=query.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/query.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/queryCache.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/queryCache.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _query_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/query.mjs\");\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\");\n\n\n\n\n\n// CLASS\nclass QueryCache extends _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config) {\n    super();\n    this.config = config || {};\n    this.queries = [];\n    this.queriesMap = {};\n  }\n\n  build(client, options, state) {\n    var _options$queryHash;\n\n    const queryKey = options.queryKey;\n    const queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n    let query = this.get(queryHash);\n\n    if (!query) {\n      query = new _query_mjs__WEBPACK_IMPORTED_MODULE_2__.Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n\n    return query;\n  }\n\n  add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'added',\n        query\n      });\n    }\n  }\n\n  remove(query) {\n    const queryInMap = this.queriesMap[query.queryHash];\n\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(x => x !== query);\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n\n      this.notify({\n        type: 'removed',\n        query\n      });\n    }\n  }\n\n  clear() {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        this.remove(query);\n      });\n    });\n  }\n\n  get(queryHash) {\n    return this.queriesMap[queryHash];\n  }\n\n  getAll() {\n    return this.queries;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find(arg1, arg2) {\n    const [filters] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2);\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.queries.find(query => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query));\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(arg1, arg2) {\n    const [filters] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2);\n    return Object.keys(filters).length > 0 ? this.queries.filter(query => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query)) : this.queries;\n  }\n\n  notify(event) {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(event);\n      });\n    });\n  }\n\n  onFocus() {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onFocus();\n      });\n    });\n  }\n\n  onOnline() {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onOnline();\n      });\n    });\n  }\n\n}\n\n\n//# sourceMappingURL=queryCache.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/queryCache.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/queryClient.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/queryClient.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _queryCache_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryCache.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/queryCache.mjs\");\n/* harmony import */ var _mutationCache_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutationCache.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/mutationCache.mjs\");\n/* harmony import */ var _focusManager_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/focusManager.mjs\");\n/* harmony import */ var _onlineManager_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./onlineManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/onlineManager.mjs\");\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _infiniteQueryBehavior_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infiniteQueryBehavior.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/infiniteQueryBehavior.mjs\");\n/* harmony import */ var _logger_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./logger.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/logger.mjs\");\n\n\n\n\n\n\n\n\n\n// CLASS\nclass QueryClient {\n  constructor(config = {}) {\n    this.queryCache = config.queryCache || new _queryCache_mjs__WEBPACK_IMPORTED_MODULE_0__.QueryCache();\n    this.mutationCache = config.mutationCache || new _mutationCache_mjs__WEBPACK_IMPORTED_MODULE_1__.MutationCache();\n    this.logger = config.logger || _logger_mjs__WEBPACK_IMPORTED_MODULE_2__.defaultLogger;\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n    this.mountCount = 0;\n\n    if ( true && config.logger) {\n      this.logger.error(\"Passing a custom logger has been deprecated and will be removed in the next major version.\");\n    }\n  }\n\n  mount() {\n    this.mountCount++;\n    if (this.mountCount !== 1) return;\n    this.unsubscribeFocus = _focusManager_mjs__WEBPACK_IMPORTED_MODULE_3__.focusManager.subscribe(() => {\n      if (_focusManager_mjs__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused()) {\n        this.resumePausedMutations();\n        this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = _onlineManager_mjs__WEBPACK_IMPORTED_MODULE_4__.onlineManager.subscribe(() => {\n      if (_onlineManager_mjs__WEBPACK_IMPORTED_MODULE_4__.onlineManager.isOnline()) {\n        this.resumePausedMutations();\n        this.queryCache.onOnline();\n      }\n    });\n  }\n\n  unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n\n    this.mountCount--;\n    if (this.mountCount !== 0) return;\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    this.unsubscribeFocus = undefined;\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n    this.unsubscribeOnline = undefined;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(arg1, arg2) {\n    const [filters] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2);\n    filters.fetchStatus = 'fetching';\n    return this.queryCache.findAll(filters).length;\n  }\n\n  isMutating(filters) {\n    return this.mutationCache.findAll({ ...filters,\n      fetching: true\n    }).length;\n  }\n\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData(arg1, arg2, arg3) {\n    const parsedOptions = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    const cachedData = this.getQueryData(parsedOptions.queryKey);\n    return cachedData ? Promise.resolve(cachedData) : this.fetchQuery(parsedOptions);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(({\n      queryKey,\n      state\n    }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n\n  setQueryData(queryKey, updater, options) {\n    const query = this.queryCache.find(queryKey);\n    const prevData = query == null ? void 0 : query.state.data;\n    const data = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.functionalUpdate)(updater, prevData);\n\n    if (typeof data === 'undefined') {\n      return undefined;\n    }\n\n    const parsedOptions = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(queryKey);\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(data, { ...options,\n      manual: true\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData(queryKeyOrFilters, updater, options) {\n    return _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => this.getQueryCache().findAll(queryKeyOrFilters).map(({\n      queryKey\n    }) => [queryKey, this.setQueryData(queryKey, updater, options)]));\n  }\n\n  getQueryState(queryKey,\n  /**\n   * @deprecated This filters will be removed in the next major version.\n   */\n  filters) {\n    var _this$queryCache$find2;\n\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(arg1, arg2) {\n    const [filters] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2);\n    const queryCache = this.queryCache;\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        queryCache.remove(query);\n      });\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries(arg1, arg2, arg3) {\n    const [filters, options] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3);\n    const queryCache = this.queryCache;\n    const refetchFilters = {\n      type: 'active',\n      ...filters\n    };\n    return _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        query.reset();\n      });\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(arg1, arg2, arg3) {\n    const [filters, cancelOptions = {}] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3);\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n\n    const promises = _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => this.queryCache.findAll(filters).map(query => query.cancel(cancelOptions)));\n    return Promise.all(promises).then(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries(arg1, arg2, arg3) {\n    const [filters, options] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3);\n    return _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => {\n      var _ref, _filters$refetchType;\n\n      this.queryCache.findAll(filters).forEach(query => {\n        query.invalidate();\n      });\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve();\n      }\n\n      const refetchFilters = { ...filters,\n        type: (_ref = (_filters$refetchType = filters.refetchType) != null ? _filters$refetchType : filters.type) != null ? _ref : 'active'\n      };\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries(arg1, arg2, arg3) {\n    const [filters, options] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3);\n    const promises = _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => this.queryCache.findAll(filters).filter(query => !query.isDisabled()).map(query => {\n      var _options$cancelRefetc;\n\n      return query.fetch(undefined, { ...options,\n        cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,\n        meta: {\n          refetchPage: filters.refetchPage\n        }\n      });\n    }));\n    let promise = Promise.all(promises).then(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop);\n\n    if (!(options != null && options.throwOnError)) {\n      promise = promise.catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop);\n    }\n\n    return promise;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery(arg1, arg2, arg3) {\n    const parsedOptions = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery(arg1, arg2, arg3) {\n    const parsedOptions = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    parsedOptions.behavior = (0,_infiniteQueryBehavior_mjs__WEBPACK_IMPORTED_MODULE_7__.infiniteQueryBehavior)();\n    return this.fetchQuery(parsedOptions);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop);\n  }\n\n  resumePausedMutations() {\n    return this.mutationCache.resumePausedMutations();\n  }\n\n  getQueryCache() {\n    return this.queryCache;\n  }\n\n  getMutationCache() {\n    return this.mutationCache;\n  }\n\n  getLogger() {\n    return this.logger;\n  }\n\n  getDefaultOptions() {\n    return this.defaultOptions;\n  }\n\n  setDefaultOptions(options) {\n    this.defaultOptions = options;\n  }\n\n  setQueryDefaults(queryKey, options) {\n    const result = this.queryDefaults.find(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(queryKey) === (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.queryKey));\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey,\n        defaultOptions: options\n      });\n    }\n  }\n\n  getQueryDefaults(queryKey) {\n    if (!queryKey) {\n      return undefined;\n    } // Get the first matching defaults\n\n\n    const firstMatchingDefaults = this.queryDefaults.find(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(queryKey, x.queryKey)); // Additional checks and error in dev mode\n\n    if (true) {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(queryKey, x.queryKey)); // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\"[QueryClient] Several query defaults match with key '\" + JSON.stringify(queryKey) + \"'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.\");\n      }\n    }\n\n    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;\n  }\n\n  setMutationDefaults(mutationKey, options) {\n    const result = this.mutationDefaults.find(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(mutationKey) === (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.mutationKey));\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey,\n        defaultOptions: options\n      });\n    }\n  }\n\n  getMutationDefaults(mutationKey) {\n    if (!mutationKey) {\n      return undefined;\n    } // Get the first matching defaults\n\n\n    const firstMatchingDefaults = this.mutationDefaults.find(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(mutationKey, x.mutationKey)); // Additional checks and error in dev mode\n\n    if (true) {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(mutationKey, x.mutationKey)); // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\"[QueryClient] Several mutation defaults match with key '\" + JSON.stringify(mutationKey) + \"'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.\");\n      }\n    }\n\n    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;\n  }\n\n  defaultQueryOptions(options) {\n    if (options != null && options._defaulted) {\n      return options;\n    }\n\n    const defaultedOptions = { ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options == null ? void 0 : options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.hashQueryKeyByOptions)(defaultedOptions.queryKey, defaultedOptions);\n    } // dependent default values\n\n\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== 'always';\n    }\n\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense;\n    }\n\n    return defaultedOptions;\n  }\n\n  defaultMutationOptions(options) {\n    if (options != null && options._defaulted) {\n      return options;\n    }\n\n    return { ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options == null ? void 0 : options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n\n  clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  }\n\n}\n\n\n//# sourceMappingURL=queryClient.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/queryClient.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/queryObserver.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/queryObserver.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryObserver: () => (/* binding */ QueryObserver)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _focusManager_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/focusManager.mjs\");\n/* harmony import */ var _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\");\n/* harmony import */ var _retryer_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./retryer.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/retryer.mjs\");\n\n\n\n\n\n\nclass QueryObserver extends _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(client, options) {\n    super();\n    this.client = client;\n    this.options = options;\n    this.trackedProps = new Set();\n    this.selectError = null;\n    this.bindMethods();\n    this.setOptions(options);\n  }\n\n  bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  }\n\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this);\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n\n      this.updateTimers();\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  }\n\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  }\n\n  destroy() {\n    this.listeners = new Set();\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n    this.currentQuery.removeObserver(this);\n  }\n\n  setOptions(options, notifyOptions) {\n    const prevOptions = this.options;\n    const prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryOptions(options);\n\n    if ( true && typeof (options == null ? void 0 : options.isDataEqual) !== 'undefined') {\n      this.client.getLogger().error(\"The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option\");\n    }\n\n    if (!(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this\n      });\n    }\n\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n\n    this.updateQuery();\n    const mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n\n  getOptimisticResult(options) {\n    const query = this.client.getQueryCache().build(this.client, options);\n    const result = this.createResult(query, options);\n\n    if (shouldAssignObserverCurrentProperties(this, result, options)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult everytime\n      // an observer reads an optimistic value.\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.currentResult = result;\n      this.currentResultOptions = this.options;\n      this.currentResultState = this.currentQuery.state;\n    }\n\n    return result;\n  }\n\n  getCurrentResult() {\n    return this.currentResult;\n  }\n\n  trackResult(result) {\n    const trackedResult = {};\n    Object.keys(result).forEach(key => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key);\n          return result[key];\n        }\n      });\n    });\n    return trackedResult;\n  }\n\n  getCurrentQuery() {\n    return this.currentQuery;\n  }\n\n  remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  }\n\n  refetch({\n    refetchPage,\n    ...options\n  } = {}) {\n    return this.fetch({ ...options,\n      meta: {\n        refetchPage\n      }\n    });\n  }\n\n  fetchOptimistic(options) {\n    const defaultedOptions = this.client.defaultQueryOptions(options);\n    const query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    query.isFetchingOptimistic = true;\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n\n  fetch(fetchOptions) {\n    var _fetchOptions$cancelR;\n\n    return this.executeFetch({ ...fetchOptions,\n      cancelRefetch: (_fetchOptions$cancelR = fetchOptions.cancelRefetch) != null ? _fetchOptions$cancelR : true\n    }).then(() => {\n      this.updateResult();\n      return this.currentResult;\n    });\n  }\n\n  executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    let promise = this.currentQuery.fetch(this.options, fetchOptions);\n\n    if (!(fetchOptions != null && fetchOptions.throwOnError)) {\n      promise = promise.catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.noop);\n    }\n\n    return promise;\n  }\n\n  updateStaleTimeout() {\n    this.clearStaleTimeout();\n\n    if (_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isServer || this.currentResult.isStale || !(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isValidTimeout)(this.options.staleTime)) {\n      return;\n    }\n\n    const time = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    const timeout = time + 1;\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n\n  computeRefetchInterval() {\n    var _this$options$refetch;\n\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  }\n\n  updateRefetchInterval(nextInterval) {\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n\n    if (_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isServer || this.options.enabled === false || !(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isValidTimeout)(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || _focusManager_mjs__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused()) {\n        this.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  }\n\n  updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  }\n\n  clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  }\n\n  clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  }\n\n  createResult(query, options) {\n    const prevQuery = this.currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.currentResult;\n    const prevResultState = this.currentResultState;\n    const prevResultOptions = this.currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    const prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    const {\n      state\n    } = query;\n    let {\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      fetchStatus,\n      status\n    } = state;\n    let isPreviousData = false;\n    let isPlaceholderData = false;\n    let data; // Optimistically set result in fetching state if needed\n\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = (0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.canFetch)(query.options.networkMode) ? 'fetching' : 'paused';\n\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle';\n      }\n    } // Keep previous data if needed\n\n\n    if (options.keepPreviousData && !state.dataUpdatedAt && prevQueryResult != null && prevQueryResult.isSuccess && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n        data = this.selectResult;\n      } else {\n        try {\n          this.selectFn = options.select;\n          data = options.select(state.data);\n          data = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.replaceData)(prevResult == null ? void 0 : prevResult.data, data, options);\n          this.selectResult = data;\n          this.selectError = null;\n        } catch (selectError) {\n          if (true) {\n            this.client.getLogger().error(selectError);\n          }\n\n          this.selectError = selectError;\n        }\n      }\n    } // Use query data\n    else {\n      data = state.data;\n    } // Show placeholder data if needed\n\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && status === 'loading') {\n      let placeholderData; // Memoize placeholder data\n\n      if (prevResult != null && prevResult.isPlaceholderData && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n            this.selectError = null;\n          } catch (selectError) {\n            if (true) {\n              this.client.getLogger().error(selectError);\n            }\n\n            this.selectError = selectError;\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.replaceData)(prevResult == null ? void 0 : prevResult.data, placeholderData, options);\n        isPlaceholderData = true;\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n\n    const isFetching = fetchStatus === 'fetching';\n    const isLoading = status === 'loading';\n    const isError = status === 'error';\n    const result = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  }\n\n  updateResult(notifyOptions) {\n    const prevResult = this.currentResult;\n    const nextResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify and update result if something has changed\n\n    if ((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(nextResult, prevResult)) {\n      return;\n    }\n\n    this.currentResult = nextResult; // Determine which callbacks to trigger\n\n    const defaultNotifyOptions = {\n      cache: true\n    };\n\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n\n      const {\n        notifyOnChangeProps\n      } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === 'function' ? notifyOnChangeProps() : notifyOnChangeProps;\n\n      if (notifyOnChangePropsValue === 'all' || !notifyOnChangePropsValue && !this.trackedProps.size) {\n        return true;\n      }\n\n      const includedProps = new Set(notifyOnChangePropsValue != null ? notifyOnChangePropsValue : this.trackedProps);\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error');\n      }\n\n      return Object.keys(this.currentResult).some(key => {\n        const typedKey = key;\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true;\n    }\n\n    this.notify({ ...defaultNotifyOptions,\n      ...notifyOptions\n    });\n  }\n\n  updateQuery() {\n    const query = this.client.getQueryCache().build(this.client, this.options);\n\n    if (query === this.currentQuery) {\n      return;\n    }\n\n    const prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n\n  onQueryUpdate(action) {\n    const notifyOptions = {};\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual;\n    } else if (action.type === 'error' && !(0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.isCancelledError)(action.error)) {\n      notifyOptions.onError = true;\n    }\n\n    this.updateResult(notifyOptions);\n\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  }\n\n  notify(notifyOptions) {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        var _this$options$onSucce, _this$options, _this$options$onSettl, _this$options2;\n\n        (_this$options$onSucce = (_this$options = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options, this.currentResult.data);\n        (_this$options$onSettl = (_this$options2 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options2, this.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        var _this$options$onError, _this$options3, _this$options$onSettl2, _this$options4;\n\n        (_this$options$onError = (_this$options3 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options3, this.currentResult.error);\n        (_this$options$onSettl2 = (_this$options4 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options4, undefined, this.currentResult.error);\n      } // Then trigger the listeners\n\n\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({\n          listener\n        }) => {\n          listener(this.currentResult);\n        });\n      } // Then the cache listeners\n\n\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  }\n\n}\n\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\n\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\n\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n\n  return false;\n}\n\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\n\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n} // this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\n\n\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult, options) {\n  // it is important to keep this condition like this for three reasons:\n  // 1. It will get removed in the v5\n  // 2. it reads: don't update the properties if we want to keep the previous\n  // data.\n  // 3. The opposite condition (!options.keepPreviousData) would fallthrough\n  // and will result in a bad decision\n  if (options.keepPreviousData) {\n    return false;\n  } // this means we want to put some placeholder data when pending and queryKey\n  // changed.\n\n\n  if (options.placeholderData !== undefined) {\n    // re-assign properties only if current data is placeholder data\n    // which means that data did not arrive yet, so, if there is some cached data\n    // we need to \"prepare\" to receive it\n    return optimisticResult.isPlaceholderData;\n  } // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n\n\n  if (!(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  } // basically, just keep previous properties if nothing changed\n\n\n  return false;\n}\n\n\n//# sourceMappingURL=queryObserver.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/queryObserver.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/removable.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/removable.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n\n\nclass Removable {\n  destroy() {\n    this.clearGcTimeout();\n  }\n\n  scheduleGc() {\n    this.clearGcTimeout();\n\n    if ((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.cacheTime);\n    }\n  }\n\n  updateCacheTime(newCacheTime) {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(this.cacheTime || 0, newCacheTime != null ? newCacheTime : _utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1000);\n  }\n\n  clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  }\n\n}\n\n\n//# sourceMappingURL=removable.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbGliL3JlbW92YWJsZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUQ7O0FBRXZEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsUUFBUSwwREFBYztBQUN0QjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHlGQUF5RixnREFBUTtBQUNqRzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRXFCO0FBQ3JCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcXVlcnktY29yZS9idWlsZC9saWIvcmVtb3ZhYmxlLm1qcz8zNzFlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzVmFsaWRUaW1lb3V0LCBpc1NlcnZlciB9IGZyb20gJy4vdXRpbHMubWpzJztcblxuY2xhc3MgUmVtb3ZhYmxlIHtcbiAgZGVzdHJveSgpIHtcbiAgICB0aGlzLmNsZWFyR2NUaW1lb3V0KCk7XG4gIH1cblxuICBzY2hlZHVsZUdjKCkge1xuICAgIHRoaXMuY2xlYXJHY1RpbWVvdXQoKTtcblxuICAgIGlmIChpc1ZhbGlkVGltZW91dCh0aGlzLmNhY2hlVGltZSkpIHtcbiAgICAgIHRoaXMuZ2NUaW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHRoaXMub3B0aW9uYWxSZW1vdmUoKTtcbiAgICAgIH0sIHRoaXMuY2FjaGVUaW1lKTtcbiAgICB9XG4gIH1cblxuICB1cGRhdGVDYWNoZVRpbWUobmV3Q2FjaGVUaW1lKSB7XG4gICAgLy8gRGVmYXVsdCB0byA1IG1pbnV0ZXMgKEluZmluaXR5IGZvciBzZXJ2ZXItc2lkZSkgaWYgbm8gY2FjaGUgdGltZSBpcyBzZXRcbiAgICB0aGlzLmNhY2hlVGltZSA9IE1hdGgubWF4KHRoaXMuY2FjaGVUaW1lIHx8IDAsIG5ld0NhY2hlVGltZSAhPSBudWxsID8gbmV3Q2FjaGVUaW1lIDogaXNTZXJ2ZXIgPyBJbmZpbml0eSA6IDUgKiA2MCAqIDEwMDApO1xuICB9XG5cbiAgY2xlYXJHY1RpbWVvdXQoKSB7XG4gICAgaWYgKHRoaXMuZ2NUaW1lb3V0KSB7XG4gICAgICBjbGVhclRpbWVvdXQodGhpcy5nY1RpbWVvdXQpO1xuICAgICAgdGhpcy5nY1RpbWVvdXQgPSB1bmRlZmluZWQ7XG4gICAgfVxuICB9XG5cbn1cblxuZXhwb3J0IHsgUmVtb3ZhYmxlIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZW1vdmFibGUubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/removable.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/retryer.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/retryer.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./focusManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/focusManager.mjs\");\n/* harmony import */ var _onlineManager_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/onlineManager.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * 2 ** failureCount, 30000);\n}\n\nfunction canFetch(networkMode) {\n  return (networkMode != null ? networkMode : 'online') === 'online' ? _onlineManager_mjs__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nclass CancelledError {\n  constructor(options) {\n    this.revert = options == null ? void 0 : options.revert;\n    this.silent = options == null ? void 0 : options.silent;\n  }\n\n}\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  let promiseResolve;\n  let promiseReject;\n  const promise = new Promise((outerResolve, outerReject) => {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n\n  const cancel = cancelOptions => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort == null ? void 0 : config.abort();\n    }\n  };\n\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n\n  const shouldPause = () => !_focusManager_mjs__WEBPACK_IMPORTED_MODULE_1__.focusManager.isFocused() || config.networkMode !== 'always' && !_onlineManager_mjs__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline();\n\n  const resolve = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n\n  const reject = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n\n  const pause = () => {\n    return new Promise(continueResolve => {\n      continueFn = value => {\n        const canContinue = isResolved || !shouldPause();\n\n        if (canContinue) {\n          continueResolve(value);\n        }\n\n        return canContinue;\n      };\n\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(() => {\n      continueFn = undefined;\n\n      if (!isResolved) {\n        config.onContinue == null ? void 0 : config.onContinue();\n      }\n    });\n  }; // Create loop function\n\n\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return;\n    }\n\n    let promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n\n    Promise.resolve(promiseOrValue).then(resolve).catch(error => {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n\n      const retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      const retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      const delay = typeof retryDelay === 'function' ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === 'number' && failureCount < retry || typeof retry === 'function' && retry(failureCount, error);\n\n      if (isRetryCancelled || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n\n      failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(failureCount, error); // Delay\n\n      (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.sleep)(delay) // Pause if the document is not visible or when the device is offline\n      .then(() => {\n        if (shouldPause()) {\n          return pause();\n        }\n\n        return;\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n\n  if (canFetch(config.networkMode)) {\n    run();\n  } else {\n    pause().then(run);\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn == null ? void 0 : continueFn();\n      return didContinue ? promise : Promise.resolve();\n    },\n    cancelRetry,\n    continueRetry\n  };\n}\n\n\n//# sourceMappingURL=retryer.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbGliL3JldHJ5ZXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBa0Q7QUFDRTtBQUNoQjs7QUFFcEM7QUFDQTtBQUNBOztBQUVBO0FBQ0EsdUVBQXVFLDZEQUFhO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSw2QkFBNkIsMkRBQVksb0RBQW9ELDZEQUFhOztBQUUxRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEtBQUs7OztBQUdMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsd0JBQXdCOztBQUV4QjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxRQUFROzs7QUFHUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHNCQUFzQjs7QUFFdEIsMkVBQTJFOztBQUUzRSxNQUFNLGlEQUFLO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTCxLQUFLOzs7QUFHTDtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBOztBQUVxRTtBQUNyRSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbGliL3JldHJ5ZXIubWpzP2EzYTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZm9jdXNNYW5hZ2VyIH0gZnJvbSAnLi9mb2N1c01hbmFnZXIubWpzJztcbmltcG9ydCB7IG9ubGluZU1hbmFnZXIgfSBmcm9tICcuL29ubGluZU1hbmFnZXIubWpzJztcbmltcG9ydCB7IHNsZWVwIH0gZnJvbSAnLi91dGlscy5tanMnO1xuXG5mdW5jdGlvbiBkZWZhdWx0UmV0cnlEZWxheShmYWlsdXJlQ291bnQpIHtcbiAgcmV0dXJuIE1hdGgubWluKDEwMDAgKiAyICoqIGZhaWx1cmVDb3VudCwgMzAwMDApO1xufVxuXG5mdW5jdGlvbiBjYW5GZXRjaChuZXR3b3JrTW9kZSkge1xuICByZXR1cm4gKG5ldHdvcmtNb2RlICE9IG51bGwgPyBuZXR3b3JrTW9kZSA6ICdvbmxpbmUnKSA9PT0gJ29ubGluZScgPyBvbmxpbmVNYW5hZ2VyLmlzT25saW5lKCkgOiB0cnVlO1xufVxuY2xhc3MgQ2FuY2VsbGVkRXJyb3Ige1xuICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgdGhpcy5yZXZlcnQgPSBvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLnJldmVydDtcbiAgICB0aGlzLnNpbGVudCA9IG9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuc2lsZW50O1xuICB9XG5cbn1cbmZ1bmN0aW9uIGlzQ2FuY2VsbGVkRXJyb3IodmFsdWUpIHtcbiAgcmV0dXJuIHZhbHVlIGluc3RhbmNlb2YgQ2FuY2VsbGVkRXJyb3I7XG59XG5mdW5jdGlvbiBjcmVhdGVSZXRyeWVyKGNvbmZpZykge1xuICBsZXQgaXNSZXRyeUNhbmNlbGxlZCA9IGZhbHNlO1xuICBsZXQgZmFpbHVyZUNvdW50ID0gMDtcbiAgbGV0IGlzUmVzb2x2ZWQgPSBmYWxzZTtcbiAgbGV0IGNvbnRpbnVlRm47XG4gIGxldCBwcm9taXNlUmVzb2x2ZTtcbiAgbGV0IHByb21pc2VSZWplY3Q7XG4gIGNvbnN0IHByb21pc2UgPSBuZXcgUHJvbWlzZSgob3V0ZXJSZXNvbHZlLCBvdXRlclJlamVjdCkgPT4ge1xuICAgIHByb21pc2VSZXNvbHZlID0gb3V0ZXJSZXNvbHZlO1xuICAgIHByb21pc2VSZWplY3QgPSBvdXRlclJlamVjdDtcbiAgfSk7XG5cbiAgY29uc3QgY2FuY2VsID0gY2FuY2VsT3B0aW9ucyA9PiB7XG4gICAgaWYgKCFpc1Jlc29sdmVkKSB7XG4gICAgICByZWplY3QobmV3IENhbmNlbGxlZEVycm9yKGNhbmNlbE9wdGlvbnMpKTtcbiAgICAgIGNvbmZpZy5hYm9ydCA9PSBudWxsID8gdm9pZCAwIDogY29uZmlnLmFib3J0KCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGNhbmNlbFJldHJ5ID0gKCkgPT4ge1xuICAgIGlzUmV0cnlDYW5jZWxsZWQgPSB0cnVlO1xuICB9O1xuXG4gIGNvbnN0IGNvbnRpbnVlUmV0cnkgPSAoKSA9PiB7XG4gICAgaXNSZXRyeUNhbmNlbGxlZCA9IGZhbHNlO1xuICB9O1xuXG4gIGNvbnN0IHNob3VsZFBhdXNlID0gKCkgPT4gIWZvY3VzTWFuYWdlci5pc0ZvY3VzZWQoKSB8fCBjb25maWcubmV0d29ya01vZGUgIT09ICdhbHdheXMnICYmICFvbmxpbmVNYW5hZ2VyLmlzT25saW5lKCk7XG5cbiAgY29uc3QgcmVzb2x2ZSA9IHZhbHVlID0+IHtcbiAgICBpZiAoIWlzUmVzb2x2ZWQpIHtcbiAgICAgIGlzUmVzb2x2ZWQgPSB0cnVlO1xuICAgICAgY29uZmlnLm9uU3VjY2VzcyA9PSBudWxsID8gdm9pZCAwIDogY29uZmlnLm9uU3VjY2Vzcyh2YWx1ZSk7XG4gICAgICBjb250aW51ZUZuID09IG51bGwgPyB2b2lkIDAgOiBjb250aW51ZUZuKCk7XG4gICAgICBwcm9taXNlUmVzb2x2ZSh2YWx1ZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlamVjdCA9IHZhbHVlID0+IHtcbiAgICBpZiAoIWlzUmVzb2x2ZWQpIHtcbiAgICAgIGlzUmVzb2x2ZWQgPSB0cnVlO1xuICAgICAgY29uZmlnLm9uRXJyb3IgPT0gbnVsbCA/IHZvaWQgMCA6IGNvbmZpZy5vbkVycm9yKHZhbHVlKTtcbiAgICAgIGNvbnRpbnVlRm4gPT0gbnVsbCA/IHZvaWQgMCA6IGNvbnRpbnVlRm4oKTtcbiAgICAgIHByb21pc2VSZWplY3QodmFsdWUpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBwYXVzZSA9ICgpID0+IHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoY29udGludWVSZXNvbHZlID0+IHtcbiAgICAgIGNvbnRpbnVlRm4gPSB2YWx1ZSA9PiB7XG4gICAgICAgIGNvbnN0IGNhbkNvbnRpbnVlID0gaXNSZXNvbHZlZCB8fCAhc2hvdWxkUGF1c2UoKTtcblxuICAgICAgICBpZiAoY2FuQ29udGludWUpIHtcbiAgICAgICAgICBjb250aW51ZVJlc29sdmUodmFsdWUpO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGNhbkNvbnRpbnVlO1xuICAgICAgfTtcblxuICAgICAgY29uZmlnLm9uUGF1c2UgPT0gbnVsbCA/IHZvaWQgMCA6IGNvbmZpZy5vblBhdXNlKCk7XG4gICAgfSkudGhlbigoKSA9PiB7XG4gICAgICBjb250aW51ZUZuID0gdW5kZWZpbmVkO1xuXG4gICAgICBpZiAoIWlzUmVzb2x2ZWQpIHtcbiAgICAgICAgY29uZmlnLm9uQ29udGludWUgPT0gbnVsbCA/IHZvaWQgMCA6IGNvbmZpZy5vbkNvbnRpbnVlKCk7XG4gICAgICB9XG4gICAgfSk7XG4gIH07IC8vIENyZWF0ZSBsb29wIGZ1bmN0aW9uXG5cblxuICBjb25zdCBydW4gPSAoKSA9PiB7XG4gICAgLy8gRG8gbm90aGluZyBpZiBhbHJlYWR5IHJlc29sdmVkXG4gICAgaWYgKGlzUmVzb2x2ZWQpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBsZXQgcHJvbWlzZU9yVmFsdWU7IC8vIEV4ZWN1dGUgcXVlcnlcblxuICAgIHRyeSB7XG4gICAgICBwcm9taXNlT3JWYWx1ZSA9IGNvbmZpZy5mbigpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBwcm9taXNlT3JWYWx1ZSA9IFByb21pc2UucmVqZWN0KGVycm9yKTtcbiAgICB9XG5cbiAgICBQcm9taXNlLnJlc29sdmUocHJvbWlzZU9yVmFsdWUpLnRoZW4ocmVzb2x2ZSkuY2F0Y2goZXJyb3IgPT4ge1xuICAgICAgdmFyIF9jb25maWckcmV0cnksIF9jb25maWckcmV0cnlEZWxheTtcblxuICAgICAgLy8gU3RvcCBpZiB0aGUgZmV0Y2ggaXMgYWxyZWFkeSByZXNvbHZlZFxuICAgICAgaWYgKGlzUmVzb2x2ZWQpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfSAvLyBEbyB3ZSBuZWVkIHRvIHJldHJ5IHRoZSByZXF1ZXN0P1xuXG5cbiAgICAgIGNvbnN0IHJldHJ5ID0gKF9jb25maWckcmV0cnkgPSBjb25maWcucmV0cnkpICE9IG51bGwgPyBfY29uZmlnJHJldHJ5IDogMztcbiAgICAgIGNvbnN0IHJldHJ5RGVsYXkgPSAoX2NvbmZpZyRyZXRyeURlbGF5ID0gY29uZmlnLnJldHJ5RGVsYXkpICE9IG51bGwgPyBfY29uZmlnJHJldHJ5RGVsYXkgOiBkZWZhdWx0UmV0cnlEZWxheTtcbiAgICAgIGNvbnN0IGRlbGF5ID0gdHlwZW9mIHJldHJ5RGVsYXkgPT09ICdmdW5jdGlvbicgPyByZXRyeURlbGF5KGZhaWx1cmVDb3VudCwgZXJyb3IpIDogcmV0cnlEZWxheTtcbiAgICAgIGNvbnN0IHNob3VsZFJldHJ5ID0gcmV0cnkgPT09IHRydWUgfHwgdHlwZW9mIHJldHJ5ID09PSAnbnVtYmVyJyAmJiBmYWlsdXJlQ291bnQgPCByZXRyeSB8fCB0eXBlb2YgcmV0cnkgPT09ICdmdW5jdGlvbicgJiYgcmV0cnkoZmFpbHVyZUNvdW50LCBlcnJvcik7XG5cbiAgICAgIGlmIChpc1JldHJ5Q2FuY2VsbGVkIHx8ICFzaG91bGRSZXRyeSkge1xuICAgICAgICAvLyBXZSBhcmUgZG9uZSBpZiB0aGUgcXVlcnkgZG9lcyBub3QgbmVlZCB0byBiZSByZXRyaWVkXG4gICAgICAgIHJlamVjdChlcnJvcik7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgZmFpbHVyZUNvdW50Kys7IC8vIE5vdGlmeSBvbiBmYWlsXG5cbiAgICAgIGNvbmZpZy5vbkZhaWwgPT0gbnVsbCA/IHZvaWQgMCA6IGNvbmZpZy5vbkZhaWwoZmFpbHVyZUNvdW50LCBlcnJvcik7IC8vIERlbGF5XG5cbiAgICAgIHNsZWVwKGRlbGF5KSAvLyBQYXVzZSBpZiB0aGUgZG9jdW1lbnQgaXMgbm90IHZpc2libGUgb3Igd2hlbiB0aGUgZGV2aWNlIGlzIG9mZmxpbmVcbiAgICAgIC50aGVuKCgpID0+IHtcbiAgICAgICAgaWYgKHNob3VsZFBhdXNlKCkpIHtcbiAgICAgICAgICByZXR1cm4gcGF1c2UoKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybjtcbiAgICAgIH0pLnRoZW4oKCkgPT4ge1xuICAgICAgICBpZiAoaXNSZXRyeUNhbmNlbGxlZCkge1xuICAgICAgICAgIHJlamVjdChlcnJvcik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcnVuKCk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH0pO1xuICB9OyAvLyBTdGFydCBsb29wXG5cblxuICBpZiAoY2FuRmV0Y2goY29uZmlnLm5ldHdvcmtNb2RlKSkge1xuICAgIHJ1bigpO1xuICB9IGVsc2Uge1xuICAgIHBhdXNlKCkudGhlbihydW4pO1xuICB9XG5cbiAgcmV0dXJuIHtcbiAgICBwcm9taXNlLFxuICAgIGNhbmNlbCxcbiAgICBjb250aW51ZTogKCkgPT4ge1xuICAgICAgY29uc3QgZGlkQ29udGludWUgPSBjb250aW51ZUZuID09IG51bGwgPyB2b2lkIDAgOiBjb250aW51ZUZuKCk7XG4gICAgICByZXR1cm4gZGlkQ29udGludWUgPyBwcm9taXNlIDogUHJvbWlzZS5yZXNvbHZlKCk7XG4gICAgfSxcbiAgICBjYW5jZWxSZXRyeSxcbiAgICBjb250aW51ZVJldHJ5XG4gIH07XG59XG5cbmV4cG9ydCB7IENhbmNlbGxlZEVycm9yLCBjYW5GZXRjaCwgY3JlYXRlUmV0cnllciwgaXNDYW5jZWxsZWRFcnJvciB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmV0cnllci5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/retryer.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/subscribable.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\nclass Subscribable {\n  constructor() {\n    this.listeners = new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n\n  subscribe(listener) {\n    const identity = {\n      listener\n    };\n    this.listeners.add(identity);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(identity);\n      this.onUnsubscribe();\n    };\n  }\n\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n\n  onSubscribe() {// Do nothing\n  }\n\n  onUnsubscribe() {// Do nothing\n  }\n\n}\n\n\n//# sourceMappingURL=subscribable.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbGliL3N1YnNjcmliYWJsZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsaUJBQWlCO0FBQ2pCOztBQUVBLG1CQUFtQjtBQUNuQjs7QUFFQTs7QUFFd0I7QUFDeEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL2xpYi9zdWJzY3JpYmFibGUubWpzPzdiNmQiXSwic291cmNlc0NvbnRlbnQiOlsiY2xhc3MgU3Vic2NyaWJhYmxlIHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5saXN0ZW5lcnMgPSBuZXcgU2V0KCk7XG4gICAgdGhpcy5zdWJzY3JpYmUgPSB0aGlzLnN1YnNjcmliZS5iaW5kKHRoaXMpO1xuICB9XG5cbiAgc3Vic2NyaWJlKGxpc3RlbmVyKSB7XG4gICAgY29uc3QgaWRlbnRpdHkgPSB7XG4gICAgICBsaXN0ZW5lclxuICAgIH07XG4gICAgdGhpcy5saXN0ZW5lcnMuYWRkKGlkZW50aXR5KTtcbiAgICB0aGlzLm9uU3Vic2NyaWJlKCk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHRoaXMubGlzdGVuZXJzLmRlbGV0ZShpZGVudGl0eSk7XG4gICAgICB0aGlzLm9uVW5zdWJzY3JpYmUoKTtcbiAgICB9O1xuICB9XG5cbiAgaGFzTGlzdGVuZXJzKCkge1xuICAgIHJldHVybiB0aGlzLmxpc3RlbmVycy5zaXplID4gMDtcbiAgfVxuXG4gIG9uU3Vic2NyaWJlKCkgey8vIERvIG5vdGhpbmdcbiAgfVxuXG4gIG9uVW5zdWJzY3JpYmUoKSB7Ly8gRG8gbm90aGluZ1xuICB9XG5cbn1cblxuZXhwb3J0IHsgU3Vic2NyaWJhYmxlIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdWJzY3JpYmFibGUubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/utils.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   difference: () => (/* binding */ difference),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   getAbortController: () => (/* binding */ getAbortController),\n/* harmony export */   hashQueryKey: () => (/* binding */ hashQueryKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isError: () => (/* binding */ isError),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isQueryKey: () => (/* binding */ isQueryKey),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   parseFilterArgs: () => (/* binding */ parseFilterArgs),\n/* harmony export */   parseMutationArgs: () => (/* binding */ parseMutationArgs),\n/* harmony export */   parseMutationFilterArgs: () => (/* binding */ parseMutationFilterArgs),\n/* harmony export */   parseQueryArgs: () => (/* binding */ parseQueryArgs),\n/* harmony export */   partialDeepEqual: () => (/* binding */ partialDeepEqual),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceAt: () => (/* binding */ replaceAt),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   scheduleMicrotask: () => (/* binding */ scheduleMicrotask),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n// TYPES\n// UTILS\nconst isServer = typeof window === 'undefined' || 'Deno' in window;\nfunction noop() {\n  return undefined;\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === 'number' && value >= 0 && value !== Infinity;\n}\nfunction difference(array1, array2) {\n  return array1.filter(x => !array2.includes(x));\n}\nfunction replaceAt(array, index, value) {\n  const copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction parseQueryArgs(arg1, arg2, arg3) {\n  if (!isQueryKey(arg1)) {\n    return arg1;\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3,\n      queryKey: arg1,\n      queryFn: arg2\n    };\n  }\n\n  return { ...arg2,\n    queryKey: arg1\n  };\n}\nfunction parseMutationArgs(arg1, arg2, arg3) {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3,\n        mutationKey: arg1,\n        mutationFn: arg2\n      };\n    }\n\n    return { ...arg2,\n      mutationKey: arg1\n    };\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2,\n      mutationFn: arg1\n    };\n  }\n\n  return { ...arg1\n  };\n}\nfunction parseFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [{ ...arg2,\n    queryKey: arg1\n  }, arg3] : [arg1 || {}, arg2];\n}\nfunction parseMutationFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [{ ...arg2,\n    mutationKey: arg1\n  }, arg3] : [arg1 || {}, arg2];\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive();\n\n    if (type === 'active' && !isActive) {\n      return false;\n    }\n\n    if (type === 'inactive' && isActive) {\n      return false;\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false;\n  }\n\n  if (typeof fetchStatus !== 'undefined' && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const {\n    exact,\n    fetching,\n    predicate,\n    mutationKey\n  } = filters;\n\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n\n    if (exact) {\n      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n\n  if (typeof fetching === 'boolean' && mutation.state.status === 'loading' !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;\n  return hashFn(queryKey);\n}\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\n\nfunction hashQueryKey(queryKey) {\n  return JSON.stringify(queryKey, (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n    result[key] = val[key];\n    return result;\n  }, {}) : val);\n}\n/**\n * Checks if key `b` partially matches with key `a`.\n */\n\nfunction partialMatchKey(a, b) {\n  return partialDeepEqual(a, b);\n}\n/**\n * Checks if `b` partially matches with `a`.\n */\n\nfunction partialDeepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (typeof a !== typeof b) {\n    return false;\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(key => !partialDeepEqual(a[key], b[key]));\n  }\n\n  return false;\n}\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\n\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b);\n\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aSize = array ? a.length : Object.keys(a).length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      copy[key] = replaceEqualDeep(a[key], b[key]);\n\n      if (copy[key] === a[key]) {\n        equalItems++;\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n\n  return b;\n}\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\n\nfunction shallowEqualObjects(a, b) {\n  if (a && !b || b && !a) {\n    return false;\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n} // Copied from: https://github.com/jonschlinkert/is-plain-object\n\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  } // If has modified constructor\n\n\n  const ctor = o.constructor;\n\n  if (typeof ctor === 'undefined') {\n    return true;\n  } // If has modified prototype\n\n\n  const prot = ctor.prototype;\n\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  } // If constructor does not have an Object-specific method\n\n\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false;\n  } // Most likely a plain Object\n\n\n  return true;\n}\n\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isQueryKey(value) {\n  return Array.isArray(value);\n}\nfunction isError(value) {\n  return value instanceof Error;\n}\nfunction sleep(timeout) {\n  return new Promise(resolve => {\n    setTimeout(resolve, timeout);\n  });\n}\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nfunction scheduleMicrotask(callback) {\n  sleep(0).then(callback);\n}\nfunction getAbortController() {\n  if (typeof AbortController === 'function') {\n    return new AbortController();\n  }\n\n  return;\n}\nfunction replaceData(prevData, data, options) {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual != null && options.isDataEqual(prevData, data)) {\n    return prevData;\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data);\n  }\n\n  return data;\n}\n\n\n//# sourceMappingURL=utils.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   defaultContext: () => (/* binding */ defaultContext),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientProvider,defaultContext,useQueryClient auto */ \nconst defaultContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(undefined);\nconst QueryClientSharingContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(false); // If we are given a context, we will use it.\n// Otherwise, if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\nfunction getQueryClientContext(context, contextSharing) {\n    if (context) {\n        return context;\n    }\n    if (contextSharing && \"undefined\" !== \"undefined\") {}\n    return defaultContext;\n}\nconst useQueryClient = ({ context } = {})=>{\n    const queryClient = react__WEBPACK_IMPORTED_MODULE_0__.useContext(getQueryClientContext(context, react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientSharingContext)));\n    if (!queryClient) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return queryClient;\n};\nconst QueryClientProvider = ({ client, children, context, contextSharing = false })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        client.mount();\n        return ()=>{\n            client.unmount();\n        };\n    }, [\n        client\n    ]);\n    if ( true && contextSharing) {\n        client.getLogger().error(\"The contextSharing option has been deprecated and will be removed in the next major version\");\n    }\n    const Context = getQueryClientContext(context, contextSharing);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(QueryClientSharingContext.Provider, {\n        value: !context && contextSharing\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Context.Provider, {\n        value: client\n    }, children));\n};\n //# sourceMappingURL=QueryClientProvider.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryErrorResetBoundary.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/QueryErrorResetBoundary.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ QueryErrorResetBoundary,useQueryErrorResetBoundary auto */ \nfunction createValue() {\n    let isReset = false;\n    return {\n        clearReset: ()=>{\n            isReset = false;\n        },\n        reset: ()=>{\n            isReset = true;\n        },\n        isReset: ()=>{\n            return isReset;\n        }\n    };\n}\nconst QueryErrorResetBoundaryContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(createValue()); // HOOK\nconst useQueryErrorResetBoundary = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryErrorResetBoundaryContext); // COMPONENT\nconst QueryErrorResetBoundary = ({ children })=>{\n    const [value] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>createValue());\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(QueryErrorResetBoundaryContext.Provider, {\n        value: value\n    }, typeof children === \"function\" ? children(value) : children);\n};\n //# sourceMappingURL=QueryErrorResetBoundary.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryErrorResetBoundary.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/errorBoundaryUtils.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/errorBoundaryUtils.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensurePreventErrorBoundaryRetry: () => (/* binding */ ensurePreventErrorBoundaryRetry),\n/* harmony export */   getHasError: () => (/* binding */ getHasError),\n/* harmony export */   useClearResetErrorBoundary: () => (/* binding */ useClearResetErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/utils.mjs\");\n/* __next_internal_client_entry_do_not_use__ ensurePreventErrorBoundaryRetry,getHasError,useClearResetErrorBoundary auto */ \n\nconst ensurePreventErrorBoundaryRetry = (options, errorResetBoundary)=>{\n    if (options.suspense || options.useErrorBoundary) {\n        // Prevent retrying failed query if the error boundary has not been reset yet\n        if (!errorResetBoundary.isReset()) {\n            options.retryOnMount = false;\n        }\n    }\n};\nconst useClearResetErrorBoundary = (errorResetBoundary)=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        errorResetBoundary.clearReset();\n    }, [\n        errorResetBoundary\n    ]);\n};\nconst getHasError = ({ result, errorResetBoundary, useErrorBoundary, query })=>{\n    return result.isError && !errorResetBoundary.isReset() && !result.isFetching && (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.shouldThrowError)(useErrorBoundary, [\n        result.error,\n        query\n    ]);\n};\n //# sourceMappingURL=errorBoundaryUtils.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/errorBoundaryUtils.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/isRestoring.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/isRestoring.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IsRestoringProvider: () => (/* binding */ IsRestoringProvider),\n/* harmony export */   useIsRestoring: () => (/* binding */ useIsRestoring)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ IsRestoringProvider,useIsRestoring auto */ \nconst IsRestoringContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(false);\nconst useIsRestoring = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(IsRestoringContext);\nconst IsRestoringProvider = IsRestoringContext.Provider;\n //# sourceMappingURL=isRestoring.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL2xpYi9pc1Jlc3RvcmluZy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUdBLE1BQUFBLHFCQUFBLGNBQUFDLGdEQUFBO0FBRU8sTUFBQUUsaUJBQUEsSUFBQUYsNkNBQUEsQ0FBQUQ7QUFDTUssTUFBQUEsc0JBQUFBLG1CQUFBQSxRQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi4vLi4vc3JjL2lzUmVzdG9yaW5nLnRzeD84NzZkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmNvbnN0IElzUmVzdG9yaW5nQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoZmFsc2UpXG5cbmV4cG9ydCBjb25zdCB1c2VJc1Jlc3RvcmluZyA9ICgpID0+IFJlYWN0LnVzZUNvbnRleHQoSXNSZXN0b3JpbmdDb250ZXh0KVxuZXhwb3J0IGNvbnN0IElzUmVzdG9yaW5nUHJvdmlkZXIgPSBJc1Jlc3RvcmluZ0NvbnRleHQuUHJvdmlkZXJcbiJdLCJuYW1lcyI6WyJJc1Jlc3RvcmluZ0NvbnRleHQiLCJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VJc1Jlc3RvcmluZyIsInVzZUNvbnRleHQiLCJJc1Jlc3RvcmluZ1Byb3ZpZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/isRestoring.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/suspense.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/suspense.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureStaleTime: () => (/* binding */ ensureStaleTime),\n/* harmony export */   fetchOptimistic: () => (/* binding */ fetchOptimistic),\n/* harmony export */   shouldSuspend: () => (/* binding */ shouldSuspend),\n/* harmony export */   willFetch: () => (/* binding */ willFetch)\n/* harmony export */ });\nconst ensureStaleTime = defaultedOptions => {\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000;\n    }\n  }\n};\nconst willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nconst shouldSuspend = (defaultedOptions, result, isRestoring) => (defaultedOptions == null ? void 0 : defaultedOptions.suspense) && willFetch(result, isRestoring);\nconst fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).then(({\n  data\n}) => {\n  defaultedOptions.onSuccess == null ? void 0 : defaultedOptions.onSuccess(data);\n  defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(data, null);\n}).catch(error => {\n  errorResetBoundary.clearReset();\n  defaultedOptions.onError == null ? void 0 : defaultedOptions.onError(error);\n  defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(undefined, error);\n});\n\n\n//# sourceMappingURL=suspense.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/suspense.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/useBaseQuery.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/useBaseQuery.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _useSyncExternalStore_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useSyncExternalStore.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/useSyncExternalStore.mjs\");\n/* harmony import */ var _QueryErrorResetBoundary_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryErrorResetBoundary.mjs\");\n/* harmony import */ var _QueryClientProvider_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _isRestoring_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isRestoring.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/isRestoring.mjs\");\n/* harmony import */ var _errorBoundaryUtils_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errorBoundaryUtils.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/errorBoundaryUtils.mjs\");\n/* harmony import */ var _suspense_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./suspense.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/suspense.mjs\");\n/* __next_internal_client_entry_do_not_use__ useBaseQuery auto */ \n\n\n\n\n\n\n\nfunction useBaseQuery(options, Observer) {\n    const queryClient = (0,_QueryClientProvider_mjs__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)({\n        context: options.context\n    });\n    const isRestoring = (0,_isRestoring_mjs__WEBPACK_IMPORTED_MODULE_2__.useIsRestoring)();\n    const errorResetBoundary = (0,_QueryErrorResetBoundary_mjs__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)();\n    const defaultedOptions = queryClient.defaultQueryOptions(options); // Make sure results are optimistically set in fetching state before subscribing or updating options\n    defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\"; // Include callbacks in batch renders\n    if (defaultedOptions.onError) {\n        defaultedOptions.onError = _tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batchCalls(defaultedOptions.onError);\n    }\n    if (defaultedOptions.onSuccess) {\n        defaultedOptions.onSuccess = _tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batchCalls(defaultedOptions.onSuccess);\n    }\n    if (defaultedOptions.onSettled) {\n        defaultedOptions.onSettled = _tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batchCalls(defaultedOptions.onSettled);\n    }\n    (0,_suspense_mjs__WEBPACK_IMPORTED_MODULE_5__.ensureStaleTime)(defaultedOptions);\n    (0,_errorBoundaryUtils_mjs__WEBPACK_IMPORTED_MODULE_6__.ensurePreventErrorBoundaryRetry)(defaultedOptions, errorResetBoundary);\n    (0,_errorBoundaryUtils_mjs__WEBPACK_IMPORTED_MODULE_6__.useClearResetErrorBoundary)(errorResetBoundary);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new Observer(queryClient, defaultedOptions));\n    const result = observer.getOptimisticResult(defaultedOptions);\n    (0,_useSyncExternalStore_mjs__WEBPACK_IMPORTED_MODULE_7__.useSyncExternalStore)(react__WEBPACK_IMPORTED_MODULE_0__.useCallback((onStoreChange)=>{\n        const unsubscribe = isRestoring ? ()=>undefined : observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batchCalls(onStoreChange)); // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult();\n        return unsubscribe;\n    }, [\n        observer,\n        isRestoring\n    ]), ()=>observer.getCurrentResult(), ()=>observer.getCurrentResult());\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        // Do not notify on updates because of changes in the options because\n        // these changes should already be reflected in the optimistic result.\n        observer.setOptions(defaultedOptions, {\n            listeners: false\n        });\n    }, [\n        defaultedOptions,\n        observer\n    ]); // Handle suspense\n    if ((0,_suspense_mjs__WEBPACK_IMPORTED_MODULE_5__.shouldSuspend)(defaultedOptions, result, isRestoring)) {\n        throw (0,_suspense_mjs__WEBPACK_IMPORTED_MODULE_5__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary);\n    } // Handle error boundary\n    if ((0,_errorBoundaryUtils_mjs__WEBPACK_IMPORTED_MODULE_6__.getHasError)({\n        result,\n        errorResetBoundary,\n        useErrorBoundary: defaultedOptions.useErrorBoundary,\n        query: observer.getCurrentQuery()\n    })) {\n        throw result.error;\n    } // Handle result property usage tracking\n    return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\n //# sourceMappingURL=useBaseQuery.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL2xpYi91c2VCYXNlUXVlcnkubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBaUJPLFNBQUFBLGFBQUFDLE9BQUEsRUFBQUMsUUFBQTs7O0lBZ0I4Qjs7Ozs7OztJQWVsQzs7O0lBTUE7OztJQU1BOztJQUdEQyx3RkFBK0JBLENBQS9CQSxrQkFBQUE7O0lBSUEsT0FBQUMsU0FBQSxHQUFBQywyQ0FBQSxTQUFBSCxTQUFBSyxhQUFBQztJQVFBLE1BQUFDLFNBQUFMLFNBQUFNLG1CQUFBLENBQUFGO0lBRUFHLCtFQUFvQkEsQ0FBcEJBLDhDQUFBQSxDQUFBQSxDQUFBQTtRQUdNLE1BQUFDLGNBQUFDLGNBQUEsSUFBQUMsWUFBQVYsU0FBQVcsU0FBQSxDQUFBQywrREFBQUEsQ0FBQUMsVUFBQSxDQUFBQyxpQkFBQTtRQUtBO1FBQ0FkLFNBQUFBLFlBQUFBO1FBRUEsT0FBQVE7SUFDRDtRQUFBUjtRQUFBUztLQUFBLE9BQUFULFNBQUFlLGdCQUFBLFFBQUFmLFNBQUFlLGdCQUFBOztRQVFIO1FBQ0E7UUFDQWYsU0FBQUEsVUFBQUEsQ0FBQUEsa0JBQUFBO1lBQXdDZ0IsV0FBQUE7O0lBQ3pDO1FBQUFaO1FBQUFKO0tBQUE7O1FBSUMsTUFBQWlCLDhEQUFBQSxDQUFBYixrQkFBQUosVUFBQWtCO0lBQ0Q7SUFHRCxJQUFBQyxvRUFBQUEsQ0FBQTs7Ozs7SUFDYzs7SUFRYjtJQUdELFFBQUFmLGlCQUFBZ0IsbUJBQUEsR0FBQXBCLFNBQUFxQixXQUFBLENBQUFoQixVQUFBQTtBQUdEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uLi8uLi9zcmMvdXNlQmFzZVF1ZXJ5LnRzPzI4YjUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcblxuaW1wb3J0IHsgbm90aWZ5TWFuYWdlciB9IGZyb20gJ0B0YW5zdGFjay9xdWVyeS1jb3JlJ1xuaW1wb3J0IHsgdXNlU3luY0V4dGVybmFsU3RvcmUgfSBmcm9tICcuL3VzZVN5bmNFeHRlcm5hbFN0b3JlJ1xuaW1wb3J0IHsgdXNlUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkgfSBmcm9tICcuL1F1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5J1xuaW1wb3J0IHsgdXNlUXVlcnlDbGllbnQgfSBmcm9tICcuL1F1ZXJ5Q2xpZW50UHJvdmlkZXInXG5pbXBvcnQgeyB1c2VJc1Jlc3RvcmluZyB9IGZyb20gJy4vaXNSZXN0b3JpbmcnXG5pbXBvcnQge1xuICBlbnN1cmVQcmV2ZW50RXJyb3JCb3VuZGFyeVJldHJ5LFxuICBnZXRIYXNFcnJvcixcbiAgdXNlQ2xlYXJSZXNldEVycm9yQm91bmRhcnksXG59IGZyb20gJy4vZXJyb3JCb3VuZGFyeVV0aWxzJ1xuaW1wb3J0IHsgZW5zdXJlU3RhbGVUaW1lLCBmZXRjaE9wdGltaXN0aWMsIHNob3VsZFN1c3BlbmQgfSBmcm9tICcuL3N1c3BlbnNlJ1xuaW1wb3J0IHR5cGUgeyBRdWVyeUtleSwgUXVlcnlPYnNlcnZlciB9IGZyb20gJ0B0YW5zdGFjay9xdWVyeS1jb3JlJ1xuaW1wb3J0IHR5cGUgeyBVc2VCYXNlUXVlcnlPcHRpb25zIH0gZnJvbSAnLi90eXBlcydcblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUJhc2VRdWVyeTxcbiAgVFF1ZXJ5Rm5EYXRhLFxuICBURXJyb3IsXG4gIFREYXRhLFxuICBUUXVlcnlEYXRhLFxuICBUUXVlcnlLZXkgZXh0ZW5kcyBRdWVyeUtleSxcbj4oXG4gIG9wdGlvbnM6IFVzZUJhc2VRdWVyeU9wdGlvbnM8XG4gICAgVFF1ZXJ5Rm5EYXRhLFxuICAgIFRFcnJvcixcbiAgICBURGF0YSxcbiAgICBUUXVlcnlEYXRhLFxuICAgIFRRdWVyeUtleVxuICA+LFxuICBPYnNlcnZlcjogdHlwZW9mIFF1ZXJ5T2JzZXJ2ZXIsXG4pIHtcbiAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCh7IGNvbnRleHQ6IG9wdGlvbnMuY29udGV4dCB9KVxuICBjb25zdCBpc1Jlc3RvcmluZyA9IHVzZUlzUmVzdG9yaW5nKClcbiAgY29uc3QgZXJyb3JSZXNldEJvdW5kYXJ5ID0gdXNlUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkoKVxuICBjb25zdCBkZWZhdWx0ZWRPcHRpb25zID0gcXVlcnlDbGllbnQuZGVmYXVsdFF1ZXJ5T3B0aW9ucyhvcHRpb25zKVxuXG4gIC8vIE1ha2Ugc3VyZSByZXN1bHRzIGFyZSBvcHRpbWlzdGljYWxseSBzZXQgaW4gZmV0Y2hpbmcgc3RhdGUgYmVmb3JlIHN1YnNjcmliaW5nIG9yIHVwZGF0aW5nIG9wdGlvbnNcbiAgZGVmYXVsdGVkT3B0aW9ucy5fb3B0aW1pc3RpY1Jlc3VsdHMgPSBpc1Jlc3RvcmluZ1xuICAgID8gJ2lzUmVzdG9yaW5nJ1xuICAgIDogJ29wdGltaXN0aWMnXG5cbiAgLy8gSW5jbHVkZSBjYWxsYmFja3MgaW4gYmF0Y2ggcmVuZGVyc1xuICBpZiAoZGVmYXVsdGVkT3B0aW9ucy5vbkVycm9yKSB7XG4gICAgZGVmYXVsdGVkT3B0aW9ucy5vbkVycm9yID0gbm90aWZ5TWFuYWdlci5iYXRjaENhbGxzKFxuICAgICAgZGVmYXVsdGVkT3B0aW9ucy5vbkVycm9yLFxuICAgIClcbiAgfVxuXG4gIGlmIChkZWZhdWx0ZWRPcHRpb25zLm9uU3VjY2Vzcykge1xuICAgIGRlZmF1bHRlZE9wdGlvbnMub25TdWNjZXNzID0gbm90aWZ5TWFuYWdlci5iYXRjaENhbGxzKFxuICAgICAgZGVmYXVsdGVkT3B0aW9ucy5vblN1Y2Nlc3MsXG4gICAgKVxuICB9XG5cbiAgaWYgKGRlZmF1bHRlZE9wdGlvbnMub25TZXR0bGVkKSB7XG4gICAgZGVmYXVsdGVkT3B0aW9ucy5vblNldHRsZWQgPSBub3RpZnlNYW5hZ2VyLmJhdGNoQ2FsbHMoXG4gICAgICBkZWZhdWx0ZWRPcHRpb25zLm9uU2V0dGxlZCxcbiAgICApXG4gIH1cblxuICBlbnN1cmVTdGFsZVRpbWUoZGVmYXVsdGVkT3B0aW9ucylcbiAgZW5zdXJlUHJldmVudEVycm9yQm91bmRhcnlSZXRyeShkZWZhdWx0ZWRPcHRpb25zLCBlcnJvclJlc2V0Qm91bmRhcnkpXG5cbiAgdXNlQ2xlYXJSZXNldEVycm9yQm91bmRhcnkoZXJyb3JSZXNldEJvdW5kYXJ5KVxuXG4gIGNvbnN0IFtvYnNlcnZlcl0gPSBSZWFjdC51c2VTdGF0ZShcbiAgICAoKSA9PlxuICAgICAgbmV3IE9ic2VydmVyPFRRdWVyeUZuRGF0YSwgVEVycm9yLCBURGF0YSwgVFF1ZXJ5RGF0YSwgVFF1ZXJ5S2V5PihcbiAgICAgICAgcXVlcnlDbGllbnQsXG4gICAgICAgIGRlZmF1bHRlZE9wdGlvbnMsXG4gICAgICApLFxuICApXG5cbiAgY29uc3QgcmVzdWx0ID0gb2JzZXJ2ZXIuZ2V0T3B0aW1pc3RpY1Jlc3VsdChkZWZhdWx0ZWRPcHRpb25zKVxuXG4gIHVzZVN5bmNFeHRlcm5hbFN0b3JlKFxuICAgIFJlYWN0LnVzZUNhbGxiYWNrKFxuICAgICAgKG9uU3RvcmVDaGFuZ2UpID0+IHtcbiAgICAgICAgY29uc3QgdW5zdWJzY3JpYmUgPSBpc1Jlc3RvcmluZ1xuICAgICAgICAgID8gKCkgPT4gdW5kZWZpbmVkXG4gICAgICAgICAgOiBvYnNlcnZlci5zdWJzY3JpYmUobm90aWZ5TWFuYWdlci5iYXRjaENhbGxzKG9uU3RvcmVDaGFuZ2UpKVxuXG4gICAgICAgIC8vIFVwZGF0ZSByZXN1bHQgdG8gbWFrZSBzdXJlIHdlIGRpZCBub3QgbWlzcyBhbnkgcXVlcnkgdXBkYXRlc1xuICAgICAgICAvLyBiZXR3ZWVuIGNyZWF0aW5nIHRoZSBvYnNlcnZlciBhbmQgc3Vic2NyaWJpbmcgdG8gaXQuXG4gICAgICAgIG9ic2VydmVyLnVwZGF0ZVJlc3VsdCgpXG5cbiAgICAgICAgcmV0dXJuIHVuc3Vic2NyaWJlXG4gICAgICB9LFxuICAgICAgW29ic2VydmVyLCBpc1Jlc3RvcmluZ10sXG4gICAgKSxcbiAgICAoKSA9PiBvYnNlcnZlci5nZXRDdXJyZW50UmVzdWx0KCksXG4gICAgKCkgPT4gb2JzZXJ2ZXIuZ2V0Q3VycmVudFJlc3VsdCgpLFxuICApXG5cbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBEbyBub3Qgbm90aWZ5IG9uIHVwZGF0ZXMgYmVjYXVzZSBvZiBjaGFuZ2VzIGluIHRoZSBvcHRpb25zIGJlY2F1c2VcbiAgICAvLyB0aGVzZSBjaGFuZ2VzIHNob3VsZCBhbHJlYWR5IGJlIHJlZmxlY3RlZCBpbiB0aGUgb3B0aW1pc3RpYyByZXN1bHQuXG4gICAgb2JzZXJ2ZXIuc2V0T3B0aW9ucyhkZWZhdWx0ZWRPcHRpb25zLCB7IGxpc3RlbmVyczogZmFsc2UgfSlcbiAgfSwgW2RlZmF1bHRlZE9wdGlvbnMsIG9ic2VydmVyXSlcblxuICAvLyBIYW5kbGUgc3VzcGVuc2VcbiAgaWYgKHNob3VsZFN1c3BlbmQoZGVmYXVsdGVkT3B0aW9ucywgcmVzdWx0LCBpc1Jlc3RvcmluZykpIHtcbiAgICB0aHJvdyBmZXRjaE9wdGltaXN0aWMoZGVmYXVsdGVkT3B0aW9ucywgb2JzZXJ2ZXIsIGVycm9yUmVzZXRCb3VuZGFyeSlcbiAgfVxuXG4gIC8vIEhhbmRsZSBlcnJvciBib3VuZGFyeVxuICBpZiAoXG4gICAgZ2V0SGFzRXJyb3Ioe1xuICAgICAgcmVzdWx0LFxuICAgICAgZXJyb3JSZXNldEJvdW5kYXJ5LFxuICAgICAgdXNlRXJyb3JCb3VuZGFyeTogZGVmYXVsdGVkT3B0aW9ucy51c2VFcnJvckJvdW5kYXJ5LFxuICAgICAgcXVlcnk6IG9ic2VydmVyLmdldEN1cnJlbnRRdWVyeSgpLFxuICAgIH0pXG4gICkge1xuICAgIHRocm93IHJlc3VsdC5lcnJvclxuICB9XG5cbiAgLy8gSGFuZGxlIHJlc3VsdCBwcm9wZXJ0eSB1c2FnZSB0cmFja2luZ1xuICByZXR1cm4gIWRlZmF1bHRlZE9wdGlvbnMubm90aWZ5T25DaGFuZ2VQcm9wc1xuICAgID8gb2JzZXJ2ZXIudHJhY2tSZXN1bHQocmVzdWx0KVxuICAgIDogcmVzdWx0XG59XG4iXSwibmFtZXMiOlsidXNlQmFzZVF1ZXJ5Iiwib3B0aW9ucyIsIk9ic2VydmVyIiwiZW5zdXJlUHJldmVudEVycm9yQm91bmRhcnlSZXRyeSIsIm9ic2VydmVyIiwiUmVhY3QiLCJ1c2VTdGF0ZSIsInF1ZXJ5Q2xpZW50IiwiZGVmYXVsdGVkT3B0aW9ucyIsInJlc3VsdCIsImdldE9wdGltaXN0aWNSZXN1bHQiLCJ1c2VTeW5jRXh0ZXJuYWxTdG9yZSIsInVuc3Vic2NyaWJlIiwiaXNSZXN0b3JpbmciLCJ1bmRlZmluZWQiLCJzdWJzY3JpYmUiLCJub3RpZnlNYW5hZ2VyIiwiYmF0Y2hDYWxscyIsIm9uU3RvcmVDaGFuZ2UiLCJnZXRDdXJyZW50UmVzdWx0IiwibGlzdGVuZXJzIiwiZmV0Y2hPcHRpbWlzdGljIiwiZXJyb3JSZXNldEJvdW5kYXJ5IiwiZ2V0SGFzRXJyb3IiLCJub3RpZnlPbkNoYW5nZVByb3BzIiwidHJhY2tSZXN1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/useBaseQuery.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/useInfiniteQuery.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/useInfiniteQuery.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInfiniteQuery: () => (/* binding */ useInfiniteQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/infiniteQueryObserver.mjs\");\n/* harmony import */ var _useBaseQuery_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/useBaseQuery.mjs\");\n/* __next_internal_client_entry_do_not_use__ useInfiniteQuery auto */ \n\nfunction useInfiniteQuery(arg1, arg2, arg3) {\n    const options = (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__.parseQueryArgs)(arg1, arg2, arg3);\n    return (0,_useBaseQuery_mjs__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)(options, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__.InfiniteQueryObserver);\n}\n //# sourceMappingURL=useInfiniteQuery.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/useInfiniteQuery.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/useMutation.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMutation: () => (/* binding */ useMutation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/mutationObserver.mjs\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _useSyncExternalStore_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useSyncExternalStore.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/useSyncExternalStore.mjs\");\n/* harmony import */ var _QueryClientProvider_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryClientProvider.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/utils.mjs\");\n/* __next_internal_client_entry_do_not_use__ useMutation auto */ \n\n\n\n\nfunction useMutation(arg1, arg2, arg3) {\n    const options = (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.parseMutationArgs)(arg1, arg2, arg3);\n    const queryClient = (0,_QueryClientProvider_mjs__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)({\n        context: options.context\n    });\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new _tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__.MutationObserver(queryClient, options));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        observer.setOptions(options);\n    }, [\n        observer,\n        options\n    ]);\n    const result = (0,_useSyncExternalStore_mjs__WEBPACK_IMPORTED_MODULE_4__.useSyncExternalStore)(react__WEBPACK_IMPORTED_MODULE_0__.useCallback((onStoreChange)=>observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batchCalls(onStoreChange)), [\n        observer\n    ]), ()=>observer.getCurrentResult(), ()=>observer.getCurrentResult());\n    const mutate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((variables, mutateOptions)=>{\n        observer.mutate(variables, mutateOptions).catch(noop);\n    }, [\n        observer\n    ]);\n    if (result.error && (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.shouldThrowError)(observer.options.useErrorBoundary, [\n        result.error\n    ])) {\n        throw result.error;\n    }\n    return {\n        ...result,\n        mutate,\n        mutateAsync: result.mutate\n    };\n} // eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n //# sourceMappingURL=useMutation.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/useQuery.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/queryObserver.mjs\");\n/* harmony import */ var _useBaseQuery_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/useBaseQuery.mjs\");\n/* __next_internal_client_entry_do_not_use__ useQuery auto */ \n\nfunction useQuery(arg1, arg2, arg3) {\n    const parsedOptions = (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__.parseQueryArgs)(arg1, arg2, arg3);\n    return (0,_useBaseQuery_mjs__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)(parsedOptions, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__.QueryObserver);\n}\n //# sourceMappingURL=useQuery.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/useSyncExternalStore.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/useSyncExternalStore.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSyncExternalStore: () => (/* binding */ useSyncExternalStore)\n/* harmony export */ });\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n/* __next_internal_client_entry_do_not_use__ useSyncExternalStore auto */ \nconst useSyncExternalStore = use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore;\n //# sourceMappingURL=useSyncExternalStore.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL2xpYi91c2VTeW5jRXh0ZXJuYWxTdG9yZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBSU8sTUFBQUEsdUJBQUFDLHVGQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi4vLi4vc3JjL3VzZVN5bmNFeHRlcm5hbFN0b3JlLnRzP2UwZDkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG4vLyBUZW1wb3Jhcnkgd29ya2Fyb3VuZCBkdWUgdG8gYW4gaXNzdWUgd2l0aCByZWFjdC1uYXRpdmUgdVNFUyAtIGh0dHBzOi8vZ2l0aHViLmNvbS9UYW5TdGFjay9xdWVyeS9wdWxsLzM2MDFcbmltcG9ydCB7IHVzZVN5bmNFeHRlcm5hbFN0b3JlIGFzIHVTRVMgfSBmcm9tICd1c2Utc3luYy1leHRlcm5hbC1zdG9yZS9zaGltL2luZGV4LmpzJ1xuXG5leHBvcnQgY29uc3QgdXNlU3luY0V4dGVybmFsU3RvcmUgPSB1U0VTXG4iXSwibmFtZXMiOlsidXNlU3luY0V4dGVybmFsU3RvcmUiLCJ1c2VTeW5jRXh0ZXJuYWxTdG9yZSQxIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/useSyncExternalStore.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/utils.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/utils.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError)\n/* harmony export */ });\nfunction shouldThrowError(_useErrorBoundary, params) {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params);\n  }\n\n  return !!_useErrorBoundary;\n}\n\n\n//# sourceMappingURL=utils.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL2xpYi91dGlscy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFNEI7QUFDNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9yZWFjdC1xdWVyeS9idWlsZC9saWIvdXRpbHMubWpzPzY5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gc2hvdWxkVGhyb3dFcnJvcihfdXNlRXJyb3JCb3VuZGFyeSwgcGFyYW1zKSB7XG4gIC8vIEFsbG93IHVzZUVycm9yQm91bmRhcnkgZnVuY3Rpb24gdG8gb3ZlcnJpZGUgdGhyb3dpbmcgYmVoYXZpb3Igb24gYSBwZXItZXJyb3IgYmFzaXNcbiAgaWYgKHR5cGVvZiBfdXNlRXJyb3JCb3VuZGFyeSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIHJldHVybiBfdXNlRXJyb3JCb3VuZGFyeSguLi5wYXJhbXMpO1xuICB9XG5cbiAgcmV0dXJuICEhX3VzZUVycm9yQm91bmRhcnk7XG59XG5cbmV4cG9ydCB7IHNob3VsZFRocm93RXJyb3IgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWxzLm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/utils.mjs\n");

/***/ })

};
;